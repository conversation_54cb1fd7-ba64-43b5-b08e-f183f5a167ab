-- =============================================
-- 再保险系统数据库DDL语句
-- 生成时间：2025-07-08
-- 目标数据库：StarRocks 3.1
-- 字符集：utf8
-- 基于设计文档：docs/design/program_design.md 2.3章节
-- =============================================

-- 设置字符集
SET NAMES utf8;

-- =============================================
-- TB0001: 保单登记再保产品信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_product` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `TransactionNo` varchar(64) NOT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) NOT NULL COMMENT '保险机构代码,唯一固定值000166',
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险合同号码',
  `ReInsuranceContName` varchar(256) NOT NULL COMMENT '再保险合同名称',
  `ReInsuranceContTitle` varchar(256) NOT NULL COMMENT '再保险合同简称',
  `MainReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险附约主合同号',
  `ContOrAmendmentType` varchar(4) NOT NULL COMMENT '合同附约类型（1=主合同,2=附约）',
  `ProductCode` varchar(64) NOT NULL COMMENT '产品编码',
  `ProductName` varchar(128) NOT NULL COMMENT '产品名称',
  `GPFlag` varchar(4) NOT NULL COMMENT '团个性质（01=个险,02=团险,99=其他）',
  `ProductType` varchar(64) NOT NULL COMMENT '险类代码',
  `LiabilityCode` varchar(64) NOT NULL COMMENT '责任代码',
  `LiabilityName` varchar(128) NOT NULL COMMENT '责任名称',
  `ReinsurerCode` varchar(64) NOT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) NOT NULL COMMENT '再保险公司名称',
  `ReinsuranceShare` varchar(32) NOT NULL COMMENT '再保人参与份额比例',
  `ReinsurMode` varchar(4) NOT NULL COMMENT '分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）',
  `ReInsuranceType` varchar(4) NOT NULL COMMENT '再保类型（01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔）',
  `TermType` varchar(4) NOT NULL COMMENT '保险期限类型（10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知）',
  `RetentionAmount` varchar(32) NOT NULL COMMENT '自留额',
  `RetentionPercentage` varchar(32) NOT NULL COMMENT '自留比例',
  `QuotaSharePercentage` varchar(32) NOT NULL COMMENT '分保比例',
  `ReportYear` int(11) NOT NULL COMMENT '所属年份',
  `ReportMonth` tinyint(4) NOT NULL COMMENT '所属月份',
  `AccountPeriod` varchar(64) NOT NULL COMMENT '所属账期',
  `DataSource` tinyint(4) NOT NULL COMMENT '数据来源（0=系统,1=人工）',
  `PushStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态（0=未推送,1=已推送）',
  `PushDate` date DEFAULT NULL COMMENT '推送日期',
  `PushBy` varchar(64) DEFAULT NULL COMMENT '推送人',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `IsDel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除, 0:否, 1:是',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CreateBy` varchar(64) DEFAULT NULL COMMENT '创建者',
  `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `UpdateBy` varchar(64) DEFAULT NULL COMMENT '更新者'
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保产品信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0002: 保单登记再保合同信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_insure_cont` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `TransactionNo` varchar(64) NOT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) NOT NULL COMMENT '保险机构代码,唯一固定值000166',
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险合同号码',
  `ReInsuranceContName` varchar(256) NOT NULL COMMENT '再保险合同名称',
  `ReInsuranceContTitle` varchar(256) NOT NULL COMMENT '再保险合同简称',
  `MainReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险附约主合同号',
  `ContOrAmendmentType` varchar(4) NOT NULL COMMENT '合同附约类型（1=主合同,2=附约）',
  `ContAttribute` varchar(4) NOT NULL COMMENT '合同属性（1=保险合同,2=混合合同,3=非保险合同）',
  `ContStatus` varchar(4) NOT NULL COMMENT '合同状态（1=有效,2=终止）',
  `TreatyOrFacultativeFlag` varchar(4) NOT NULL COMMENT '合同/临分标志（0=否,1=是）',
  `ContSigndate` date NOT NULL COMMENT '合同签署日期',
  `PeriodFrom` date NOT NULL COMMENT '合同生效起期',
  `PeriodTo` date NOT NULL COMMENT '合同生效止期',
  `ContType` varchar(4) NOT NULL COMMENT '合同类型（1=比例合同,2=非比例合同）',
  `ReinsurerCode` varchar(64) NOT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) NOT NULL COMMENT '再保险公司名称',
  `ChargeType` varchar(4) NOT NULL COMMENT '佣金核算方式（1=业务年度，2=财务年度）',
  `ReportYear` int(11) NOT NULL COMMENT '所属年份',
  `ReportMonth` tinyint(4) NOT NULL COMMENT '所属月份',
  `AccountPeriod` varchar(64) NOT NULL COMMENT '所属账期',
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保合同信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0003: 保单登记再保账单信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_account` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `TransactionNo` varchar(64) DEFAULT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) DEFAULT NULL COMMENT '保险机构代码,唯一固定值000166',
  `AccountID` varchar(64) DEFAULT NULL COMMENT '账单编号',
  `AccountingPeriodfrom` date DEFAULT NULL COMMENT '账单起期',
  `AccountingPeriodto` date DEFAULT NULL COMMENT '账单止期',
  `ReinsurerCode` varchar(64) DEFAULT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) DEFAULT NULL COMMENT '再保险公司名称',
  `ReInsuranceContNo` varchar(64) DEFAULT NULL COMMENT '再保险合同号码',
  `ReInsuranceContName` varchar(256) DEFAULT NULL COMMENT '再保险合同名称',
  `ReinsurancePremium` decimal(20,2) DEFAULT '0.00' COMMENT '分保费',
  `ReinsuranceCommssionRate` decimal(20,4) DEFAULT '0.0000' COMMENT '分保佣金率',
  `ReinsuranceCommssion` decimal(20,2) DEFAULT '0.00' COMMENT '分保佣金',
  `ReturnReinsurancePremium` decimal(20,2) DEFAULT '0.00' COMMENT '退回分保费',
  `ReturnReinsuranceCommssion` decimal(20,2) DEFAULT '0.00' COMMENT '退回分保佣金',
  `ReturnSurrenderPay` decimal(20,2) DEFAULT '0.00' COMMENT '摊回退保金',
  `ReturnClaimPay` decimal(20,2) DEFAULT '0.00' COMMENT '摊回理赔款',
  `ReturnMaturity` decimal(20,2) DEFAULT '0.00' COMMENT '摊回满期金',
  `ReturnAnnuity` decimal(20,2) DEFAULT '0.00' COMMENT '摊回年金',
  `ReturnLivBene` decimal(20,2) DEFAULT '0.00' COMMENT '摊回生存金',
  `AccountStatus` varchar(4) DEFAULT NULL COMMENT '账单状态（1=有效,2=无效）',
  `PairingStatus` varchar(4) DEFAULT NULL COMMENT '结算状态（1=未结算,2=已结算）',
  `PairingDate` date DEFAULT NULL COMMENT '结算日期',
  `Currency` varchar(4) DEFAULT NULL COMMENT '货币代码',
  `CurrentRate` decimal(20,6) DEFAULT NULL COMMENT '结算汇率',
  `SettleBillNo` varchar(64) DEFAULT NULL COMMENT '结算账单号',
  `ContImportStatus` tinyint(4) DEFAULT NULL COMMENT '首续期明细导入状态（0=未导入,1=已导入,2=不需要导入）',
  `ContImportRemark` varchar(128) DEFAULT NULL COMMENT '首续期明细导入描述',
  `EdorImportStatus` tinyint(4) DEFAULT NULL COMMENT '保全明细导入状态（0=未导入,1=已导入,2=不需要导入）',
  `EdorImportRemark` varchar(128) DEFAULT NULL COMMENT '保全明细导入描述',
  `ClaimImportStatus` tinyint(4) DEFAULT NULL COMMENT '理赔明细导入状态（0=未导入,1=已导入,2=不需要导入）',
  `ClaimImportRemark` varchar(128) DEFAULT NULL COMMENT '理赔明细导入描述',
  `BenefitImportStatus` tinyint(4) DEFAULT NULL COMMENT '生存金明细导入状态（0=未导入,1=已导入,2=不需要导入）',
  `BenefitImportRemark` varchar(128) DEFAULT NULL COMMENT '生存金明细导入描述',
  `ReportYear` int(11) NOT NULL COMMENT '所属年份',
  `ReportMonth` tinyint(4) NOT NULL COMMENT '所属月份',
  `AccountPeriod` varchar(64) NOT NULL COMMENT '所属账期',
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保账单信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0004: 保单登记再保首续期险种明细表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_cont` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `TransactionNo` varchar(64) DEFAULT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) NOT NULL COMMENT '保险机构代码,唯一固定值000166',
  `GrpPolicyNo` varchar(64) DEFAULT NULL COMMENT '团体保单号',
  `GrpProductNo` varchar(64) DEFAULT NULL COMMENT '团单保险险种号码',
  `PolicyNo` varchar(64) NOT NULL COMMENT '个人保单号',
  `ProductNo` varchar(64) NOT NULL COMMENT '个单保险险种号码',
  `GPFlag` varchar(4) NOT NULL COMMENT '团个性质（01=个险,02=团险,99=其他）',
  `MainProductNo` varchar(64) NOT NULL COMMENT '主险保险险种号码',
  `MainProductFlag` varchar(4) NOT NULL COMMENT '主附险性质代码（1=主险,2=附加险,3=不区分）',
  `ProductCode` varchar(64) NOT NULL COMMENT '产品编码',
  `LiabilityCode` varchar(64) NOT NULL COMMENT '责任代码',
  `LiabilityName` varchar(128) NOT NULL COMMENT '责任名称',
  `Classification` varchar(64) DEFAULT NULL COMMENT '责任分类代码',
  `EventType` varchar(4) NOT NULL COMMENT '业务类型(01=新单,02=续期,03=续保)',
  `PolYear` tinyint(4) NOT NULL COMMENT '保单年度',
  `RenewalTimes` tinyint(4) DEFAULT NULL COMMENT '续期续保次数(保单年度-1)',
  `TermType` varchar(4) DEFAULT NULL COMMENT '保险期限类型',
  `ManageCom` varchar(64) NOT NULL COMMENT '管理机构代码',
  `SignDate` date NOT NULL COMMENT '签单日期',
  `EffDate` date NOT NULL COMMENT '保险责任生效日期',
  `InvalidDate` date NOT NULL COMMENT '保险责任终止日期',
  `UWConclusion` varchar(64) DEFAULT NULL COMMENT '核保结论代码',
  `PolStatus` varchar(4) NOT NULL COMMENT '保单状态代码',
  `Status` varchar(4) NOT NULL COMMENT '保单险种状态代码',
  `BasicSumInsured` decimal(20,2) DEFAULT NULL COMMENT '基本保额',
  `RiskAmnt` decimal(20,2) DEFAULT NULL COMMENT '风险保额',
  `Premium` decimal(20,2) DEFAULT NULL COMMENT '保费',
  `AccountValue` decimal(20,2) DEFAULT NULL COMMENT '保险账户价值',
  `FacultativeFlag` varchar(64) NOT NULL COMMENT '临分标记（0=否,1=是）',
  `AnonymousFlag` varchar(4) DEFAULT NULL COMMENT '无名单标志',
  `WaiverFlag` varchar(4) DEFAULT NULL COMMENT '豁免险标志（0=否,1=是）',
  `WaiverPrem` decimal(20,2) DEFAULT NULL COMMENT '所需豁免剩余保费',
  `FinalCashValue` decimal(20,2) DEFAULT NULL COMMENT '期末现金价值',
  `FinalLiabilityReserve` decimal(20,2) DEFAULT NULL COMMENT '期末责任准备金',
  `InsuredNo` varchar(64) NOT NULL COMMENT '被保人客户号',
  `InsuredName` varchar(64) NOT NULL COMMENT '被保人姓名',
  `InsuredSex` varchar(4) NOT NULL COMMENT '被保人性别',
  `InsuredCertType` varchar(4) NOT NULL COMMENT '被保人证件类型',
  `InsuredCertNo` varchar(64) NOT NULL COMMENT '被保人证件编码',
  `OccupationType` varchar(12) NOT NULL COMMENT '职业代码',
  `AppntAge` tinyint(4) DEFAULT NULL COMMENT '投保年龄',
  `PreAge` tinyint(4) DEFAULT NULL COMMENT '当前年龄',
  `ProfessionalFee` decimal(20,2) DEFAULT NULL COMMENT '职业加费金额',
  `SubStandardFee` decimal(20,2) DEFAULT NULL COMMENT '次标准体加费金额',
  `EMRate` decimal(10,4) DEFAULT NULL COMMENT 'EM加点',
  `ProjectFlag` varchar(4) DEFAULT NULL COMMENT '建工险标志',
  `InsurePeoples` tinyint(4) DEFAULT NULL COMMENT '被保人数',
  `SaparateFlag` varchar(4) NOT NULL COMMENT '分出标记（0=未达到溢额线保单,1=分出保单）',
  `ReInsuranceContNo` varchar(64) NOT NULL COMMENT '再保险合同号码',
  `ReinsurerCode` varchar(64) NOT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) NOT NULL COMMENT '再保险公司名称',
  `ReinsurMode` varchar(4) NOT NULL COMMENT '分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）',
  `RetentionAmount` decimal(20,2) DEFAULT NULL COMMENT '自留额',
  `ReinsuranceAmnt` decimal(20,2) DEFAULT NULL COMMENT '分保保额',
  `QuotaSharePercentage` varchar(32) DEFAULT NULL COMMENT '分保比例',
  `ReinsurancePremium` decimal(20,2) DEFAULT NULL COMMENT '分保费',
  `ReinsuranceCommssion` decimal(20,2) DEFAULT NULL COMMENT '分保佣金',
  `ReComputationsDate` date DEFAULT NULL COMMENT '分保计算日期',
  `AccountGetDate` date DEFAULT NULL COMMENT '账单归属日期',
  `Currency` varchar(4) NOT NULL COMMENT '货币代码',
  `AccTransNo` varchar(64) NOT NULL COMMENT '所属账单流水号',
  `DataSource` tinyint(4) NOT NULL COMMENT "数据来源（0=系统,1=人工）",
  `PushStatus` tinyint(4) NOT NULL DEFAULT "0" COMMENT "推送状态（0=未推送,1=已推送）",
  `PushDate` date NULL COMMENT "推送日期",
  `PushBy` varchar(64) NULL COMMENT "推送人",
  `Remark` varchar(128) NULL COMMENT "备注",
  `IsDel` tinyint(4) NOT NULL DEFAULT "0" COMMENT "是否删除(0=未删除,1=已删除)",
  `CreateBy` varchar(64) NULL COMMENT "创建人",
  `CreateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
  `UpdateBy` varchar(64) NULL COMMENT "修改人",
  `UpdateTime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "修改时间"
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保首续期险种明细表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0005: 保单登记再保保全变更信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_edor` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `TransactionNo` varchar(64) DEFAULT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) DEFAULT NULL COMMENT '保险机构代码,唯一固定值000166',
  `GrpPolicyNo` varchar(64) NOT NULL COMMENT '团体保单号',
  `GrpProductNo` varchar(64) NOT NULL COMMENT '团单保险险种号码',
  `PolicyNo` varchar(64) DEFAULT NULL COMMENT '个人保单号',
  `ProductNo` varchar(64) DEFAULT NULL COMMENT '个单保险险种号码',
  `GPFlag` varchar(4) DEFAULT NULL COMMENT '团个性质（01=个险,02=团险,99=其他）',
  `MainProductNo` varchar(64) DEFAULT NULL COMMENT '主险保险险种号码',
  `MainProductFlag` varchar(4) DEFAULT NULL COMMENT '主附险性质代码（1=主险,2=附加险,3=不区分）',
  `ProductCode` varchar(64) DEFAULT NULL COMMENT '产品编码',
  `LiabilityCode` varchar(64) DEFAULT NULL COMMENT '责任代码',
  `LiabilityName` varchar(128) DEFAULT NULL COMMENT '责任名称',
  `Classification` varchar(64) NOT NULL COMMENT '责任分类代码',
  `TermType` varchar(4) NOT NULL COMMENT '保险期限类型',
  `ManageCom` varchar(64) NOT NULL COMMENT '管理机构代码',
  `PolYear` tinyint(4) DEFAULT NULL COMMENT '保单年度',
  `SignDate` date DEFAULT NULL COMMENT '签单日期',
  `EffDate` date DEFAULT NULL COMMENT '保险责任生效日期',
  `InvalidDate` date DEFAULT NULL COMMENT '保险责任终止日期',
  `UWConclusion` varchar(64) NOT NULL COMMENT '核保结论代码',
  `PolStatus` varchar(4) DEFAULT NULL COMMENT '保单状态代码',
  `Status` varchar(4) DEFAULT NULL COMMENT '保单险种状态代码',
  `BasicSumInsured` decimal(20,2) NOT NULL COMMENT '基本保额',
  `RiskAmnt` decimal(20,2) NOT NULL COMMENT '风险保额',
  `Premium` decimal(20,2) NOT NULL COMMENT '保费',
  `AccountValue` decimal(20,2) NOT NULL COMMENT '保险账户价值',
  `FacultativeFlag` varchar(64) DEFAULT NULL COMMENT '临分标记（0=否,1=是）',
  `AnonymousFlag` varchar(4) NOT NULL COMMENT '无名单标志',
  `WaiverFlag` varchar(4) NOT NULL COMMENT '豁免险标志（0=否,1=是）',
  `WaiverPrem` decimal(20,2) NOT NULL COMMENT '所需豁免剩余保费',
  `FinalCashValue` decimal(20,2) NOT NULL COMMENT '期末现金价值',
  `FinalLiabilityReserve` decimal(20,2) NOT NULL COMMENT '期末责任准备金',
  `InsurePeoples` tinyint(4) NOT NULL COMMENT '被保人数',
  `InsuredNo` varchar(64) DEFAULT NULL COMMENT '被保人客户号',
  `InsuredName` varchar(64) DEFAULT NULL COMMENT '被保人姓名',
  `InsuredSex` varchar(4) DEFAULT NULL COMMENT '被保人性别',
  `InsuredCertType` varchar(4) DEFAULT NULL COMMENT '被保人证件类型',
  `InsuredCertNo` varchar(64) DEFAULT NULL COMMENT '被保人证件编码',
  `OccupationType` varchar(12) DEFAULT NULL COMMENT '职业代码',
  `AppntAge` tinyint(4) NOT NULL COMMENT '投保年龄',
  `PreAge` tinyint(4) NOT NULL COMMENT '当前年龄',
  `ProfessionalFee` decimal(20,2) NOT NULL COMMENT '职业加费金额',
  `SubStandardFee` decimal(20,2) NOT NULL COMMENT '次标准体加费金额',
  `EMRate` decimal(10,4) NOT NULL COMMENT 'EM加点',
  `ProjectFlag` varchar(4) NOT NULL COMMENT '建工险标志',
  `EndorAcceptNo` varchar(64) NOT NULL COMMENT '保全受理号码',
  `EndorsementNo` varchar(64) NOT NULL COMMENT '保全批单号码',
  `EdorType` varchar(4) NOT NULL COMMENT '保全项目类型',
  `EdorValiDate` date NOT NULL COMMENT '保全生效日期',
  `EdorConfDate` date NOT NULL COMMENT '保全确认日期',
  `EdorMoney` decimal(20,2) NOT NULL COMMENT '保全发生费用',
  `PreInsuredAge` tinyint(4) NOT NULL COMMENT '变更前被保人投保年龄',
  `PreBasicSumInsured` decimal(20,2) NOT NULL COMMENT '变更前基本保额',
  `PreRiskAmnt` decimal(20,2) NOT NULL COMMENT '变更前风险保额',
  `PreReinsuranceAmnt` decimal(20,2) NOT NULL COMMENT '变更前分保保额',
  `PreRetentionAmount` decimal(20,2) NOT NULL COMMENT '变更前自留额',
  `PrePremium` decimal(20,2) NOT NULL COMMENT '变更前保费',
  `PreAccountValue` decimal(20,2) NOT NULL COMMENT '变更前账户价值',
  `PreWaiverPrem` decimal(20,2) NOT NULL COMMENT '变更前所需豁免剩余保费',
  `ProjectAcreageChange` decimal(20,2) NOT NULL COMMENT '建筑面积变化量',
  `ProjectCostChange` decimal(20,2) NOT NULL COMMENT '工程造价变化量',
  `SaparateFlag` varchar(4) DEFAULT NULL COMMENT '分出标记（0=未达到溢额线保单,1=分出保单）',
  `ReInsuranceContNo` varchar(64) DEFAULT NULL COMMENT '再保险合同号码',
  `ReinsurerCode` varchar(64) DEFAULT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) DEFAULT NULL COMMENT '再保险公司名称',
  `ReinsurMode` varchar(4) DEFAULT NULL COMMENT '分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）',
  `QuotaSharePercentage` varchar(32) NOT NULL COMMENT '分保比例',
  `ReinsuranceAmntChange` decimal(20,2) NOT NULL COMMENT '变更后分保保额',
  `RetentionAmount` decimal(20,2) NOT NULL COMMENT '变更后自留额',
  `ReinsurancePremiumChange` decimal(20,2) NOT NULL COMMENT '变更分保费',
  `ReinsuranceCommssionChange` decimal(20,2) NOT NULL COMMENT '变更分保佣金',
  `Currency` varchar(4) DEFAULT NULL COMMENT '货币代码',
  `ReComputationsDate` date NOT NULL COMMENT '分保计算日期',
  `AccountGetDate` date NOT NULL COMMENT '账单归属日期',
  `AccTransNo` varchar(64) DEFAULT NULL COMMENT '所属账单流水号',
  `DataSource` tinyint(4) NOT NULL COMMENT '数据来源（0=系统,1=人工）',
  `PushStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态（0=未推送,1=已推送）',
  `PushDate` date DEFAULT NULL COMMENT '推送日期',
  `PushBy` varchar(64) DEFAULT NULL COMMENT '推送人',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `IsDel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除, 0:否, 1:是',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CreateBy` varchar(64) DEFAULT NULL COMMENT '创建者',
  `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `UpdateBy` varchar(64) DEFAULT NULL COMMENT '更新者'
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保保全变更信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0006: 保单登记再保理赔信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_claim` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `TransactionNo` varchar(64) DEFAULT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) DEFAULT NULL COMMENT '保险机构代码,唯一固定值000166',
  `GrpPolicyNo` varchar(64) NOT NULL COMMENT '团体保单号',
  `GrpProductNo` varchar(64) NOT NULL COMMENT '团单保险险种号码',
  `PolicyNo` varchar(64) DEFAULT NULL COMMENT '个人保单号',
  `ProductNo` varchar(64) DEFAULT NULL COMMENT '个单保险险种号码',
  `GPFlag` varchar(4) DEFAULT NULL COMMENT '团个性质（01=个险,02=团险,99=其他）',
  `MainProductNo` varchar(64) DEFAULT NULL COMMENT '主险保险险种号码',
  `MainProductFlag` varchar(4) DEFAULT NULL COMMENT '主附险性质代码（1=主险,2=附加险,3=不区分）',
  `ProductCode` varchar(64) DEFAULT NULL COMMENT '产品编码',
  `LiabilityCode` varchar(64) DEFAULT NULL COMMENT '责任代码',
  `LiabilityName` varchar(128) DEFAULT NULL COMMENT '责任名称',
  `GetLiabilityCode` varchar(64) DEFAULT NULL COMMENT '给付责任代码',
  `GetLiabilityName` varchar(128) DEFAULT NULL COMMENT '给付责任名称',
  `BenefitType` varchar(4) NOT NULL COMMENT '赔付责任类型代码',
  `TermType` varchar(4) NOT NULL COMMENT '保险期限类型',
  `ManageCom` varchar(64) NOT NULL COMMENT '管理机构代码',
  `PolYear` tinyint(4) DEFAULT NULL COMMENT '保单年度',
  `SignDate` date DEFAULT NULL COMMENT '签单日期',
  `EffDate` date DEFAULT NULL COMMENT '保险责任生效日期',
  `InvalidDate` date DEFAULT NULL COMMENT '保险责任终止日期',
  `UWConclusion` varchar(64) NOT NULL COMMENT '核保结论代码',
  `PolStatus` varchar(4) DEFAULT NULL COMMENT '保单状态代码',
  `Status` varchar(4) DEFAULT NULL COMMENT '保单险种状态代码',
  `BasicSumInsured` decimal(20,2) NOT NULL COMMENT '基本保额',
  `RiskAmnt` decimal(20,2) NOT NULL COMMENT '风险保额',
  `Premium` decimal(20,2) NOT NULL COMMENT '保费',
  `DeductibleType` varchar(4) NOT NULL COMMENT '免赔类型代码',
  `Deductible` decimal(20,2) NOT NULL COMMENT '免赔额',
  `ClaimRatio` decimal(10,2) NOT NULL COMMENT '赔付比例',
  `AccountValue` decimal(20,2) NOT NULL COMMENT '保险账户价值',
  `FacultativeFlag` varchar(64) DEFAULT NULL COMMENT '临分标记（0=否,1=是）',
  `AnonymousFlag` varchar(4) NOT NULL COMMENT '无名单标志',
  `WaiverFlag` varchar(4) NOT NULL COMMENT '豁免险标志（0=否,1=是）',
  `WaiverPrem` decimal(20,2) NOT NULL COMMENT '所需豁免剩余保费',
  `FinalCashValue` decimal(20,2) NOT NULL COMMENT '期末现金价值',
  `FinalLiabilityReserve` decimal(20,2) NOT NULL COMMENT '期末责任准备金',
  `InsurePeoples` tinyint(4) NOT NULL COMMENT '被保人数',
  `InsuredNo` varchar(64) DEFAULT NULL COMMENT '被保人客户号',
  `InsuredName` varchar(64) DEFAULT NULL COMMENT '被保人姓名',
  `InsuredSex` varchar(4) DEFAULT NULL COMMENT '被保人性别',
  `InsuredCertType` varchar(4) DEFAULT NULL COMMENT '被保人证件类型',
  `InsuredCertNo` varchar(64) DEFAULT NULL COMMENT '被保人证件编码',
  `OccupationType` varchar(12) DEFAULT NULL COMMENT '职业代码',
  `AppntAge` tinyint(4) NOT NULL COMMENT '投保年龄',
  `PreAge` tinyint(4) NOT NULL COMMENT '当前年龄',
  `ProfessionalFee` decimal(20,2) NOT NULL COMMENT '职业加费金额',
  `SubStandardFee` decimal(20,2) NOT NULL COMMENT '次标准体加费金额',
  `EMRate` decimal(10,4) NOT NULL COMMENT 'EM加点',
  `ProjectFlag` varchar(4) NOT NULL COMMENT '建工险标志',
  `SaparateFlag` varchar(4) DEFAULT NULL COMMENT '分出标记（0=未达到溢额线保单,1=分出保单）',
  `ReInsuranceContNo` varchar(64) DEFAULT NULL COMMENT '再保险合同号码',
  `ReinsurerCode` varchar(64) DEFAULT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) DEFAULT NULL COMMENT '再保险公司名称',
  `ReinsurMode` varchar(4) DEFAULT NULL COMMENT '分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）',
  `RetentionAmount` decimal(20,2) NOT NULL COMMENT '自留额',
  `ReinsuranceAmnt` decimal(20,2) NOT NULL COMMENT '分保保额',
  `QuotaSharePercentage` varchar(32) NOT NULL COMMENT '分保比例',
  `ClaimNo` varchar(64) NOT NULL COMMENT '赔案号',
  `AccidentDate` date NOT NULL COMMENT '出险日期',
  `ClmSettDate` date NOT NULL COMMENT '结案日期',
  `PayStatusCode` varchar(4) NOT NULL COMMENT '理赔结论代码',
  `ClaimMoney` decimal(20,2) NOT NULL COMMENT '实际赔款金额',
  `BackClaimMoney` decimal(20,2) NOT NULL COMMENT '摊回赔款金额',
  `BackDate` date NOT NULL COMMENT '摊回日期',
  `ReComputationsDate` date NOT NULL COMMENT '分保计算日期',
  `AccountGetDate` date NOT NULL COMMENT '账单归属日期',
  `Currency` varchar(4) DEFAULT NULL COMMENT '货币代码',
  `AccTransNo` varchar(64) DEFAULT NULL COMMENT '所属账单流水号',
  `DataSource` tinyint(4) NOT NULL COMMENT '数据来源（0=系统,1=人工）',
  `PushStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态（0=未推送,1=已推送）',
  `PushDate` date DEFAULT NULL COMMENT '推送日期',
  `PushBy` varchar(64) DEFAULT NULL COMMENT '推送人',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `IsDel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除, 0:否, 1:是',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CreateBy` varchar(64) DEFAULT NULL COMMENT '创建者',
  `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `UpdateBy` varchar(64) DEFAULT NULL COMMENT '更新者'
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保理赔信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);

-- =============================================
-- TB0007: 保单登记再保生存金信息表
-- =============================================
CREATE TABLE IF NOT EXISTS `t_dws_prp_benefit` (
  `Id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `TransactionNo` varchar(64) DEFAULT NULL COMMENT '交易编码',
  `CompanyCode` varchar(64) DEFAULT NULL COMMENT '保险机构代码,唯一固定值000166',
  `GrpPolicyNo` varchar(64) NOT NULL COMMENT '团体保单号',
  `GPFlag` varchar(4) DEFAULT NULL COMMENT '团个性质（01=个险,02=团险,99=其他）',
  `PolicyNo` varchar(64) DEFAULT NULL COMMENT '个人保单号',
  `ProductNo` varchar(64) DEFAULT NULL COMMENT '个单保险险种号码',
  `PolYear` tinyint(4) DEFAULT NULL COMMENT '保单年度',
  `ProductCode` varchar(64) DEFAULT NULL COMMENT '产品编码',
  `LiabilityCode` varchar(64) DEFAULT NULL COMMENT '责任代码',
  `LiabilityName` varchar(128) DEFAULT NULL COMMENT '责任名称',
  `GetLiabilityCode` varchar(64) DEFAULT NULL COMMENT '给付责任代码',
  `GetLiabilityName` varchar(128) DEFAULT NULL COMMENT '给付责任名称',
  `TermType` varchar(4) NOT NULL COMMENT '保险期限类型',
  `WDNo` varchar(4) NOT NULL COMMENT '领取序号',
  `InsuredNo` varchar(64) DEFAULT NULL COMMENT '被保人客户号',
  `InsuredName` varchar(64) DEFAULT NULL COMMENT '被保人姓名',
  `InsuredSex` varchar(4) DEFAULT NULL COMMENT '被保人性别',
  `InsuredCertType` varchar(4) DEFAULT NULL COMMENT '被保人证件类型',
  `InsuredCertNo` varchar(64) DEFAULT NULL COMMENT '被保人证件编码',
  `OccupationType` varchar(12) DEFAULT NULL COMMENT '职业代码',
  `AppntAge` tinyint(4) NOT NULL COMMENT '投保年龄',
  `PreAge` tinyint(4) NOT NULL COMMENT '当前年龄',
  `BenefitDate` date DEFAULT NULL COMMENT '给付日期',
  `SaparateFlag` varchar(4) DEFAULT NULL COMMENT '分出标记（0=未达到溢额线保单,1=分出保单）',
  `BenefitClass` varchar(12) NOT NULL COMMENT '生存金类型',
  `BenefitAmount` decimal(20,2) NOT NULL COMMENT '生存金领取金额',
  `EnterAccDate` date DEFAULT NULL COMMENT '到账日期',
  `ReInsuranceContNo` varchar(64) DEFAULT NULL COMMENT '再保险合同号码',
  `ReinsurerCode` varchar(64) DEFAULT NULL COMMENT '再保险公司代码',
  `ReinsurerName` varchar(256) DEFAULT NULL COMMENT '再保险公司名称',
  `ReinsurMode` varchar(4) DEFAULT NULL COMMENT '分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）',
  `BackBenefitAmount` decimal(20,2) NOT NULL COMMENT '摊回金额',
  `BackDate` date NOT NULL COMMENT '摊回日期',
  `Currency` varchar(4) DEFAULT NULL COMMENT '货币代码',
  `ReComputationsDate` date NOT NULL COMMENT '分保计算日期',
  `AccountGetDate` date NOT NULL COMMENT '账单归属日期',
  `AccTransNo` varchar(64) DEFAULT NULL COMMENT '所属账单流水号',
  `DataSource` tinyint(4) NOT NULL COMMENT '数据来源（0=系统,1=人工）',
  `PushStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推送状态（0=未推送,1=已推送）',
  `PushDate` date DEFAULT NULL COMMENT '推送日期',
  `PushBy` varchar(64) DEFAULT NULL COMMENT '推送人',
  `Remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `IsDel` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除, 0:否, 1:是',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CreateBy` varchar(64) DEFAULT NULL COMMENT '创建者',
  `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `UpdateBy` varchar(64) DEFAULT NULL COMMENT '更新者'
) ENGINE=OLAP
PRIMARY KEY(`Id`)
COMMENT "保单登记再保生存金信息表"
DISTRIBUTED BY HASH(`Id`) BUCKETS 16
PROPERTIES (
"compression" = "LZ4",
"enable_persistent_index" = "true",
"fast_schema_evolution" = "false",
"replicated_storage" = "true",
"replication_num" = "3"
);