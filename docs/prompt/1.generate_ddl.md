# 基于设计文档创建表DDL
## 数据库：
- StarRocks 3.1
- CHARSET=utf8

## 需求描述：
- 请基于docs模块下“design/program_design.md”文档中的2.3章节只有生成所有新建表表的DDL
- 以init.sql文件存储到docs模块下的sql目录下
- 所有表的DDL都写到init.sql中
- 金额字段统一使用decimal类型，长度为20位，小数位为2位
- 不要漏掉字段，全部字段都要包含

## 添加公共字段：
- 针对所有表添加以下字段信息
| 字段名      | 数据类型 | 长度  | 允许空 | 是否主键 | 默认值                | 说明                 |
| ----------- | -------- | ----- | ------ | -------- | --------------------- | -------------------- |
| Id          | bigint   | 20    | 否     | 是       | 无                    | 主键                   |
| IsDel      | tinyint  | 1     | 否     | 否       | 0                     | 是否删除, 0:否, 1:是    |
| CreateTime | datetime |       | 否     | 否       | current_timestamp     | 创建时间               |
| CreateBy   | verchar  | 64    | 是     | 否       |                       | 创建者                 |
| UpdateTime | datetime |       | 否     | 否       | current_timestamp     | 更新时间               |
| UpdateBy   | verchar  | 64    | 是     | 否       |                       | 更新者                 |