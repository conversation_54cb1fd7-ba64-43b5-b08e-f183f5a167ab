# 需求规格说明书模板

## 文档信息

| 项目名称 | 【需求名称】 |
| -------- | ------------ |
| 文档版本 | V1.0         |
| 作者     | 武自昌       |
| 创建日期 | 2025-06-16   |
| 状态     | 已确认       |

## 变更履历

| 版本  | 日期        |  变更描述  | 修订人 | 审核 |
| ---- | ---------- | -------- | ------ | ---- |
| V1.0 | 2025-06-16 | 初次编写   | 武自昌 | 通过  |

## 1. 需求概述

### 1.1 需求背景

先忽略

### 1.2 需求目标

先忽略

### 1.3 需求范围

先忽略

### 1.4 相关干系人

| 角色       | 部门 | 姓名 | 职责 |
| ---------- | ---- | ---- | ---- |
| 产品负责人 |      |      |      |
| 业务负责人 |      |      |      |
| 技术负责人 |      |      |      |

## 2. 业务架构

### 2.1 业务模块关系图

无

### 2.2 模块列表

| 模块编号 | 模块名称 | 模块英文名 | 英文缩写    |
| -------- | -------- | ---------- |---------|
| MD0001   | 保单登记     | policy register platform   | dws_prp |

### 2.3 数据模型

#### 2.3.1 久期模块
##### 2.3.1.1 表间关系

##### 2.3.1.2 表名字典
| 表编号    | 表中文名                 | 表英文名                         |   备注     |
|--------|---------------------------|---------------------------------| -----------|
| TB0001 | 保单登记再保产品信息表        | t_dws_prp_product               |   新建表    |
| TB0002 | 保单登记再保合同信息表        | t_dws_prp_insure_cont           |   新建表    |
| TB0003 | 保单登记再保账单信息表        | t_dws_prp_account               |   新建表    |
| TB0004 | 保单登记再保首续期险种明细表   | t_dws_prp_cont                  |   新建表    |
| TB0005 | 保单登记再保保全变更信息表     | t_dws_prp_edor                  |   新建表    |
| TB0006 | 保单登记再保理赔信息表        | t_dws_prp_claim                 |   新建表    |
| TB0007 | 保单登记再保生存金信息表       | t_dws_prp_benefit               |   新建表    |
| TB0008 | 再保合同表                   | t_cedeout_contract              |   原有表    |
| TB0009 | 再保合同责任表               | t_cedeout_contract_liability    |   原有表    |
| TB0010 | 再保结算账单表               | t_dws_reinsu_settle_bill        |   原有表    |
| TB0011 | 再保分出摊回明细             | t_dws_reinsu_trade              |   原有表    |


##### 2.3.1.3 表集
**（1）TB0001**

| 字段名                       | 数据类型  | 长度  | 允许空  | 唯一索引  | 默认值 | 说明                                                                                 |
|---------------------------| -------- |-----| ------ | ------- | ------ |------------------------------------------------------------------------------------|
| **TransactionNo**         | varchar  | 64  | 否     | 否       | 无     | 交易编码                                                                               |
| **CompanyCode**           | varchar  | 64  | 否     | 否       | 无     | 保险机构代码,唯一固定值000166                                                                 |
| **ReInsuranceContNo**     | varchar  | 64  | 否     | 否       | 无     | 再保险合同号码                                                                            |
| **ReInsuranceContName**   | varchar  | 256 | 否     | 否       | 无     | 再保险合同名称                                                                            |
| **ReInsuranceContTitle**  | varchar  | 256 | 否     | 否       | 无     | 再保险合同简称                                                                            |
| **MainReInsuranceContNo** | varchar  | 64  | 否     | 否       | 无     | 再保险附约主合同号                                                                          |
| **ContOrAmendmentType**   | varchar  | 4   | 否     | 否       | 无     | 合同附约类型（1=主合同,2=附约）                                                                 |
| **ProductCode**           | varchar  | 64  | 否     | 否       | 无     | 产品编码                                                                               |
| **ProductName**           | varchar  | 128 | 否     | 否       | 无     | 产品名称                                                                               |
| **GPFlag**                | varchar  | 4   | 否     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）                                                             |
| **ProductType**           | varchar  | 64  | 否     | 否       | 无     | 险类代码                                                                               |
| **LiabilityCode**         | varchar  | 64  | 否     | 否       | 无     | 责任代码                                                                               |
| **LiabilityName**         | varchar  | 128 | 否     | 否       | 无     | 责任名称                                                                               |
| **ReinsurerCode**         | varchar  | 64  | 否     | 否       | 无     | 再保险公司代码                                                                            |
| **ReinsurerName**         | varchar  | 256 | 否     | 否       | 无     | 再保险公司名称                                                                            |
| **ReinsuranceShare**      | varchar  | 32  | 否     | 否       | 无     | 再保人参与份额比例                                                                          |
| **ReinsurMode**           | varchar  | 4   | 否     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）                                                       |
| **ReInsuranceType**       | varchar  | 4   | 否     | 否       | 无     | 再保类型（01=事故超赔,02=修正共保方式,03=共保方式,04=风险保费方式,05=赔付率超赔,06=损失终止,07=险位超赔）                  |
| **TermType**              | varchar  | 4   | 否     | 否       | 无     | 保险期限类型（10=长期险,11=定期(年),12=定期(岁),13=定期(两可),14=终身,20=短期险,21=短期,22=极短期,30=主险缴费期,90=未知） |
| **RetentionAmount**       | varchar  | 32  | 否     | 否       | 无     | 自留额                                                                                |
| **RetentionPercentage**   | varchar  | 32  | 否     | 否       | 无     | 自留比例                                                                               |
| **QuotaSharePercentage**  | varchar  | 32  | 否     | 否       | 无     | 分保比例                                                                               |
| **ReportYear**            | int      | 11  | 否     | 否       | 无     | 所属年份                                                                               |
| **ReportMonth**           | tinyint  | 4   | 否     | 否       | 无     | 所属月份                                                                               |
| **AccountPeriod**         | varchar  | 64  | 否     | 否       | 无     | 所属账期                                                                               |
| **DataSource**            | tinyint  | 4   | 否     | 否       | 无     | 数据来源（0=系统,1=人工）                                                                     |
| **PushStatus**            | tinyint  | 4   | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）                                                                   |
| **PushDate**              | date     |     | 是     | 否       | 无     | 推送日期                                                                               |
| **PushBy**                | varchar  | 64  | 是     | 否       | 无     | 推送人                                                                                |
| **Remark**                | varchar  | 128 | 是     | 否       | 无     | 备注                                                                                 |

**（2）TB0002**

| 字段名                          | 数据类型  | 长度      | 允许空  | 唯一索引  | 默认值 | 说明                          |
|------------------------------| -------- |---------| ------ | ------- | ------ | ---------------------------- |
| **TransactionNo**            | varchar  | 64      | 否     | 否       | 无     | 交易编码  |
| **CompanyCode**              | varchar  | 64      | 否     | 否       | 无     | 保险机构代码,唯一固定值000166  |
| **ReInsuranceContNo**        | varchar  | 64      | 否     | 否       | 无     | 再保险合同号码  |
| **ReInsuranceContName**      | varchar  | 256     | 否     | 否       | 无     | 再保险合同名称  |
| **ReInsuranceContTitle**     | varchar  | 256     | 否     | 否       | 无     | 再保险合同简称  |
| **MainReInsuranceContNo**    | varchar  | 64      | 否     | 否       | 无     | 再保险附约主合同号  |
| **ContOrAmendmentType**      | varchar  | 4       | 否     | 否       | 无     | 合同附约类型（1=主合同,2=附约）  |
| **ContAttribute**            | varchar  | 4       | 否     | 否       | 无     | 合同属性（1=保险合同,2=混合合同,3=非保险合同）  |
| **ContStatus**               | varchar  | 4       | 否     | 否       | 无     | 合同状态（1=有效,2=终止）  |
| **TreatyOrFacultativeFlag**  | varchar  | 4       | 否     | 否       | 无     | 合同/临分标志（0=否,1=是）  |
| **ContSigndate**             | date     |         | 否     | 否       | 无     | 合同签署日期  |
| **PeriodFrom**               | date     |         | 否     | 否       | 无     | 合同生效起期  |
| **PeriodTo**                 | date     |         | 否     | 否       | 无     | 合同生效止期    |
| **ContType**                 | varchar  | 4       | 否     | 否       | 无     | 合同类型（1=比例合同,2=非比例合同）    |
| **ReinsurerCode**            | varchar  | 64      | 否     | 否       | 无     | 再保险公司代码    |
| **ReinsurerName**            | varchar  | 256     | 否     | 否       | 无     | 再保险公司名称    |
| **ChargeType**               | varchar  | 4       | 否     | 否       | 无     | 佣金核算方式（1=业务年度，2=财务年度）    |
| **ReportYear**               | int      | 11      | 否     | 否       | 无     | 所属年份  |
| **ReportMonth**              | tinyint  | 4       | 否     | 否       | 无     | 所属月份  |
| **AccountPeriod**            | varchar  | 64      | 否     | 否       | 无     | 所属账期  |
| **DataSource**               | tinyint  | 4       | 否     | 否       | 无     | 数据来源（0=系统,1=人工）  |
| **PushStatus**               | tinyint  | 4       | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）  |
| **PushDate**                 | date     |         | 是     | 否       | 无     | 推送日期  |
| **PushBy**                   | varchar  | 64      | 是     | 否       | 无     | 推送人    |
| **Remark**                   | varchar  | 128     | 是     | 否       | 无     | 备注      |

**（3）TB0003**

| 字段名                            | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                         |
|--------------------------------| -------- | -----  | ------ | ------- | ------ | --------------------------- |
| **TransactionNo**              | varchar  | 64     | 是     | 否       | 无     | 交易编码 |
| **CompanyCode**                | varchar  | 64     | 是     | 否       | 无     | 保险机构代码,唯一固定值000166 |
| **AccountID**                  | varchar  | 64     | 是     | 否       | 无     | 账单编号 |
| **AccountingPeriodfrom**       | date     |        | 是     | 否       | 无     | 账单起期 |
| **AccountingPeriodto**         | date     |        | 是     | 否       | 无     | 账单止期 |
| **ReinsurerCode**              | varchar  | 64     | 是     | 否       | 无     | 再保险公司代码 |
| **ReinsurerName**              | varchar  | 256    | 是     | 否       | 无     | 再保险公司名称 |
| **ReInsuranceContNo**          | varchar  | 64     | 是     | 否       | 无     | 再保险合同号码 |
| **ReInsuranceContName**        | varchar  | 256    | 是     | 否       | 无     | 再保险合同名称 |
| **ReinsurancePremium**         | decimal  | 20     | 是     | 否       | '0'    | 分保费 |
| **ReinsuranceCommssionRate**   | decimal  | 20     | 是     | 否       | '0'    | 分保佣金率 |
| **ReinsuranceCommssion**       | decimal  | 20     | 是     | 否       | '0'    | 分保佣金 |
| **ReturnReinsurancePremium**   | decimal  | 20     | 是     | 否       | '0'    | 退回分保费 |
| **ReturnReinsuranceCommssion** | decimal  | 20     | 是     | 否       | '0'    | 退回分保佣金 |
| **ReturnSurrenderPay**         | decimal  | 20     | 是     | 否       | '0'    | 摊回退保金 |
| **ReturnClaimPay**             | decimal  | 20     | 是     | 否       | '0'    | 摊回理赔款 |
| **ReturnMaturity**             | decimal  | 20     | 是     | 否       | '0'    | 摊回满期金 |
| **ReturnAnnuity**              | decimal  | 20     | 是     | 否       | '0'    | 摊回年金 |
| **ReturnLivBene**              | decimal  | 20     | 是     | 否       | '0'    | 摊回生存金 |
| **AccountStatus**              | varchar  | 4      | 是     | 否       | 无     | 账单状态（1=有效,2=无效） |
| **PairingStatus**              | varchar  | 4      | 是     | 否       | 无     | 结算状态（1=未结算,2=已结算） |
| **PairingDate**                | date     |        | 是     | 否       | 无     | 结算日期 |
| **Currency**                   | varchar  | 4      | 是     | 否       | 无     | 货币代码 |
| **CurrentRate**                | decimal  | 20     | 是     | 否       | 无     | 结算汇率 |
| **SettleBillNo**               | varchar  | 64     | 是     | 否       | 无     | 结算账单号 |
| **ContImportStatus**           | tinyint  | 4      | 是     | 否       | 无     | 首续期明细导入状态（0=未导入,1=已导入,2=不需要导入） |
| **ContImportRemark**           | varchar  | 128    | 是     | 否       | 无     | 首续期明细导入描述 |
| **EdorImportStatus**           | tinyint  | 4      | 是     | 否       | 无     | 保全明细导入状态（0=未导入,1=已导入,2=不需要导入） |
| **EdorImportRemark**           | varchar  | 128    | 是     | 否       | 无     | 保全明细导入描述 |
| **ClaimImportStatus**          | tinyint  | 4      | 是     | 否       | 无     | 理赔明细导入状态（0=未导入,1=已导入,2=不需要导入） |
| **ClaimImportRemark**          | varchar  | 128    | 是     | 否       | 无     | 理赔明细导入描述 |
| **BenefitImportStatus**        | tinyint  | 4      | 是     | 否       | 无     | 生存金明细导入状态（0=未导入,1=已导入,2=不需要导入） |
| **BenefitImportRemark**        | varchar  | 128    | 是     | 否       | 无     | 生存金明细导入描述 |
| **ReportYear**                 | int      | 11     | 否     | 否       | 无     | 所属年份  |
| **ReportMonth**                | tinyint  | 4      | 否     | 否       | 无     | 所属月份  |
| **AccountPeriod**              | varchar  | 64     | 否     | 否       | 无     | 所属账期  |
| **DataSource**                 | tinyint  | 4      | 否     | 否       | 无     | 数据来源（0=系统,1=人工） |
| **PushStatus**                 | tinyint  | 4      | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送） |
| **PushDate**                   | date     |        | 是     | 否       | 无     | 推送日期  |
| **PushBy**                     | varchar  | 64     | 是     | 否       | 无     | 推送人    |
| **Remark**                     | varchar  | 128     | 是     | 否       | 无     | 备注     |

**（4）TB0004**

| 字段名                       | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |
| **TransactionNo**         | varchar  | 64     | 是     | 否       | 无     | 交易编码  | 
| **CompanyCode**           | varchar  | 64     | 是     | 否       | 无     | 保险机构代码,唯一固定值000166  | 
| **GrpPolicyNo**           | varchar  | 64     | 否     | 否       | 无     | 团体保单号  | 
| **GrpProductNo**          | varchar  | 64     | 否     | 否       | 无     | 团单保险险种号码  | 
| **PolicyNo**              | varchar  | 64     | 是     | 否       | 无     | 个人保单号  | 
| **ProductNo**             | varchar  | 64     | 是     | 否       | 无     | 个单保险险种号码  | 
| **GPFlag**                | varchar  | 4      | 是     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）  | 
| **MainProductNo**         | varchar  | 64     | 是     | 否       | 无     | 主险保险险种号码  | 
| **MainProductFlag**       | varchar  | 4      | 是     | 否       | 无     | 主附险性质代码（1=主险,2=附加险,3=不区分）  | 
| **ProductCode**           | varchar  | 64     | 是     | 否       | 无     | 产品编码  | 
| **LiabilityCode**         | varchar  | 64     | 是     | 否       | 无     | 责任代码  | 
| **LiabilityName**         | varchar  | 128    | 是     | 否       | 无     | 责任名称  | 
| **Classification**        | varchar  | 64     | 否     | 否       | 无     | 责任分类代码  | 
| **EventType**             | varchar  | 4      | 是     | 否       | 无     | 业务类型（01=新单,02=续期,03=续保） |
| **RenewalTimes**          | tinyint  | 4      | 否     | 否       | 无     | 续期续保次数(保单年度-1)  | 
| **TermType**              | varchar  | 4      | 否     | 否       | 无     | 保险期限类型  | 
| **ManageCom**             | varchar  | 4      | 否     | 否       | 无     | 管理机构代码  | 
| **PolYear**               | tinyint  | 4      | 是     | 否       | 无     | 保单年度  | 
| **SignDate**              | date     |        | 是     | 否       | 无     | 签单日期  | 
| **EffDate**               | date     |        | 是     | 否       | 无     | 保险责任生效日期  | 
| **InvalidDate**           | date     |        | 是     | 否       | 无     | 保险责任终止日期  | 
| **UWConclusion**          | varchar  | 64     | 否     | 否       | 无     | 核保结论代码  | 
| **PolStatus**             | varchar  | 4      | 是     | 否       | 无     | 保单状态代码  | 
| **Status**                | varchar  | 4      | 是     | 否       | 无     | 保单险种状态代码  | 
| **BasicSumInsured**       | decimal  | 20     | 否     | 否       | 无     | 基本保额  |
| **RiskAmnt**              | decimal  | 20     | 否     | 否       | 无     | 风险保额  | 
| **Premium**               | decimal  | 20     | 否     | 否       | 无     | 保费  | 
| **AccountValue**          | decimal  | 20     | 否     | 否       | 无     | 保险账户价值  | 
| **FacultativeFlag**       | varchar  | 64     | 是     | 否       | 无     | 临分标记（0=否,1=是）  | 
| **AnonymousFlag**         | varchar  | 4      | 否     | 否       | 无     | 无名单标志  | 
| **WaiverFlag**            | varchar  | 4      | 否     | 否       | 无     | 豁免险标志（0=否,1=是）  | 
| **WaiverPrem**            | decimal  | 20     | 否     | 否       | 无     | 所需豁免剩余保费  | 
| **FinalCashValue**        | decimal  | 20     | 否     | 否       | 无     | 期末现金价值  | 
| **FinalLiabilityReserve** | decimal  | 20     | 否     | 否       | 无     | 期末责任准备金  | 
| **InsurePeoples**         | tinyint  | 4      | 否     | 否       | 无     | 被保人数  | 
| **InsuredNo**             | varchar  | 64     | 是     | 否       | 无     | 被保人客户号  | 
| **InsuredName**           | varchar  | 64     | 是     | 否       | 无     | 被保人姓名  | 
| **InsuredSex**            | varchar  | 4      | 是     | 否       | 无     | 被保人性别  | 
| **InsuredCertType**       | varchar  | 4      | 是     | 否       | 无     | 被保人证件类型  | 
| **InsuredCertNo**         | varchar  | 64     | 是     | 否       | 无     | 被保人证件编码  | 
| **OccupationType**        | varchar  | 12     | 是     | 否       | 无     | 职业代码  | 
| **AppntAge**              | tinyint  | 4      | 否     | 否       | 无     | 投保年龄   | 
| **PreAge**                | tinyint  | 4      | 否     | 否       | 无     | 当前年龄  | 
| **ProfessionalFee**       | decimal  | 20     | 否     | 否       | 无     | 职业加费金额  | 
| **SubStandardFee**        | decimal  | 20     | 否     | 否       | 无     | 次标准体加费金额  | 
| **EMRate**                | decimal  | 10     | 否     | 否       | 无     | EM加点  | 
| **ProjectFlag**           | varchar  | 4      | 否     | 否       | 无     | 建工险标志  |
| **SaparateFlag**          | varchar  | 4      | 是     | 否       | 无     | 分出标记（0=未达到溢额线保单,1=分出保单）  | 
| **ReInsuranceContNo**     | varchar  | 64     | 是     | 否       | 无     | 再保险合同号码  | 
| **ReinsurerCode**         | varchar  | 64     | 是     | 否       | 无     | 再保险公司代码  | 
| **ReinsurerName**         | varchar  | 256    | 是     | 否       | 无     | 再保险公司名称  | 
| **ReinsurMode**           | varchar  | 4      | 是     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）  | 
| **RetentionAmount**       | decimal  | 20     | 否     | 否       | 无     | 自留额  | 
| **ReinsuranceAmnt**       | decimal  | 20     | 否     | 否       | 无     | 分保保额  | 
| **QuotaSharePercentage**  | varchar  | 32     | 否     | 否       | 无     | 分保比例  | 
| **ReinsurancePremium**    | decimal  | 20     | 否     | 否       | 无     | 分保费  | 
| **ReinsuranceCommssion**  | decimal  | 20     | 否     | 否       | 无     | 分保佣金  | 
| **ReComputationsDate**    | date     |        | 否     | 否       | 无     | 分保计算日期  | 
| **AccountGetDate**        | date     |        | 否     | 否       | 无     | 账单归属日期  | 
| **Currency**              | varchar  | 4      | 是     | 否       | 无     | 货币代码  | 
| **AccTransNo**            | varchar  | 64     | 是     | 否       | 无     | 所属账单流水号  | 
| **DataSource**            | tinyint  | 4      | 否     | 否       | 无     | 数据来源（0=系统,1=人工）  |
| **PushStatus**            | tinyint  | 4      | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）  |
| **PushDate**              | date     |        | 是     | 否       | 无     | 推送日期  |
| **PushBy**                | varchar  | 64     | 是     | 否       | 无     | 推送人    |
| **Remark**                | varchar  | 128    | 是     | 否       | 无     | 备注      |


**（5）TB0005**

| 字段名                            | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|--------------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |
| **TransactionNo**              | varchar  | 64     | 是     | 否       | 无     | 交易编码  | 
| **CompanyCode**                | varchar  | 64     | 是     | 否       | 无     | 保险机构代码,唯一固定值000166  | 
| **GrpPolicyNo**                | varchar  | 64     | 否     | 否       | 无     | 团体保单号  | 
| **GrpProductNo**               | varchar  | 64     | 否     | 否       | 无     | 团单保险险种号码  | 
| **PolicyNo**                   | varchar  | 64     | 是     | 否       | 无     | 个人保单号  | 
| **ProductNo**                  | varchar  | 64     | 是     | 否       | 无     | 个单保险险种号码  | 
| **GPFlag**                     | varchar  | 4      | 是     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）  | 
| **MainProductNo**              | varchar  | 64     | 是     | 否       | 无     | 主险保险险种号码  | 
| **MainProductFlag**            | varchar  | 4      | 是     | 否       | 无     | 主附险性质代码（1=主险,2=附加险,3=不区分）  | 
| **ProductCode**                | varchar  | 64     | 是     | 否       | 无     | 产品编码  | 
| **LiabilityCode**              | varchar  | 64     | 是     | 否       | 无     | 责任代码  | 
| **LiabilityName**              | varchar  | 128    | 是     | 否       | 无     | 责任名称  | 
| **Classification**             | varchar  | 64     | 否     | 否       | 无     | 责任分类代码  |
| **TermType**                   | varchar  | 4      | 否     | 否       | 无     | 保险期限类型  | 
| **ManageCom**                  | varchar  | 4      | 否     | 否       | 无     | 管理机构代码  | 
| **PolYear**                    | tinyint  | 4      | 是     | 否       | 无     | 保单年度  | 
| **SignDate**                   | date     |        | 是     | 否       | 无     | 签单日期  | 
| **EffDate**                    | date     |        | 是     | 否       | 无     | 保险责任生效日期  | 
| **InvalidDate**                | date     |        | 是     | 否       | 无     | 保险责任终止日期  | 
| **UWConclusion**               | varchar  | 64     | 否     | 否       | 无     | 核保结论代码  | 
| **PolStatus**                  | varchar  | 4      | 是     | 否       | 无     | 保单状态代码  | 
| **Status**                     | varchar  | 4      | 是     | 否       | 无     | 保单险种状态代码  | 
| **BasicSumInsured**            | decimal  | 20     | 否     | 否       | 无     | 基本保额  |
| **RiskAmnt**                   | decimal  | 20     | 否     | 否       | 无     | 风险保额  | 
| **Premium**                    | decimal  | 20     | 否     | 否       | 无     | 保费  | 
| **AccountValue**               | decimal  | 20     | 否     | 否       | 无     | 保险账户价值  | 
| **FacultativeFlag**            | varchar  | 64     | 是     | 否       | 无     | 临分标记（0=否,1=是）  | 
| **AnonymousFlag**              | varchar  | 4      | 否     | 否       | 无     | 无名单标志  | 
| **WaiverFlag**                 | varchar  | 4      | 否     | 否       | 无     | 豁免险标志（0=否,1=是）  | 
| **WaiverPrem**                 | decimal  | 20     | 否     | 否       | 无     | 所需豁免剩余保费  | 
| **FinalCashValue**             | decimal  | 20     | 否     | 否       | 无     | 期末现金价值  | 
| **FinalLiabilityReserve**      | decimal  | 20     | 否     | 否       | 无     | 期末责任准备金  | 
| **InsurePeoples**              | tinyint  | 4      | 否     | 否       | 无     | 被保人数  | 
| **InsuredNo**                  | varchar  | 64     | 是     | 否       | 无     | 被保人客户号  | 
| **InsuredName**                | varchar  | 64     | 是     | 否       | 无     | 被保人姓名  | 
| **InsuredSex**                 | varchar  | 4      | 是     | 否       | 无     | 被保人性别  | 
| **InsuredCertType**            | varchar  | 4      | 是     | 否       | 无     | 被保人证件类型  | 
| **InsuredCertNo**              | varchar  | 64     | 是     | 否       | 无     | 被保人证件编码  | 
| **OccupationType**             | varchar  | 12     | 是     | 否       | 无     | 职业代码  | 
| **AppntAge**                   | tinyint  | 4      | 否     | 否       | 无     | 投保年龄   | 
| **PreAge**                     | tinyint  | 4      | 否     | 否       | 无     | 当前年龄  | 
| **ProfessionalFee**            | decimal  | 20     | 否     | 否       | 无     | 职业加费金额  | 
| **SubStandardFee**             | decimal  | 20     | 否     | 否       | 无     | 次标准体加费金额  | 
| **EMRate**                     | decimal  | 10     | 否     | 否       | 无     | EM加点  | 
| **ProjectFlag**                | varchar  | 4      | 否     | 否       | 无     | 建工险标志  |
| **EndorAcceptNo**              | varchar  | 64     | 否     | 否       | 无     | 保全受理号码  |
| **EndorsementNo**              | varchar  | 64     | 否     | 否       | 无     | 保全批单号码  |
| **EdorType**                   | varchar  | 4      | 否     | 否       | 无     | 保全项目类型  |
| **EdorValiDate**               | date     |        | 否     | 否       | 无     | 保全生效日期  |
| **EdorConfDate**               | date     |        | 否     | 否       | 无     | 保全确认日期  |
| **EdorMoney**                  | decimal  | 20     | 否     | 否       | 无     | 保全发生费用  |
| **PreInsuredAge**              | tinyint  | 4      | 否     | 否       | 无     | 变更前被保人投保年龄  |
| **PreBasicSumInsured**         | decimal  | 20     | 否     | 否       | 无     | 变更前基本保额  |
| **PreRiskAmnt**                | decimal  | 20     | 否     | 否       | 无     | 变更前风险保额  |
| **PreReinsuranceAmnt**         | decimal  | 20     | 否     | 否       | 无     | 变更前分保保额  |
| **PreRetentionAmount**         | decimal  | 20     | 否     | 否       | 无     | 变更前自留额  |
| **PrePremium**                 | decimal  | 20     | 否     | 否       | 无     | 变更前保费  |
| **PreAccountValue**            | decimal  | 20     | 否     | 否       | 无     | 变更前账户价值  |
| **PreWaiverPrem**              | decimal  | 20     | 否     | 否       | 无     | 变更前所需豁免剩余保费  |
| **ProjectAcreageChange**       | decimal  | 20     | 否     | 否       | 无     | 建筑面积变化量  |
| **ProjectCostChange**          | decimal  | 20     | 否     | 否       | 无     | 工程造价变化量  |
| **SaparateFlag**               | varchar  | 4      | 是     | 否       | 无     | 分出标记（0=未达到溢额线保单,1=分出保单）  | 
| **ReInsuranceContNo**          | varchar  | 64     | 是     | 否       | 无     | 再保险合同号码  | 
| **ReinsurerCode**              | varchar  | 64     | 是     | 否       | 无     | 再保险公司代码  | 
| **ReinsurerName**              | varchar  | 256    | 是     | 否       | 无     | 再保险公司名称  |
| **ReinsurMode**                | varchar  | 4      | 是     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）  |
| **QuotaSharePercentage**       | varchar  | 32     | 否     | 否       | 无     | 分保比例  |
| **ReinsuranceAmntChange**      | decimal  | 20     | 否     | 否       | 无     | 变更后分保保额  |
| **RetentionAmount**            | decimal  | 20     | 否     | 否       | 无     | 变更后自留额  |
| **ReinsurancePremiumChange**   | decimal  | 20     | 否     | 否       | 无     | 变更分保费  | 
| **ReinsuranceCommssionChange** | decimal  | 20     | 否     | 否       | 无     | 变更分保佣金  | 
| **Currency**                   | varchar  | 4      | 是     | 否       | 无     | 货币代码  | 
| **ReComputationsDate**         | date     |        | 否     | 否       | 无     | 分保计算日期  | 
| **AccountGetDate**             | date     |        | 否     | 否       | 无     | 账单归属日期  | 
| **AccTransNo**                 | varchar  | 64     | 是     | 否       | 无     | 所属账单流水号  | 
| **DataSource**                 | tinyint  | 4      | 否     | 否       | 无     | 数据来源（0=系统,1=人工）  |
| **PushStatus**                 | tinyint  | 4      | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）  |
| **PushDate**                   | date     |        | 是     | 否       | 无     | 推送日期  |
| **PushBy**                     | varchar  | 64     | 是     | 否       | 无     | 推送人    |
| **Remark**                     | varchar  | 128    | 是     | 否       | 无     | 备注      |


**（6）TB0006**

| 字段名                       | 数据类型      |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------|-----------| -----  | ------ | ------- | ------ | ---------------------------- |
| **TransactionNo**         | varchar   | 64     | 是     | 否       | 无     | 交易编码  | 
| **CompanyCode**           | varchar   | 64     | 是     | 否       | 无     | 保险机构代码,唯一固定值000166  | 
| **GrpPolicyNo**           | varchar   | 64     | 否     | 否       | 无     | 团体保单号  | 
| **GrpProductNo**          | varchar   | 64     | 否     | 否       | 无     | 团单保险险种号码  | 
| **PolicyNo**              | varchar   | 64     | 是     | 否       | 无     | 个人保单号  | 
| **ProductNo**             | varchar   | 64     | 是     | 否       | 无     | 个单保险险种号码  | 
| **GPFlag**                | varchar   | 4      | 是     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）  | 
| **MainProductNo**         | varchar   | 64     | 是     | 否       | 无     | 主险保险险种号码  | 
| **MainProductFlag**       | varchar   | 4      | 是     | 否       | 无     | 主附险性质代码（1=主险,2=附加险,3=不区分）  | 
| **ProductCode**           | varchar   | 64     | 是     | 否       | 无     | 产品编码  | 
| **LiabilityCode**         | varchar   | 64     | 是     | 否       | 无     | 责任代码  | 
| **LiabilityName**         | varchar   | 128    | 是     | 否       | 无     | 责任名称  | 
| **GetLiabilityCode**      | varchar   | 64     | 是     | 否       | 无     | 给付责任代码  | 
| **GetLiabilityName**      | varchar   | 128    | 是     | 否       | 无     | 给付责任名称  | 
| **BenefitType**           | varchar   | 64     | 否     | 否       | 无     | 赔付责任类型代码  |
| **TermType**              | varchar   | 4      | 否     | 否       | 无     | 保险期限类型  | 
| **ManageCom**             | varchar   | 4      | 否     | 否       | 无     | 管理机构代码  |
| **PolYear**               | tinyint   | 4      | 是     | 否       | 无     | 保单年度  |
| **SignDate**              | date      |        | 是     | 否       | 无     | 签单日期  | 
| **EffDate**               | date      |        | 是     | 否       | 无     | 保险责任生效日期  | 
| **InvalidDate**           | date      |        | 是     | 否       | 无     | 保险责任终止日期  | 
| **UWConclusion**          | varchar   | 64     | 否     | 否       | 无     | 核保结论代码  | 
| **PolStatus**             | varchar   | 4      | 是     | 否       | 无     | 保单状态代码  | 
| **Status**                | varchar   | 4      | 是     | 否       | 无     | 保单险种状态代码  | 
| **BasicSumInsured**       | decimal   | 20     | 否     | 否       | 无     | 基本保额  |
| **RiskAmnt**              | decimal   | 20     | 否     | 否       | 无     | 风险保额  | 
| **Premium**               | decimal   | 20     | 否     | 否       | 无     | 保费  | 
| **DeductibleType**        | decimal   | 20     | 否     | 否       | 无     | 免赔类型代码  |
| **Deductible**            | decimal   | 20     | 否     | 否       | 无     | 免赔额  |
| **ClaimRatio**            | decimal   | 20     | 否     | 否       | 无     | 赔付比例  |
| **AccountValue**          | decimal   | 20     | 否     | 否       | 无     | 保险账户价值  |
| **FacultativeFlag**       | varchar   | 64     | 是     | 否       | 无     | 临分标记（0=否,1=是）  | 
| **AnonymousFlag**         | varchar   | 4      | 否     | 否       | 无     | 无名单标志  | 
| **WaiverFlag**            | varchar   | 4      | 否     | 否       | 无     | 豁免险标志（0=否,1=是）  | 
| **WaiverPrem**            | decimal   | 20     | 否     | 否       | 无     | 所需豁免剩余保费  | 
| **FinalCashValue**        | decimal   | 20     | 否     | 否       | 无     | 期末现金价值  | 
| **FinalLiabilityReserve** | decimal   | 20     | 否     | 否       | 无     | 期末责任准备金  | 
| **InsurePeoples**         | tinyint   | 4      | 否     | 否       | 无     | 被保人数  | 
| **InsuredNo**             | varchar   | 64     | 是     | 否       | 无     | 被保人客户号  | 
| **InsuredName**           | varchar   | 64     | 是     | 否       | 无     | 被保人姓名  | 
| **InsuredSex**            | varchar   | 4      | 是     | 否       | 无     | 被保人性别  | 
| **InsuredCertType**       | varchar   | 4      | 是     | 否       | 无     | 被保人证件类型  | 
| **InsuredCertNo**         | varchar   | 64     | 是     | 否       | 无     | 被保人证件编码  | 
| **OccupationType**        | varchar   | 12     | 是     | 否       | 无     | 职业代码  | 
| **AppntAge**              | tinyint   | 4      | 否     | 否       | 无     | 投保年龄   | 
| **PreAge**                | tinyint   | 4      | 否     | 否       | 无     | 当前年龄  | 
| **ProfessionalFee**       | decimal   | 20     | 否     | 否       | 无     | 职业加费金额  | 
| **SubStandardFee**        | decimal   | 20     | 否     | 否       | 无     | 次标准体加费金额  | 
| **EMRate**                | decimal   | 10     | 否     | 否       | 无     | EM加点  | 
| **ProjectFlag**           | varchar   | 4      | 否     | 否       | 无     | 建工险标志  |
| **SaparateFlag**          | varchar   | 4      | 是     | 否       | 无     | 分出标记（0=未达到溢额线保单,1=分出保单）  | 
| **ReInsuranceContNo**     | varchar   | 64     | 是     | 否       | 无     | 再保险合同号码  | 
| **ReinsurerCode**         | varchar   | 64     | 是     | 否       | 无     | 再保险公司代码  | 
| **ReinsurerName**         | varchar   | 256    | 是     | 否       | 无     | 再保险公司名称  | 
| **ReinsurMode**           | varchar   | 4      | 是     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）  | 
| **RetentionAmount**       | decimal   | 20     | 否     | 否       | 无     | 自留额  | 
| **ReinsuranceAmnt**       | decimal   | 20     | 否     | 否       | 无     | 分保保额  | 
| **QuotaSharePercentage**  | varchar   | 32     | 否     | 否       | 无     | 分保比例  |
| **ClaimNo**               | decimal   | 20     | 否     | 否       | 无     | 赔案号  | 
| **AccidentDate**          | date      |        | 否     | 否       | 无     | 出险日期  |
| **ClmSettDate**           | date      |        | 否     | 否       | 无     | 结案日期  |
| **PayStatusCode**         | date      |        | 否     | 否       | 无     | 理赔结论代码  |
| **ClaimMoney**            | date      |        | 否     | 否       | 无     | 实际赔款金额  |
| **BackClaimMoney**        | date      |        | 否     | 否       | 无     | 摊回赔款金额  |
| **BackDate**              | date      |        | 否     | 否       | 无     | 摊回日期  |
| **ReComputationsDate**    | date      |        | 否     | 否       | 无     | 分保计算日期  | 
| **AccountGetDate**        | date      |        | 否     | 否       | 无     | 账单归属日期  | 
| **Currency**              | varchar   | 4      | 是     | 否       | 无     | 货币代码  | 
| **AccTransNo**            | varchar   | 64     | 是     | 否       | 无     | 所属账单流水号  | 
| **DataSource**            | tinyint   | 4      | 否     | 否       | 无     | 数据来源（0=系统,1=人工）  |
| **PushStatus**            | tinyint   | 4      | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）  |
| **PushDate**              | date      |        | 是     | 否       | 无     | 推送日期  |
| **PushBy**                | varchar   | 64     | 是     | 否       | 无     | 推送人    |
| **Remark**                | varchar   | 128    | 是     | 否       | 无     | 备注      |


**（7）TB0007**

| 字段名                    | 数据类型      | 长度     | 允许空  | 唯一索引  | 默认值 | 说明                          |
|------------------------|-----------|--------| ------ | ------- | ------ | ---------------------------- |
| **TransactionNo**      | varchar   | 64     | 是     | 否       | 无     | 交易编码  | 
| **CompanyCode**        | varchar   | 64     | 是     | 否       | 无     | 保险机构代码,唯一固定值000166  | 
| **GrpPolicyNo**        | varchar   | 64     | 否     | 否       | 无     | 团体保单号  |
| **GPFlag**             | varchar   | 4      | 是     | 否       | 无     | 团个性质（01=个险,02=团险,99=其他）  | 
| **PolicyNo**           | varchar   | 64     | 是     | 否       | 无     | 个人保单号  | 
| **ProductNo**          | varchar   | 64     | 是     | 否       | 无     | 个单保险险种号码  |
| **PolYear**            | tinyint   | 4      | 是     | 否       | 无     | 保单年度  |
| **ProductCode**        | varchar   | 64     | 是     | 否       | 无     | 产品编码  | 
| **LiabilityCode**      | varchar   | 64     | 是     | 否       | 无     | 责任代码  | et
| **LiabilityName**      | varchar   | 128    | 是     | 否       | 无     | 责任名称  | 
| **GetLiabilityCode**   | varchar   | 64     | 是     | 否       | 无     | 给付责任代码  | 
| **GetLiabilityName**   | varchar   | 128    | 是     | 否       | 无     | 给付责任名称  | 
| **TermType**           | varchar   | 4      | 否     | 否       | 无     | 保险期限类型  | 
| **WDNo**               | varchar   | 4      | 否     | 否       | 无     | 领取序号  | 
| **InsuredNo**          | varchar   | 64     | 是     | 否       | 无     | 被保人客户号  | 
| **InsuredName**        | varchar   | 64     | 是     | 否       | 无     | 被保人姓名  | 
| **InsuredSex**         | varchar   | 4      | 是     | 否       | 无     | 被保人性别  | 
| **InsuredCertType**    | varchar   | 4      | 是     | 否       | 无     | 被保人证件类型  | 
| **InsuredCertNo**      | varchar   | 64     | 是     | 否       | 无     | 被保人证件编码  | 
| **OccupationType**     | varchar   | 12     | 是     | 否       | 无     | 职业代码  | 
| **AppntAge**           | tinyint   | 4      | 否     | 否       | 无     | 投保年龄   | 
| **PreAge**             | tinyint   | 4      | 否     | 否       | 无     | 当前年龄  |
| **BenefitDate**        | date      |        | 是     | 否       | 无     | 给付日期  |
| **SaparateFlag**       | varchar   | 4      | 是     | 否       | 无     | 分出标记（0=未达到溢额线保单,1=分出保单）  |
| **BenefitClass**       | varchar   | 12     | 否     | 否       | 无     | 生存金类型  |
| **BenefitAmount**      | decimal   | 20     | 否     | 否       | 无     | 生存金领取金额  |
| **EnterAccDate**       | date      |        | 是     | 否       | 无     | 到账日期  | 
| **ReInsuranceContNo**  | varchar   | 64     | 是     | 否       | 无     | 再保险合同号码  | 
| **ReinsurerCode**      | varchar   | 64     | 是     | 否       | 无     | 再保险公司代码  | 
| **ReinsurerName**      | varchar   | 256    | 是     | 否       | 无     | 再保险公司名称  | 
| **ReinsurMode**        | varchar   | 4      | 是     | 否       | 无     | 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔）  |
| **BackBenefitAmount**  | date      |        | 否     | 否       | 无     | 摊回金额  |
| **BackDate**           | date      |        | 否     | 否       | 无     | 摊回日期  |
| **Currency**           | varchar   | 4      | 是     | 否       | 无     | 货币代码  | 
| **ReComputationsDate** | date      |        | 否     | 否       | 无     | 分保计算日期  | 
| **AccountGetDate**     | date      |        | 否     | 否       | 无     | 账单归属日期  |
| **AccTransNo**         | varchar   | 64     | 是     | 否       | 无     | 所属账单流水号  | 
| **DataSource**         | tinyint   | 4      | 否     | 否       | 无     | 数据来源（0=系统,1=人工）  |
| **PushStatus**         | tinyint   | 4      | 否     | 否       |'0'     | 推送状态（0=未推送,1=已推送）  |
| **PushDate**           | date      |        | 是     | 否       | 无     | 推送日期  |
| **PushBy**             | varchar   | 64     | 是     | 否       | 无     | 推送人    |
| **Remark**             | varchar   | 128    | 是     | 否       | 无     | 备注      |

**（8）TB0008**

| 字段名                           | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |

| **batch_no**                    | varchar  | 50      | 否     | 否       | 无     | 批次号 | 
| **version**                     | tinyint  | 20      | 否     | 否       | 无     | 版本号 | 
| **company_code**                | varchar  | 20      | 是     | 否       | 无     | 再保公司编码 | 
| **contract_code**               | varchar  | 20      | 否     | 否       | 无     | 合同编码 | 
| **contract_no**                 | varchar  | 50      | 是     | 否       | 无     | 合同号 | 
| **contract_name**               | varchar  | 200     | 是     | 否       | 无     | 合同名称 | 
| **contract_abbr**               | varchar  | 100     | 是     | 否       | 无     | 合同简称 | 
| **sign_date**                   | date     |         | 是     | 否       | 无     | 签订日期 | 
| **effective_date**              | date     |         | 是     | 否       | 无     | 生效日期 | 
| **expired_date**                | date     |         | 是     | 否       | 无     | 失效日期 | 
| **contract_type**               | tinyint  | 1       | 是     | 否       | 无     | 合同类型,0=主合同,1=补充协议 | 
| **cedeout_type**                | tinyint  |         | 是     | 否       | 无     | 是否临分,0=否,1=是 | 
| **main_contract_id**            | bigint   |         | 是     | 否       | 无     | 主合同id | 
| **main_contract_code**          | varchar  | 20      | 是     | 否       | 无     | 主合同编码 | 
| **file_name**                   | varchar  | 200     | 是     | 否       | 无     | 电子版文件名称 | 
| **file_path**                   | varchar  | 500     | 是     | 否       | 无     | 电子版合同地址 | 
| **status**                      | tinyint  | 1       | 是     | 否       | 无     | 状态,0=有效,1=无效 | 

**（9）TB0009**

| 字段名                           | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |
| **batch_no**                    | varchar  | 50     | 否     | 否       | 无     | 批次号 |
| **version**                     | tinyint  | 4      | 否     | 否       | 无     | 版本号 |
| **contract_id**                 | bigint   | 20     | 否     | 否       | 无     | 合同Id |
| **contract_code**               | varchar  | 20     | 否     | 否       | 无     | 合同编码 |
| **risk_code**                   | varchar  | 20     | 否     | 否       | 无     | 险种编码 |
| **liability_code**              | varchar  | 20     | 是     | 否       | 无     | 责任编码 |
| **start_date**                  | date     |        | 是     | 否       | 无     | 匹配开始日期 |
| **end_date**                    | date     |        | 是     | 否       | 无     | 匹配结束日期 |
| **cedeout_way**                 | tinyint  | 1      | 是     | 否       | 无     | 分保方式,1:溢额分保, 2:成数分保,3:混合分保|
| **self_amount**                 | decimal  | 10     | 是     | 否       | 无     | 接收份额 |
| **self_ratio**                  | decimal  | 10     | 是     | 否       | 无     | 自留比例 |
| **cedeout_ratio**               | decimal  | 10     | 是     | 否       | 无     | 分出比例 |
| **retention_line**              | varchar  | 100    | 是     | 否       | 无     | 自留额线 |
| **status**                      | tinyint  | 1      | 是     | 否       | 无     | 状态,0:有效,1:无效 |

**（10）TB0010**

| 字段名                           | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |
| **bill_no**                     | varchar  | 64     | 否     | 否       | 无     | 账单号 | 
| **company_code**                | varchar  | 64     | 否     | 否       | 无     | 再保公司编码 | 
| **company_name**                | varchar  | 256    | 否     | 否       | 无     | 再保公司名称 | 
| **contract_code**               | varchar  | 64     | 否     | 否       | 无     | 原始合同编码 | 
| **contract_name**               | varchar  | 256    | 否     | 否       | 无     | 原始合同名称 | 
| **account_start_date**          | date     |        | 否     | 否       | 无     | 业务起始日期 | 
| **account_end_date**            | date     |        | 否     | 否       | 无     | 业务截止日期 | 
| **actual_start_date**           | date     |        | 否     | 否       | 无     | 账单实际起始日期 | 
| **actual_end_date**             | date     |        | 否     | 否       | 无     | 账单实际截止日期 | 
| **settle_trade_no**             | varchar  | 64     | 是     | 否       | 无     | 结算流水号 | 
| **settle_date**                 | date     |        | 是     | 否       | 无     | 结算日期 | 
| **fund_trade_no**               | varchar  | 64     | 是     | 否       | 无     | 资金交易流水号 | 
| **acc_voucher_no**              | varchar  | 64     | 是     | 否       | 无     | 记账凭证号 | 
| **acc_voucher_date**            | date     |        | 是     | 否       | 无     | 凭证记账日期 | 
| **exchange_rate**               | decimal  | 20     | 否     | 否       | 无     | 结算汇率 | 
| **currency**                    | varchar  | 12     | 否     | 否       | 无     | 贷币代码 | 
| **confirm_status**              | tinyint  | 4      | 否     | 否       | '0'    | 确认状态,0=未确认, 1=已确认 | 
| **confirm_date**                | datetime |        | 是     | 否       | 无     | 确认日期 | 
| **confirmer**                   | varchar  | 64     | 是     | 否       | 无     | 确认人 | 


**（11）TB0011**

| 字段名                           | 数据类型  |   长度  | 允许空  | 唯一索引  | 默认值 | 说明                          |
|---------------------------------| -------- | -----  | ------ | ------- | ------ | ---------------------------- |
| **batch_no**                    | varchar  | 64     | 否     | 否       | 无     | 批次号 | 
| **busi_type**                   | tinyint  | 4      | 否     | 否       | 无     | 业务类型,0=新单,1=续期,2=保全,3=理赔,4=满期,5=失效 | 
| **data_type**                   | tinyint  | 4      | 否     | 否       | 无     | 数据类型,0=分出,1=摊回 | 
| **cont_type**                   | tinyint  | 4      | 否     | 否       | 无     | 保单类型,1=个险,2=团险 | 
| **data_copy**                   | tinyint  | 4      | 否     | 否       | 无     | 数据复制标识,0=原始,1=复制 | 
| **data_group_no**               | varchar  | 64     | 否     | 否       | 无     | 数据分组号 | 
| **busi_occur_date**             | date     |        | 否     | 否       | 无     | 业务发生日期 | 
| **get_date**                    | date     |        | 否     | 否       | 无     | 提数日期 | 
| **get_time**                    | varchar  | 24     | 否     | 否       | 无     | 提数时间 | 
| **account_period**              | int      |11      | 否     | 否       | 无     | 所属账期 | 
| **account_date**                | date     |        | 否     | 否       | 无     | 账单日期 | 
| **grp_cont_no**                 | varchar  | 64     | 否     | 否       | 无     | 集体合同号码 | 
| **cont_no**                     | varchar  | 64     | 否     | 否       | 无     | 个单合同号码 | 
| **pol_no**                      | varchar  | 64     | 否     | 否       | 无     | 险种号 | 
| **main_pol_no**                 | varchar  | 64     | 否     | 否       | 无     | 主险保单险种号 | 
| **sale_chnl**                   | varchar  | 16     | 否     | 否       | 无     | 销售渠道编码 | 
| **sale_chnl_name**              | varchar  | 64     | 否     | 否       | 无     | 销售渠道名称 | 
| **sell_type**                   | varchar  | 16     | 否     | 否       | 无     | 销售方式 | 
| **sell_type_name**              | varchar  | 64     | 否     | 否       | 无     | 销售方式名称 | 
| **sale_com_code**               | varchar  | 64     | 否     | 否       | 无     | 销售机构编码 | 
| **sale_com_name**               | varchar  | 256    | 否     | 否       | 无     | 销售机构名称 | 
| **agent_com_code**              | varchar  | 64     | 否     | 否       | 无     | 代理机构编码 | 
| **agent_com_name**              | varchar  | 256    | 否     | 否       | 无     | 代理机构名称 | 
| **manage_com_code**             | varchar  | 64     | 否     | 否       | 无     | 管理机构编码 | 
| **manage_com_name**             | varchar  | 256    | 否     | 否       | 无     | 管理机构名称 | 
| **cont_make_date**              | date     |        | 否     | 否       | 无     | 保单创建日期 | 
| **sign_date**                   | date     |        | 否     | 否       | 无     | 签单日期 | 
| **sign_time**                   | varchar  | 24     | 否     | 否       | 无     | 签单时间 | 
| **cont_year**                   | int      | 11     | 否     | 否       | 无     | 保单年度 | 
| **cont_month**                  | tinyint  | 4      | 否     | 否       | '0'    | 分出月份 | 
| **cont_anniversary**            | date     |        | 否     | 否       | 无     | 保单周年日 | 
| **previous_cont_anniversary**   | date     |        | 否     | 否       | 无     | 上一保单周年日 | 
| **cont_app_flag**               | tinyint  | 4      | 否     | 否       | 无     | 保单状态,1=有效,4=失效 | 
| **invalid_state_type**          | varchar  | 64     | 否     | 否       | 无     | 终止类型 | 
| **invalid_state_reason**        | varchar  | 16     | 否     | 否       | 无     | 终止原因 | 
| **invalid_start_date**          | date     |        | 否     | 否       | 无     | 终止开始日期 | 
| **risk_code**                   | varchar  | 64     | 否     | 否       | 无     | 险种编码 | 
| **risk_name**                   | varchar  | 128    | 否     | 否       | 无     | 险种名称 | 
| **risk_app_flag**               | tinyint  | 4      | 否     | 否       | 无     | 险种状态,1=有效,4=失效 | 
| **risk_vali_date**              | date     |        | 否     | 否       | 无     | 生效日期 | 
| **risk_end_date**               | date     |        | 否     | 否       | 无     | 保险终止日 | 
| **sub_risk_flag**               | varchar  | 16     | 否     | 否       | 无     | 主附险标识,M=主险,S=附加险 | 
| **risk_period**                 | varchar  | 16     | 否     | 否       | 无     | 一年期险种标志,L=长险,M=一年期险,S=极短期险 | 
| **risk_type3**                  | varchar  | 16     | 否     | 否       | 无     | 险种类别,1=传统险,2=分红险,3=投连险,4=万能险 | 
| **plan_code**                   | varchar  | 32     | 否     | 否       | 无     | 保障计划 | 
| **pol_risk_type**               | varchar  | 8      | 否     | 否       | 无     | 风险类型 | 
| **get_duty_codes**              | varchar  | 128    | 否     | 否       | 无     | 给付责任编码,多个之间用逗号分隔 | 
| **get_duty_names**              | varchar  | 512    | 否     | 否       | 无     | 给付责任名称,多个之间用逗号分隔 | 
| **liability_code**              | varchar  | 64     | 否     | 否       | 无     | 再保责任编码 | 
| **liability_name**              | varchar  | 128    | 否     | 否       | 无     | 再保责任名称 | 
| **insu_year**                   | int      | 11     | 否     | 否       | 无     | 保险期间 | 
| **insu_year_flag**              | varchar  | 16     | 否     | 否       | 无     | 保险期间单位,Y=年, M=月, D=日, A=岁 | 
| **pay_intv**                    | tinyint  | 4      | 否     | 否       | 无     | 缴费频率,0=趸交, 1=月交, 12=年交 | 
| **payend_year**                 | tinyint  | 4      | 否     | 否       | 无     | 缴费期间 | 
| **payend_year_flag**            | varchar  | 16     | 否     | 否       | 无     | 缴费期间单位,Y=年, M=月, D=日, A=岁 | 
| **risk_free_flag**              | tinyint  | 4      | 否     | 否       | '0'    | 险种是否已豁免,0=未豁免,1=已豁免 | 
| **pay_periods**                 | int      | 11     | 否     | 否       | '0'    | 总缴费期数 | 
| **in_pay_periods**              | int      | 11     | 否     | 否       | '0'    | 已缴费期数 | 
| **un_pay_periods**              | int      | 11     | 否     | 否       | '0'    | 未缴费期数 | 
| **un_pay_premium**              | decimal  | 20     | 否     | 否       | '0'    | 应缴未缴保费 | 
| **amount**                      | decimal  | 16     | 否     | 否       | 无     | 保额 | 
| **available_amount**            | decimal  | 16     | 否     | 否       | 无     | 有效保额 | 
| **total_premium**               | decimal  | 16     | 否     | 否       | 无     | 总保费 | 
| **base_premium**                | decimal  | 16     | 否     | 否       | 无     | 基础保费 | 
| **add_premium**                 | decimal  | 16     | 否     | 否       | 无     | 加费 | 
| **add_scale**                   | decimal  | 16     | 否     | 否       | 无     | 加费评点 | 
| **pay_to_date**                 | date     |        | 否     | 否       | 无     | 保费交至日期 | 
| **pay_end_date**                | date     |        | 否     | 否       | 无     | 交费终止日 | 
| **sum_pay_money**               | decimal  | 16     | 否     | 否       | '0'    | 累交保费 | 
| **sum_add_money**               | decimal  | 16     | 否     | 否       | '0'    | 累计加费 | 
| **sum_get_money**               | decimal  | 16     | 否     | 否       | '0'    | 累计领取金额 | 
| **cash_value**                  | decimal  | 20     | 否     | 否       | '0'    | 现金价值 | 
| **insuacc_value**               | decimal  | 20     | 否     | 否       | '0'    | 账户价值 | 
| **appnt_no**                    | varchar  | 64     | 否     | 否       | 无     | 投保人客户号 | 
| **appnt_name**                  | varchar  | 128    | 否     | 否       | 无     | 投保人姓名 | 
| **appnt_id_type**               | varchar  | 64     | 否     | 否       | 无     | 投保人证件类型 | 
| **appnt_id_no**                 | varchar  | 64     | 否     | 否       | 无     | 投保人证件号码 | 
| **appnt_sex**                   | tinyint  | 4      | 否     | 否       | 无     | 投保人性别,0=男,1=女 | 
| **appnt_birthday**              | date     |        | 否     | 否       | 无     | 投保人出生日期 | 
| **appnt_occ_type**              | varchar  | 16     | 否     | 否       | 无     | 投保人职业等级 | 
| **appnt_occ_code**              | varchar  | 24     | 否     | 否       | 无     | 投保人职业代码 | 
| **insured_peoples**             | int      |11      | 否     | 否       | 无     | 被保险人数量 | 
| **insured_no**                  | varchar  | 64     | 否     | 否       | 无     | 被保险人客户号 | 
| **insured_name**                | varchar  | 128    | 否     | 否       | 无     | 被保险人姓名 | 
| **insured_id_type**             | varchar  | 64     | 否     | 否       | 无     | 被保险人证件类型 | 
| **insured_id_no**               | varchar  | 64     | 否     | 否       | 无     | 被保险人证件号码 | 
| **insured_sex**                 | tinyint  | 4      | 否     | 否       | 无     | 被保险人性别,0=男,1=女 | 
| **insured_birthday**            | date     |        | 否     | 否       | 无     | 被保险人出生日期 | 
| **insured_occ_type**            | varchar  | 16     | 否     | 否       | 无     | 被保险人职业等级 | 
| **insured_occ_code**            | varchar  | 24     | 否     | 否       | 无     | 被保险人职业代码 | 
| **insured_socisec**             | tinyint  | 4      | 否     | 否       | 无     | 被保人是否有医保,0否,1=是 | 
| **insured_pass_flag**           | tinyint  | 4      | 否     | 否       | 无     | 被保险人健康状况,4=次标体, 9=标体 | 
| **insured_app_age**             | tinyint  | 4      | 否     | 否       | 无     | 被保险人投保年龄 | 
| **edor_accept_no**              | varchar  | 64     | 否     | 否       | 无     | 保全受理号 | 
| **edor_type**                   | varchar  | 16     | 否     | 否       | 无     | 保全项目编码 | 
| **edor_state**                  | tinyint  | 4      | 否     | 否       | 无     | 保全状态,0=有效 | 
| **edor_get_money**              | decimal  | 16     | 否     | 否       | 无     | 保全补退金额 | 
| **edor_get_interest**           | decimal  | 16     | 否     | 否       | 无     | 保全补退利息 | 
| **edor_app_date**               | datetime |        | 否     | 否       | 无     | 保全申请日期 | 
| **edor_validate**               | datetime |        | 否     | 否       | 无     | 保全生效日期 | 
| **edor_conf_date**              | date     |        | 否     | 否       | 无     | 保全确认日期 | 
| **edor_conf_time**              | varchar  | 24     | 否     | 否       | 无     | 保全确认时间 | 
| **edor_make_date**              | date     |        | 否     | 否       | 无     | 保全创建日期 | 
| **clm_no**                      | varchar  | 64     | 否     | 否       | 无     | 赔案号 | 
| **clm_state**                   | varchar  | 16     | 否     | 否       | 无     | 案件状态 | 
| **clm_give_type**               | varchar  | 16     | 否     | 否       | 无     | 理赔结论代码 | 
| **clm_standpay**                | decimal  | 16     | 否     | 否       | 无     | 理算金额 | 
| **clm_realpay**                 | decimal  | 16     | 否     | 否       | 无     | 赔付金额 | 
| **clm_accident_date**           | date     |        | 否     | 否       | 无     | 事故发生日期 | 
| **clm_acc_date**                | date     |        | 否     | 否       | 无     | 出险日期 | 
| **clm_rpt_date**                | date     |        | 否     | 否       | 无     | 报案日 | 
| **clm_rgt_date**                | date     |        | 否     | 否       | 无     | 立案日 | 
| **clm_case_end_date**           | date     |        | 否     | 否       | 无     | 结案日 | 
| **clm_enter_acc_date**          | date     |        | 否     | 否       | 无     | 赔款给付日期 | 
| **clm_fee_sum**                 | decimal  | 16     | 否     | 否       | 无     | 调查费 | 
| **clm_fee_type**                | varchar  | 64     | 否     | 否       | 无     | 结算业务类型 | 
| **clm_bal_type_desc**           | varchar  | 128    | 否     | 否       | 无     | 结算业务类型名称 | 
| **clm_sub_fee_type**            | varchar  | 64     | 否     | 否       | 无     | 结算业务子类型 | 
| **clm_sub_bal_type_desc**       | varchar  | 128    | 否     | 否       | 无     | 结算业务子类型名称 | 
| **clm_defo_grade**              | varchar  | 64     | 否     | 否       | 无     | 伤残等级编码 | 
| **clm_defo_grade_name**         | varchar  | 128    | 否     | 否       | 无     | 伤残等级 | 
| **clm_defo_type**               | varchar  | 64     | 否     | 否       | 无     | 伤残程度编码 | 
| **clm_defo_name**               | varchar  | 128    | 否     | 否       | 无     | 伤残程度 | 
| **clm_hospital_name**           | varchar  | 512    | 否     | 否       | 无     | 医院名称 | 
| **clm_in_hospital_date**        | date     |        | 否     | 否       | 无     | 住院日期 | 
| **clm_out_hospital_date**       | date     |        | 否     | 否       | 无     | 出院日期 | 
| **clm_accident_reason**         | varchar  | 128    | 否     | 否       | 无     | 出险原因代码,1=意外,2=疾病,9=其它 | 
| **clm_accresult_1**             | varchar  | 32     | 否     | 否       | 无     | 出险结果1编码 | 
| **clm_accresult_2**             | varchar  | 32     | 否     | 否       | 无     | 出险结果2编码 | 
| **clm_accresult_1_name**        | varchar  | 64     | 否     | 否       | 无     | 出险结果1名称 | 
| **clm_accresult_2_name**        | varchar  | 64     | 否     | 否       | 无     | 出险结果2名称 | 
| **clm_make_date**               | date     |        | 否     | 否       | 无     | 理赔创建日期 | 
| **rs_pay_intv**                 | tinyint  | 4      | 否     | 否       | '12'   |再保缴费频率,12=年交 | 
| **calc_status**                 | tinyint  | 4      | 否     | 否       | '0'    | 计算状态,0=未计算,1=计算成功,1=计算失败 | 
| **calc_type**                   | tinyint  | 4      | 否     | 否       | 无     | 计算方式,0=自动,1=手动     | 
| **calc_fail_code**              | tinyint  | 4      | 否     | 否       | 无     | 失败原因,0=缺少险种映射,1=缺少准备金,2=缺少费率,3=缺少合同,4=缺少方案,5=缺少公式 | 
| **cedeout_type**                | tinyint  | 4      | 否     | 否       | 无     | 是否临分,0=否,1=是 | 
| **facultative_way**             | tinyint  | 4      | 否     | 否       | 无     | 临分方式,1=自留分保,2=合同分保,3=自定义方案 | 
| **re_notice_url**               | varchar  | 128    | 否     | 否       | 无     | 临分通知书 | 
| **company_code**                | varchar  | 64     | 否     | 否       | 无     | 再保公司编码 | 
| **company_name**                | varchar  | 256    | 否     | 否       | 无     | 再保公司名称 | 
| **contract_code**               | varchar  | 64     | 否     | 否       | 无     | 再保合约编码 | 
| **contract_name**               | varchar  | 128    | 否     | 否       | 无     | 再保合约名称 | 
| **main_contract_code**          | varchar  | 64     | 否     | 否       | 无     | 再保主合约编码 | 
| **main_contract_name**          | varchar  | 128    | 否     | 否       | 无     | 再保主合约名称 | 
| **programme_code**              | varchar  | 64     | 否     | 否       | 无     | 方案编码 | 
| **programme_name**              | varchar  | 128    | 否     | 否       | 无     | 方案名称 | 
| **programme_self_amount**       | decimal  | 16     | 否     | 否       | 无     | 方案自留额 | 
| **programme_self_scale**        | decimal  | 16     | 否     | 否       | 无     | 方案自留比例 | 
| **cedeout_count**               | tinyint  | 4      | 否     | 否       | 无     | 分出次数 | 
| **cedeout_mode**                | tinyint  | 4      | 否     | 否       | 无     | 分出模式,0=净保费,1=毛保费 | 
| **cedeout_way**                 | tinyint  | 4      | 否     | 否       | 无     | 分出方式,0=溢额,1=成数,2=混合 | 
| **addup_amount_type**           | tinyint  | 4      | 否     | 否       | 无     | 累计风险保额方式,0=不累计,1=责任层累计 | 
| **addup_risk_code**             | varchar  | 64     | 否     | 否       | 无     | 风险保额编码 | 
| **addup_risk_name**             | varchar  | 64     | 否     | 否       | 无     | 风险保额名称 | 
| **init_risk_amount**            | decimal  | 20     | 否     | 否       | 无     | 初始化风险保额 | 
| **occupy_risk_amount**          | decimal  | 20     | 否     | 否       | 无     | 占用风险保额 | 
| **release_risk_amount**         | decimal  | 20     | 否     | 否       | 无     | 释放风险保额 | 
| **cedeout_amount**              | decimal  | 20     | 否     | 否       | 无     | 分出保额 | 
| **self_amount**                 | decimal  | 20     | 否     | 否       | 无     | 自留额 | 
| **accept_copies**               | decimal  | 16     | 否     | 否       | 无     | 接受份额 | 
| **cedeout_scale**               | decimal  | 16     | 否     | 否       | 无     | 分保比例 | 
| **self_scale**                  | decimal  | 16     | 否     | 否       | 无     | 自留比例 | 
| **cedeout_premium**             | decimal  | 16     | 否     | 否       | 无     | 基础再保费 | 
| **cedeout_add_premium**         | decimal  | 16     | 否     | 否       | 无     | 加费再保费 | 
| **cedeout_total_premium**       | decimal  | 16     | 否     | 否       | 无     | 总再保费 | 
| **cedeout_commission**          | decimal  | 16     | 否     | 否       | 无     | 再保佣金 | 
| **tax_rate**                    | decimal  | 16     | 否     | 否       | 无     | 增值税率 | 
| **added_tax**                   | decimal  | 16     | 否     | 否       | 无     | 增值税 | 
| **reserves_type**               | tinyint  | 4      | 否     | 否       | 无     | 准备金类型 | 
| **reserves_id**                 | bigint   | 20     | 否     | 否       | 无     | 准备金因子Id | 
| **reserves**                    | decimal  | 20     | 否     | 否       | 无     | 准备金 | 
| **rate_code**                   | varchar  | 64     | 否     | 否       | 无     | 再保费率编码 | 
| **rate_data_id**                | bigint   | 20     | 否     | 否       | 无     | 再保费率Id | 
| **rate_data_value**             | decimal  | 20     | 否     | 否       | 无     | 再保费率值 | 
| **cedeout_rate_data_value**     | decimal  | 20     | 否     | 否       | 无     | 再保费率值*拆扣费率值 | 
| **com_rate_code**               | varchar  | 64     | 否     | 否       | 无     | 佣金费率编码 | 
| **com_rate_data_id**            | bigint   | 20     | 否     | 否       | 无     | 佣金费率Id | 
| **com_rate_data_value**         | decimal  | 20     | 否     | 否       | 无     | 佣金费率值 | 
| **dis_rate_code**               | varchar  | 64     | 否     | 否       | 无     | 折扣费率编码 | 
| **dis_rate_data_id**            | bigint   | 20     | 否     | 否       | 无     | 折扣费率Id | 
| **dis_rate_data_value**         | decimal  | 20     | 否     | 否       | 无     | 拆扣费率值 | 
| **return_status**               | tinyint  | 4      | 否     | 否       | '0'    | 摊回状态,0=未摊回,1=已摊回,2=已终止 | 
| **return_date**                 | date     |        | 否     | 否       | 无     | 摊回日期 | 
| **src_out_trade_id**            | bigint   | 20     | 否     | 否       | 无     | 源分出记录Id,摊回记录该字段有值 | 
| **return_reason**               | varchar  | 512    | 否     | 否       | 无     | 摊回原因描述,分出记录该字段有值 | 
| **return_premium**              | decimal  | 16     | 否     | 否       | 无     | 退回分保费 | 
| **return_cb_premium**           | decimal  | 16     | 否     | 否       | 无     | 退回次标再保费 | 
| **return_total_premium**        | decimal  | 16     | 否     | 否       | 无     | 退回总保费 | 
| **return_claim_amount**         | decimal  | 16     | 否     | 否       | 无     | 摊回理赔金 | 
| **return_expired_gold**         | decimal  | 16     | 否     | 否       | 无     | 摊回满期金 | 
| **return_commission**           | decimal  | 16     | 否     | 否       | 无     | 退还佣金 | 
| **adjust_status**               | tinyint  | 4      | 否     | 否       | '0'    | 是否调整过,0=否,1=是 | 
| **adjust_date**                 | datetime |        | 否     | 否       | 无     | 调整时间 | 
| **adjuster**                    | varchar  | 64     | 否     | 否       | 无     | 调整人 | 
| **adjust_reason**               | varchar  | 256    | 否     | 否       | 无     | 调整原因 | 
| **adjust_batch_no**             | varchar  | 64     | 否     | 否       | 无     | 调整批次号 | 
| **bill_confirm_status**         | tinyint  | 4      | 否     | 否       | '1'    | 账单确认状态,0=未确认, 1=已确认 | 
| **bill_confirm_date**           | datetime |        | 否     | 否       | 无     | 账单确认日期 | 
| **bill_confirmer**              | varchar  | 64     | 否     | 否       | 无     | 账单确认人 | 
| **bill_no**                     | varchar  | 64     | 否     | 否       | 无     | 账单号 | 
| **status**                      | tinyint  | 4      | 否     | 否       | '0'    | 状态,0=有效,1=无效 | 
| **back_track_data**             | tinyint  | 4      | 否     | 否       | '0'    | 状态,0=正常,1=回溯数据 |



### 2.4 用例列表

| 用例编号   | 用例名称      | 用例描述                   | 模块编号 |
|--------|-----------|------------------------| ------- |
| UC0001 | 生成再保产品信息表 | 将再保产品配置信息转换为保单登记产品信息表  | MD0001  |
| UC0002 | 导入再保产品信息表 | 手功上传文件将保单登记产品信息表导入到系统中 | MD0001  |
| UC0003 | 推送再保产品信息表 | 将单登记产品信息表推送到监管报送平台     | MD0001  |
| UC0004 | 删除再保产品信息表 | 删除再保产品信息表的数据           | MD0001  |

### 2.5 接口清单
| 接口编号   | 接口名称          | 接口描述                   | 模块编号  |
| --------- |---------------- | ------------------------- | -------- |

## 3. 业务概念与术语

| 术语      | 定义   | 业务含义   | 备注   |
| ------- | ---- | ------ | ---- |
| 【通用术语1】 | 【定义】 | 【业务含义】 | 【备注】 |
| 【通用术语2】 | 【定义】 | 【业务含义】 | 【备注】 |

## 4. 功能需求

### 4.1 监管报表及推送

#### 4.1.1 原型图

#### 4.1.1.1 再保产品信息列表页

##### 4.1.1.1.1 原型图


##### 4.1.1.1.2 查询条件


##### 4.1.1.1.3 列表展示


##### 4.1.1.1.4 其他描述


#### 4.1.2 接口功能


#### 4.1.3 功能描述

##### 4.1.3.1 生成报表数据（FN0001）

##### 4.1.3.1.1 功能概要

 将“分保信息维护维”的配置数据及产生再保分出摊回数据转换为保单登记报送的对应报表数据。

##### 4.1.3.1.2 业务总流程
```mermaid
flowchart TD
    A[UC0001: 生成再保产品信息表]
    A[UC0002: 导入再保产品信息表]
    A[UC0003: 推送再保产品信息表]
    A[UC0004: 删除再保产品信息表]
```

##### 4.1.3.1.3 用例描述
##### 4.1.3.1.3.1 生成再保产品信息表(UC0001)
|用例标识 | 用例描述            |
|:-------:| ----------------- |
|类型    | 页面功能             |
|用例名称 | 生成再保产品信息表    |
|功能描述 | 生成再保产品信息表    |
|参与者  | 系统                |
|原型图  |                     |
|关联表  | TB0001,TB0005,TB0006|
|前置用例 | 无                  |

**步骤1.** 查询系统中的产品配置数据
(1)查询SQL：select cc.company_code as ReinsurerCode, rc.company_name as ReinsurerName, l.contract_code as ReInsuranceContNo, cc.contract_name as ReInsuranceContName, cc.contract_abbr as ReInsuranceContTitle, cc.contract_type as ContOrAmendmentType, cc.main_contract_code as MainReInsuranceContNo,
l.risk_code as ProductCode, trl.risk_name as ProductName, trl.sale_chnl as GPFlag, trl.ins_product_type as ProductType, l.liability_code as LiabilityCode, trl.liability_name as LiabilityName, l.cedeout_way as ReinsurMode,
(select group_concat(lp.period_type_name separator';') from t_risk_liability_period lp where lp.is_del=0 and lp.status=0 and lp.risk_code=l.risk_code and lp.liability_code=l.liability_code) as TermType,
l.retention_line as RetentionAmount, format(l.self_ratio * 100, 2) as RetentionPercentage, format(l.cedeout_ratio * 100, 2) as QuotaSharePercentage, format(l.self_amount * 100, 2) as ReinsuranceShare , cc.contract_class as ReInsuranceType
from t_cedeout_contract_liability l inner join t_risk_liability trl on l.risk_code=trl.risk_code and trl.liability_code=l.liability_code inner join t_cedeout_contract cc on cc.contract_code=l.contract_code inner join t_cedeout_company rc on cc.company_code=rc.company_code
where l.is_del=0 and l.status=#{status} and trl.is_del=0 and trl.status=#{status} and l.create_time >= date_format(#{params.startDate}, '%Y-%m-%d') and l.create_time <= date_format(#{params.endDate}, '%Y-%m-%d') order by l.id

**步骤2.** 获取再保产品源数据
(1)再保合同Service接口的实现类中调用步骤1。

**步骤3.** 生成再保产品信息表数据
(1)监管报表信息Service实现类中新增方法，方法名：insertPrpProductData，方法入参为监管报表信息DTO对象
(2)调用步骤2的方法，判断查询到的数据为空逻辑结束。
(3)读取系统参数表的报表报送数据保险公司代码参数。
(4)参照East再保产品信息批量生成流水号的逻辑
(5)部分系统字段赋值
    |     字段名   |          值               |
    | ----------- | -----------------------   |
    | 创建者       | 当前登录者                 |
    | 更新者       | 当前登录者                 |
    | 创建时间     | 当前时间                   |
    | 更新时间     | 当前时间                   |
    | 状态        | 有效                       |
    | 数据来源     | 系统                      |
    | 推送状态     | 未推送                    |
    | 保险机构代码 | 保险公司代码                |
    | 交易编码     | 第4步生成的流水号           |

(6)调用保单登记再保产品信息表Service类的方法将TB0001集合数据批量入库。

##### 4.1.3.1.3.2 导入再保产品信息表(UC0002)
|用例标识 | 用例描述      |
|:-------:|-----------|
|类型    | 页面功能      |
|用例名称 | 导入再保产品信息表 |
|功能描述 | 导入再保产品信息表 |
|参与者  | 系统        |
|原型图  |           |
|关联表  | TB0001    |
|前置用例 | 无         |

要求：优化现有导入逻辑
**步骤1.** 生成流水号
(4)参照East再保产品信息批量生成流水号的逻辑

**步骤1.** 补充部分系统字段赋值
    |     字段名   |          值               |
    | ----------- | -----------------------   |
    | 创建者       | 当前登录者                 |
    | 更新者       | 当前登录者                 |
    | 创建时间     | 当前时间                   |
    | 更新时间     | 当前时间                   |
    | 状态        | 有效                       |
    | 数据来源     | 系统                      |
    | 推送状态     | 未推送                    |
    | 保险机构代码 | 传入的保险机构代码           |
    | 交易编码     | 第2步生成的流水号           |

**步骤2.** 更新监管报表信息表推送状态
(1)参考East再保产品表的导入成功后的逻辑。


##### 4.1.3.1.3.3 推送再保产品信息表(UC0003)
|用例标识 | 用例描述      |
|:-------:|-----------|
|类型    | 页面功能      |
|用例名称 | 推送再保产品信息表 |
|功能描述 | 推送再保产品信息表 |
|参与者  | 系统        |
|原型图  |           |
|关联表  | TB0001    |
|前置用例 | 无         |

要求：优化现有导入逻辑
**步骤1.** 更新监管报表信息表推送状态
(1)参考East再保产品表的导入成功后的逻辑。

##### 4.1.3.1.3.4 删除再保产品信息表(UC0004)
|用例标识 | 用例描述      |
|:-------:|-----------|
|类型    | 页面功能      |
|用例名称 | 删除再保产品信息表 |
|功能描述 | 删除再保产品信息表 |
|参与者  | 系统        |
|原型图  |           |
|关联表  | TB0001    |
|前置用例 | 无         |

要求：优化现有删除功能
**步骤1.** 更新监管报表信息表推送状态
(1)参考East再保产品表的导入成功后的逻辑。