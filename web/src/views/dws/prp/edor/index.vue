<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="交易编码" prop="TransactionNo">
        <el-input
          v-model="queryParams.TransactionNo"
          placeholder="请输入交易编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="保单号" prop="PolicyNo">
        <el-input
          v-model="queryParams.PolicyNo"
          placeholder="请输入个人保单号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="保全号" prop="EdorNo">
        <el-input
          v-model="queryParams.EdorNo"
          placeholder="请输入保全号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被保人姓名" prop="InsuredName">
        <el-input
          v-model="queryParams.InsuredName"
          placeholder="请输入被保人姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="推送状态" prop="PushStatus">
        <el-select v-model="queryParams.PushStatus" placeholder="请选择推送状态" clearable>
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['dws:prp:edor:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['dws:prp:edor:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['dws:prp:edor:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['dws:prp:edor:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="edorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="ID" align="center" prop="Id" />
      <el-table-column label="交易编码" align="center" prop="TransactionNo" />
      <el-table-column label="个人保单号" align="center" prop="PolicyNo" />
      <el-table-column label="保全号" align="center" prop="EdorNo" />
      <el-table-column label="保全类型" align="center" prop="EdorType" />
      <el-table-column label="被保人姓名" align="center" prop="InsuredName" />
      <el-table-column label="责任名称" align="center" prop="LiabilityName" />
      <el-table-column label="保费" align="center" prop="Premium" />
      <el-table-column label="变更后保费" align="center" prop="PremiumChange" />
      <el-table-column label="推送状态" align="center" prop="PushStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.regulator_report_push_status" :value="scope.row.PushStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['dws:prp:edor:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['dws:prp:edor:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改再保保全险种明细对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="交易编码" prop="TransactionNo">
          <el-input v-model="form.TransactionNo" placeholder="请输入交易编码" />
        </el-form-item>
        <el-form-item label="保险机构代码" prop="CompanyCode">
          <el-input v-model="form.CompanyCode" placeholder="请输入保险机构代码" />
        </el-form-item>
        <el-form-item label="个人保单号" prop="PolicyNo">
          <el-input v-model="form.PolicyNo" placeholder="请输入个人保单号" />
        </el-form-item>
        <el-form-item label="个单保险险种号码" prop="ProductNo">
          <el-input v-model="form.ProductNo" placeholder="请输入个单保险险种号码" />
        </el-form-item>
        <el-form-item label="团个性质" prop="GPFlag">
          <el-select v-model="form.GPFlag" placeholder="请选择团个性质">
            <el-option
              v-for="dict in dict.type.prp_gp_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="主险保险险种号码" prop="MainProductNo">
          <el-input v-model="form.MainProductNo" placeholder="请输入主险保险险种号码" />
        </el-form-item>
        <el-form-item label="主附险性质代码" prop="MainProductFlag">
          <el-select v-model="form.MainProductFlag" placeholder="请选择主附险性质代码">
            <el-option
              v-for="dict in dict.type.prp_main_product_flag"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品编码" prop="ProductCode">
          <el-input v-model="form.ProductCode" placeholder="请输入产品编码" />
        </el-form-item>
        <el-form-item label="责任代码" prop="LiabilityCode">
          <el-input v-model="form.LiabilityCode" placeholder="请输入责任代码" />
        </el-form-item>
        <el-form-item label="责任名称" prop="LiabilityName">
          <el-input v-model="form.LiabilityName" placeholder="请输入责任名称" />
        </el-form-item>
        <el-form-item label="业务类型" prop="EventType">
          <el-select v-model="form.EventType" placeholder="请选择业务类型">
            <el-option
              v-for="dict in dict.type.prp_event_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保单年度" prop="PolYear">
          <el-input v-model="form.PolYear" placeholder="请输入保单年度" />
        </el-form-item>
        <el-form-item label="签单日期" prop="SignDate">
          <el-date-picker clearable
            v-model="form.SignDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择签单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保险责任生效日期" prop="EffDate">
          <el-date-picker clearable
            v-model="form.EffDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保险责任生效日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保险责任终止日期" prop="InvalidDate">
          <el-date-picker clearable
            v-model="form.InvalidDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保险责任终止日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保单状态代码" prop="PolStatus">
          <el-select v-model="form.PolStatus" placeholder="请选择保单状态代码">
            <el-option
              v-for="dict in dict.type.prp_cont_pol_duty_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保单险种状态代码" prop="Status">
          <el-select v-model="form.Status" placeholder="请选择保单险种状态代码">
            <el-option
              v-for="dict in dict.type.prp_cont_pol_duty_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="基本保额" prop="BasicSumInsured">
          <el-input v-model="form.BasicSumInsured" placeholder="请输入基本保额" />
        </el-form-item>
        <el-form-item label="风险保额" prop="RiskAmnt">
          <el-input v-model="form.RiskAmnt" placeholder="请输入风险保额" />
        </el-form-item>
        <el-form-item label="保费" prop="Premium">
          <el-input v-model="form.Premium" placeholder="请输入保费" />
        </el-form-item>
        <el-form-item label="临分标记" prop="FacultativeFlag">
          <el-input v-model="form.FacultativeFlag" placeholder="请输入临分标记" />
        </el-form-item>
        <el-form-item label="分出标记" prop="SaparateFlag">
          <el-input v-model="form.SaparateFlag" placeholder="请输入分出标记" />
        </el-form-item>
        <el-form-item label="再保险合同号码" prop="ReInsuranceContNo">
          <el-input v-model="form.ReInsuranceContNo" placeholder="请输入再保险合同号码" />
        </el-form-item>
        <el-form-item label="再保险公司代码" prop="ReinsurerCode">
          <el-input v-model="form.ReinsurerCode" placeholder="请输入再保险公司代码" />
        </el-form-item>
        <el-form-item label="再保险公司名称" prop="ReinsurerName">
          <el-input v-model="form.ReinsurerName" placeholder="请输入再保险公司名称" />
        </el-form-item>
        <el-form-item label="分保方式" prop="ReinsurMode">
          <el-select v-model="form.ReinsurMode" placeholder="请选择分保方式">
            <el-option
              v-for="dict in dict.type.prp_reinsur_mode"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保全号" prop="EdorNo">
          <el-input v-model="form.EdorNo" placeholder="请输入保全号" />
        </el-form-item>
        <el-form-item label="保全类型" prop="EdorType">
          <el-input v-model="form.EdorType" placeholder="请输入保全类型" />
        </el-form-item>
        <el-form-item label="保全日期" prop="EdorDate">
          <el-date-picker clearable
            v-model="form.EdorDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保全日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保全生效日期" prop="EdorValidDate">
          <el-date-picker clearable
            v-model="form.EdorValidDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择保全生效日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保全原因" prop="EdorReason">
          <el-input v-model="form.EdorReason" placeholder="请输入保全原因" />
        </el-form-item>
        <el-form-item label="被保人客户号" prop="InsuredNo">
          <el-input v-model="form.InsuredNo" placeholder="请输入被保人客户号" />
        </el-form-item>
        <el-form-item label="被保人姓名" prop="InsuredName">
          <el-input v-model="form.InsuredName" placeholder="请输入被保人姓名" />
        </el-form-item>
        <el-form-item label="被保人性别" prop="InsuredSex">
          <el-select v-model="form.InsuredSex" placeholder="请选择被保人性别">
            <el-option
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="被保人证件类型" prop="InsuredCertType">
          <el-select v-model="form.InsuredCertType" placeholder="请选择被保人证件类型">
            <el-option
              v-for="dict in dict.type.prp_cert_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="被保人证件编码" prop="InsuredCertNo">
          <el-input v-model="form.InsuredCertNo" placeholder="请输入被保人证件编码" />
        </el-form-item>
        <el-form-item label="职业代码" prop="OccupationType">
          <el-input v-model="form.OccupationType" placeholder="请输入职业代码" />
        </el-form-item>
        <el-form-item label="货币代码" prop="Currency">
          <el-input v-model="form.Currency" placeholder="请输入货币代码" />
        </el-form-item>
        <el-form-item label="所属账单流水号" prop="AccTransNo">
          <el-input v-model="form.AccTransNo" placeholder="请输入所属账单流水号" />
        </el-form-item>
        <el-form-item label="数据来源" prop="DataSource">
          <el-select v-model="form.DataSource" placeholder="请选择数据来源">
            <el-option
              v-for="dict in dict.type.regulator_report_data_source"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="推送状态" prop="PushStatus">
          <el-select v-model="form.PushStatus" placeholder="请选择推送状态">
            <el-option
              v-for="dict in dict.type.regulator_report_push_status"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="Remark">
          <el-input v-model="form.Remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEdor, getEdor, delEdor, addEdor, updateEdor } from "@/api/dws/prp/edor";

export default {
  name: "Edor",
  dicts: ['regulator_report_push_status', 'regulator_report_data_source', 'prp_gp_flag', 'prp_main_product_flag', 'prp_event_type', 'prp_cont_pol_duty_status', 'prp_reinsur_mode', 'sys_user_sex', 'prp_cert_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 再保保全险种明细表格数据
      edorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        TransactionNo: null,
        PolicyNo: null,
        EdorNo: null,
        InsuredName: null,
        PushStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        TransactionNo: [
          { required: true, message: "交易编码不能为空", trigger: "blur" }
        ],
        CompanyCode: [
          { required: true, message: "保险机构代码不能为空", trigger: "blur" }
        ],
        PolicyNo: [
          { required: true, message: "个人保单号不能为空", trigger: "blur" }
        ],
        ProductNo: [
          { required: true, message: "个单保险险种号码不能为空", trigger: "blur" }
        ],
        GPFlag: [
          { required: true, message: "团个性质不能为空", trigger: "change" }
        ],
        MainProductNo: [
          { required: true, message: "主险保险险种号码不能为空", trigger: "blur" }
        ],
        MainProductFlag: [
          { required: true, message: "主附险性质代码不能为空", trigger: "change" }
        ],
        ProductCode: [
          { required: true, message: "产品编码不能为空", trigger: "blur" }
        ],
        LiabilityCode: [
          { required: true, message: "责任代码不能为空", trigger: "blur" }
        ],
        LiabilityName: [
          { required: true, message: "责任名称不能为空", trigger: "blur" }
        ],
        EventType: [
          { required: true, message: "业务类型不能为空", trigger: "change" }
        ],
        PolYear: [
          { required: true, message: "保单年度不能为空", trigger: "blur" }
        ],
        SignDate: [
          { required: true, message: "签单日期不能为空", trigger: "blur" }
        ],
        EffDate: [
          { required: true, message: "保险责任生效日期不能为空", trigger: "blur" }
        ],
        InvalidDate: [
          { required: true, message: "保险责任终止日期不能为空", trigger: "blur" }
        ],
        PolStatus: [
          { required: true, message: "保单状态代码不能为空", trigger: "change" }
        ],
        Status: [
          { required: true, message: "保单险种状态代码不能为空", trigger: "change" }
        ],
        FacultativeFlag: [
          { required: true, message: "临分标记不能为空", trigger: "blur" }
        ],
        SaparateFlag: [
          { required: true, message: "分出标记不能为空", trigger: "blur" }
        ],
        ReInsuranceContNo: [
          { required: true, message: "再保险合同号码不能为空", trigger: "blur" }
        ],
        ReinsurerCode: [
          { required: true, message: "再保险公司代码不能为空", trigger: "blur" }
        ],
        ReinsurerName: [
          { required: true, message: "再保险公司名称不能为空", trigger: "blur" }
        ],
        ReinsurMode: [
          { required: true, message: "分保方式不能为空", trigger: "change" }
        ],
        EdorNo: [
          { required: true, message: "保全号不能为空", trigger: "blur" }
        ],
        EdorType: [
          { required: true, message: "保全类型不能为空", trigger: "blur" }
        ],
        EdorDate: [
          { required: true, message: "保全日期不能为空", trigger: "blur" }
        ],
        EdorValidDate: [
          { required: true, message: "保全生效日期不能为空", trigger: "blur" }
        ],
        EdorReason: [
          { required: true, message: "保全原因不能为空", trigger: "blur" }
        ],
        InsuredNo: [
          { required: true, message: "被保人客户号不能为空", trigger: "blur" }
        ],
        InsuredName: [
          { required: true, message: "被保人姓名不能为空", trigger: "blur" }
        ],
        InsuredSex: [
          { required: true, message: "被保人性别不能为空", trigger: "change" }
        ],
        InsuredCertType: [
          { required: true, message: "被保人证件类型不能为空", trigger: "change" }
        ],
        InsuredCertNo: [
          { required: true, message: "被保人证件编码不能为空", trigger: "blur" }
        ],
        OccupationType: [
          { required: true, message: "职业代码不能为空", trigger: "blur" }
        ],
        Currency: [
          { required: true, message: "货币代码不能为空", trigger: "blur" }
        ],
        AccTransNo: [
          { required: true, message: "所属账单流水号不能为空", trigger: "blur" }
        ],
        DataSource: [
          { required: true, message: "数据来源不能为空", trigger: "change" }
        ],
        PushStatus: [
          { required: true, message: "推送状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询再保保全险种明细列表 */
    getList() {
      this.loading = true;
      listEdor(this.queryParams).then(response => {
        this.edorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        Id: null,
        TransactionNo: null,
        CompanyCode: null,
        GrpPolicyNo: null,
        GrpProductNo: null,
        PolicyNo: null,
        ProductNo: null,
        GPFlag: null,
        MainProductNo: null,
        MainProductFlag: null,
        ProductCode: null,
        LiabilityCode: null,
        LiabilityName: null,
        Classification: null,
        EventType: null,
        PolYear: null,
        RenewalTimes: null,
        TermType: null,
        ManageCom: null,
        SignDate: null,
        EffDate: null,
        InvalidDate: null,
        UWConclusion: null,
        PolStatus: null,
        Status: null,
        BasicSumInsured: null,
        RiskAmnt: null,
        Premium: null,
        AccountValue: null,
        FacultativeFlag: null,
        AnonymousFlag: null,
        WaiverFlag: null,
        WaiverPrem: null,
        FinalCashValue: null,
        FinalLiabilityReserve: null,
        InsuredNo: null,
        InsuredName: null,
        InsuredSex: null,
        InsuredCertType: null,
        InsuredCertNo: null,
        OccupationType: null,
        AppntAge: null,
        PreAge: null,
        ProfessionalFee: null,
        SubStandardFee: null,
        EMRate: null,
        ProjectFlag: null,
        InsurePeoples: null,
        SaparateFlag: null,
        ReInsuranceContNo: null,
        ReinsurerCode: null,
        ReinsurerName: null,
        ReinsurMode: null,
        EdorNo: null,
        EdorType: null,
        EdorDate: null,
        EdorValidDate: null,
        EdorReason: null,
        BasicSumInsuredChange: null,
        RiskAmntChange: null,
        PremiumChange: null,
        RetentionAmount: null,
        ReinsurancePremiumChange: null,
        ReinsuranceCommssionChange: null,
        Currency: null,
        ReComputationsDate: null,
        AccountGetDate: null,
        AccTransNo: null,
        DataSource: null,
        PushStatus: null,
        PushDate: null,
        PushBy: null,
        Remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.Id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加再保保全险种明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const Id = row.Id || this.ids
      getEdor(Id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改再保保全险种明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.Id != null) {
            updateEdor(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEdor(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const Ids = row.Id || this.ids;
      this.$modal.confirm('是否确认删除再保保全险种明细编号为"' + Ids + '"的数据项？').then(function() {
        return delEdor(Ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('dws/prp/edor/export', {
        ...this.queryParams
      }, `edor_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
