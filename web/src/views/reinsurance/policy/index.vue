<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="被保人号" prop="insuredNo">
        <el-input v-model="queryParams.insuredNo" placeholder="请输入被保人号" clearable style="width: 200px;" />
      </el-form-item>
      <el-form-item label="分保公司" prop="companyCode" v-if="showtable==1||showtable==3">
        <el-select v-model="queryParams.companyCode" placeholder="请选择分保公司" clearable filterable size="small" style="width: 200px;">
          <el-option v-for="i in typeList" :label="i.companyName" :value="i.companyCode" :key="i.companyCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="再保方案" prop="programmeCode" v-if="showtable==1||showtable==3">
        <el-select v-model="queryParams.programmeCode" placeholder="请选择再保方案" clearable filterable size="small" style="width: 200px;">
          <el-option v-for="i in programmeList" :label="i.programmeName" :value="i.programmeCode" :key="i.programmeCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="身份证号" prop="insuredIdNo">
        <el-input v-model="queryParams.insuredIdNo" placeholder="请输入被保人身份证号" clearable style="width: 200px;" />
      </el-form-item>
      <el-form-item label="保单号" prop="contNo">
        <el-input v-model="queryParams.contNo" placeholder="请输入保单号" clearable style="width: 200px;" />
      </el-form-item>
      <el-form-item label="业务发生日期" prop="busiOccurDate">
        <el-date-picker v-model="queryParams.busiOccurDate" placeholder="请选择业务发生日期" style="width: 200px" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
      <el-form-item label="账单日期" prop="accountDate" v-if="showtable==1||showtable==3">
        <el-date-picker v-model="queryParams.accountDate" placeholder="请选择账单日期" style="width: 200px" value-format="yyyy-MM-dd" ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8" style="margin-bottom:0px">
      <el-col :span="1.5" style="margin-bottom: -39px;padding-top: 5px;float: right;position: relative;z-index: 1;">
        <!-- <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['monitor:logininfor:export']"
        >导出</el-button> -->
        <el-button type="primary" plain icon="el-icon-upload2" size="mini" @click="handlerImportExcel"
          v-hasPermi="['monitor:logininfor:import']">导入历史数据</el-button>
      </el-col>
    </el-row>
    <el-tabs v-model="insuredCedeoutType" @tab-click="handleClick">
      <el-tab-pane label="分出保单" name="1"></el-tab-pane>
      <el-tab-pane label="未达溢额线保单" name="3"></el-tab-pane>
      <el-tab-pane label="未分出保单" name="2"></el-tab-pane>
    </el-tabs>
    <el-table v-if="showtable==1||showtable==3" ref="tables" v-loading="loading" :data="list">
      <!-- <el-table-column type="selection" width="55" align="center" /> -->
      <el-table-column label="保单号" align="center" prop="contNo" min-width="180"/>
      <el-table-column label="被保人号" align="center" prop="insuredNo"  :show-overflow-tooltip="true" />
      <el-table-column label="被保人身份证号" align="center" prop="insuredIdNo" min-width="150px" :show-overflow-tooltip="true" />
      <el-table-column label="险种" align="center" prop="riskCode" :show-overflow-tooltip="true" min-width="180px">
        <template slot-scope="scope">
          {{scope.row.riskCode}}-{{scope.row.riskName}}
        </template>
      </el-table-column>
      <el-table-column label="责任" align="center" prop="liabilityCode" :show-overflow-tooltip="true" min-width="100px">
        <template slot-scope="scope">
          {{scope.row.liabilityCode}}-{{scope.row.liabilityName}}
        </template>
      </el-table-column>
      <el-table-column label="再保方案号" align="center" prop="programmeCode" min-width="100"/>
      <el-table-column label="分保公司" align="center" prop="companyCode" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          {{scope.row.companyCode}}-{{scope.row.companyName}}
        </template>
      </el-table-column>
      <el-table-column label="交易类型" align="center" prop="busiType" min-width="100" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.formula_reinsurance_project" :value="scope.row.busiType"/>
        </template>
      </el-table-column>
      <el-table-column label="保费(元)" align="center" prop="totalPremium" />
      <el-table-column label="加费(元)" align="center" prop="addPremium" />
      <el-table-column label="基本保额(元)" align="center" prop="amount"  min-width="120px" />
      <el-table-column label="准备金(元)" align="center" prop="reserves" min-width="100"/>
      <el-table-column label="分出保额(元)" align="center" prop="cedeoutAmount" min-width="120px"  />
      <el-table-column label="风险保额(元)" align="center" prop="initRiskAmount"  min-width="120px" />
      <el-table-column label="自留额(元)" align="center" prop="selfAmount" min-width="100"/>
      <el-table-column label="基础分保费(元)" align="center" prop="cedeoutPremium" min-width="120"/>
      <el-table-column label="分出比例" align="center" prop="cedeoutScale" ></el-table-column>
      <el-table-column label="分出费率值" align="center" prop="cedeoutRateDataValue" min-width="120" />
      <!-- <el-table-column label="基础分保佣金" align="center" prop="cedeoutCommission" width="100"/> -->
      <el-table-column label="佣金率" align="center" prop="comRateDataValue" />
      <el-table-column label="加费分出保费(元)" align="center" prop="cedeoutAddPremium" min-width="140"/>
      <!-- <el-table-column label="加费分保佣金" align="center" prop="jiafeifebboyojin" width="120" /> -->
      <el-table-column label="分保份额%(再保公司占比)" align="center" prop="acceptCopies" min-width="180"/>
      <el-table-column label="账单日期" align="center" prop="accountDate" min-width="100"/>
      <el-table-column label="业务日期" align="center" prop="busiOccurDate" min-width="100"/>

      <el-table-column label="摊回日期" align="center" prop="returnDate" min-width="100"/>
      <el-table-column label="退回分保费" align="center" prop="returnPremium" min-width="100"/>
      <el-table-column label="退回次标再保费" align="center" prop="returnCbPremium" min-width="120"/>
      <el-table-column label="摊回理赔金" align="center" prop="returnClaimAmount" min-width="100"/>
      <el-table-column label="摊回满期金" align="center" prop="returnExpiredGold" min-width="100"/>
      <el-table-column label="退还佣金" align="center" prop="returnCommission" min-width="100"/>

    </el-table>
    <el-table v-if="showtable==2" ref="tablesec" v-loading="loading" :data="list" >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="被保人号" align="center" prop="insuredNo"  :show-overflow-tooltip="true" />
      <el-table-column label="被保险人身份证号" align="center" prop="insuredIdNo"  min-width="150px"  :show-overflow-tooltip="true" />
      <el-table-column label="保单号" align="center" prop="contNo" :show-overflow-tooltip="true" min-width="150"/>
      <el-table-column label="险种" align="center" prop="riskCode" :show-overflow-tooltip="true" min-width="180">
        <template slot-scope="scope">
          {{scope.row.riskCode}}-{{scope.row.riskName}}
        </template>
      </el-table-column>
      <el-table-column label="责任" align="center" prop="getDutyCode" :show-overflow-tooltip="true" min-width="100">
        <template slot-scope="scope" v-if="scope.row.getDutyCode">
          {{scope.row.getDutyCode}}-{{scope.row.getDutyName}}
        </template>
      </el-table-column>
      <!-- <el-table-column label="再保方案号" align="center" prop="programmeCode" /> -->
      <!-- <el-table-column label="交易代码" align="center" prop="jiayidaoma" /> -->
      <!-- <el-table-column label="风险保额(元)" align="center" prop="initRiskAmount" min-width="120px"/>
      <el-table-column label="分出保额(元)" align="center" prop="cedeoutAmount" min-width="120px"/>
      <el-table-column label="自留额(元)" align="center" prop="selfAmount" />
      <el-table-column label="保费(元)" align="center" prop="totalPremium" />
      <el-table-column label="加费(元)" align="center" prop="addPremium" />
      <el-table-column label="分保比例" align="center" prop="cedeoutScale" /> -->
      <!-- <el-table-column label="账单日期" align="center" prop="accountDate" min-width="100"/> -->
      <el-table-column label="业务日期" align="center" prop="busiOccurDate" min-width="100"/>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <el-dialog :title="dialog.title" :visible.sync="dialog.visible"
      :close-on-click-modal="false" :width="dialog.width">
      <component :is="dialog.componentsName" v-if="dialog.visible"
        :params="dialog.params" @ok="dialogOk" @cancel="dialogCancel" />
    </el-dialog>
  </div>
</template>

<script>
import { companyList} from "../../../api/reinsurance/company";
import { listProgramme, } from "@/api/reinsurance/programme";
import { listPolicy,listUnCedeoutPolicy } from "@/api/reinsurance/policy";
import ImportDataConfig from '@/views/reinsurance/importDataConfig/import.vue';
export default {
  name: "Policy",
  dicts: [
    'sys_common_companyCode',
    'formula_reinsurance_project',
  ],
  components:{
    ImportDataConfig
  },
  data() {
    return {
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 公共模态框
      dialog: {
        title: '',
        visible: false,
        width: '400px',
        params: null,
        importUrl:null,
        componentsName: null,
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        insuredNo: undefined,
        companyCode: undefined,
        programmeCode: undefined,
        insuredIdNo: undefined,
        contNo: undefined,
        busiOccurDate: undefined,
        accountDate: undefined,
      },
      insuredCedeoutType:'1',
      typeList:[],
      programmeList:[],
      showtable:1,
    };
  },
  created() {
    this.getList();
    this.getcompyList();
    this.getlistProgramme();
  },
  methods: {
    getcompyList(){
      companyList({pageNum:1,pageSize:99999}).then(response => {
        this.typeList = response.rows;
      })
    },
    getlistProgramme(){
      listProgramme({pageNum:1,pageSize:99999}).then(response => {
        this.programmeList = response.rows;
      })
    },
    /** 查询登录日志列表 */
    getList() {
      this.loading = true;
      if(this.insuredCedeoutType == 2){
        listUnCedeoutPolicy({...this.queryParams}).then(response => {
            this.list = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      }else{
        listPolicy({...this.queryParams,insuredCedeoutType:this.insuredCedeoutType}).then(response => {
            this.list = response.rows;
            this.total = response.total;
            this.loading = false;
          }
        );
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.infoId)
      this.multiple = !selection.length
    },
    handleClick(){
       this.showtable = ''
       if (this.insuredCedeoutType==2) this.queryParams.accountDate = ''
      setTimeout(() => {
        this.showtable = this.insuredCedeoutType
      }, 1);
      this.handleQuery()
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('monitor/logininfor/export', {
        ...this.queryParams
      }, `logininfor_${new Date().getTime()}.xlsx`)
    },
    isTure(val){
      return val!==undefined&&val!==null&val!==''
    },
    //导入
    handlerImportExcel() {
      this.dialog = {
        title: '导入历史数据',
        visible: true,
        width: '450px',
        params: {
          url:'importHistoryData',
          limit:10
        },
        componentsName: 'ImportDataConfig',
      }
    },
    // 模态框确定
    dialogOk() {
      this.dialogCancel()
      this.getList()
    },
    // 模态框取消
    dialogCancel() {
      this.dialog = {
        title: '',
        visible: false,
        width: '400px',
        params: null,
        componentsName: null,
      }
    },
  }
};
</script>

