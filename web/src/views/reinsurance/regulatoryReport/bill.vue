<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      label-width="100px"
    >
      <el-form-item label="预提账单标志" prop="ytzdbz">
        <el-select
          v-model="queryParams.ytzdbz"
          placeholder="预提账单标志"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_column_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="再保公司" prop="zbxgsdm">
        <el-select
          v-model="queryParams.zbxgsdm"
          placeholder="再保公司"
          clearable
          filterable
          style="width: 240px"
        >
          <el-option
            v-for="i in companyData"
            :label="i.companyName"
            :value="i.companyCode"
            :key="i.companyCode"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="推送状态" prop="pushStatus">
        <el-select
          v-model="queryParams.pushStatus"
          placeholder="推送状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_push_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="账单编号" prop="zdbh">
        <el-input v-model="queryParams.zdbh"
         placeholder="请输入账单编号"
         clearable
         style="width: 240px;" />
      </el-form-item>
      <el-form-item label="推送日期" prop="pushDate">
        <el-date-picker
          v-model="queryParams.pushDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="推送日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="明细导入状态" prop="importStatus">
        <el-select
          v-model="queryParams.importStatus"
          placeholder="明细导入状态"
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.regulator_report_column_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务发生起期" prop="zdqq">
        <el-date-picker
          v-model="queryParams.zdqq"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="业务发生起期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="业务发生止期" prop="zdzq">
        <el-date-picker
          v-model="queryParams.zdzq"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="date"
          placeholder="业务发生止期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="small"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="small" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px">
      <el-button
        type="success"
        plain
        icon="el-icon-upload2"
        size="small"
        @click="handleImport"
        >人工导入再保账单信息表</el-button
      >
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExport"
        >导出再保账单信息表</el-button
      >
      <el-button
        type="warning"
        plain
        icon="el-icon-download"
        size="small"
        @click="handleExportDetail"
        >导出再保保单明细表</el-button
      >
      <el-button type="primary" plain size="small" @click="handlePush"
        >推送</el-button
      >
    </div>

    <el-table
      v-loading="loading"
      :data="reportList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" align="center" fixed="left" />
      <el-table-column label="序号" type="index" width="80" align="center">
        <template slot-scope="scope">
          <span>{{
            queryParams.pageSize * (queryParams.pageNum - 1) + scope.$index + 1
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="报表名称"
        align="center"
        prop="reportCode"
        width="150px"
      >
        再保账单信息表
      </el-table-column>
      <el-table-column
        label="流水号"
        align="center"
        prop="lsh"
        width="250px"
        show-overflow-tooltips
      />
      <el-table-column
        label="保险机构代码"
        align="center"
        prop="bxjgdm"
        width="120px"
      />
      <el-table-column
        label="保险机构名称"
        align="center"
        prop="bxjgmc"
        width="200px"
        show-overflow-tooltips
      />
      <el-table-column
        label="再保险公司代码"
        align="center"
        prop="zbxgsdm"
        width="150px"
      />
      <el-table-column
        label="再保险公司名称"
        align="center"
        prop="zbxgsmc"
        width="250px"
        show-overflow-tooltip
      />
      <el-table-column
        label="原保险公司代码"
        align="center"
        prop="ybxgsdm"
        width="150px"
      />
      <el-table-column
        label="原保险公司名称"
        align="center"
        prop="ybxgsmc"
        width="200px"
      />
      <el-table-column
        label="再保合同号码"
        align="center"
        prop="zbxhthm"
        width="150px"
      />
      <el-table-column
        label="再保合同名称"
        align="center"
        prop="zbxhtmc"
        width="200px"
        show-overflow-tooltip
      />
      <el-table-column
        label="业务发生起期"
        align="center"
        prop="zdqq"
        width="150px"
      />
      <el-table-column
        label="业务发生止期"
        align="center"
        prop="zdzq"
        width="150px"
      />
      <el-table-column
        label="账单分类"
        align="center"
        prop="zdfl"
        width="120px"
      />
      <el-table-column
        label="账单编号"
        align="center"
        prop="zdbh"
        width="150px"
      />
      <el-table-column
        label="预提账单标志"
        align="center"
        prop="ytzdbz"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_column_flag"
            :value="scope.row.ytzdbz"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="虚拟合同标志"
        align="center"
        prop="xnhtbz"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_column_flag"
            :value="scope.row.xnhtbz"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="分保费"
        align="center"
        prop="fbf"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="退回分保费"
        align="center"
        prop="thfbf"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="分保佣金"
        align="center"
        prop="fbyj"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="退回分保佣金"
        align="center"
        prop="thfbyj"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="摊回退保金"
        align="center"
        prop="thtbj"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="摊回理赔款"
        align="center"
        prop="thlpk"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="摊回满期金"
        align="center"
        prop="thmqj"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="摊回生存金"
        align="center"
        prop="thscj"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="结算状态"
        align="center"
        prop="jszt"
        width="120px"
      />
      <el-table-column
        label="结算日期"
        align="center"
        prop="jsrq"
        width="120px"
      />
      <el-table-column
        label="货币代码"
        align="center"
        prop="hbdm"
        width="120px"
      />
      <el-table-column
        label="结算汇率"
        align="center"
        prop="jshl"
        width="120px"
      />
      <el-table-column
        label="账单交易流水编号"
        align="center"
        prop="zdjylsbh"
        width="150px"
        show-overflow-tooltips
      />
      <el-table-column
        label="采集日期"
        align="center"
        prop="cjrq"
        width="120px"
      />
      <el-table-column
        label="所属年份"
        align="center"
        prop="reportYear"
        width="120px"
      />
      <el-table-column
        label="所属月份"
        align="center"
        prop="reportMonth"
        width="120px"
      />
      <el-table-column
        label="数据来源"
        align="center"
        prop="dataSource"
        width="100px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_data_source"
            :value="scope.row.dataSource"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送状态"
        align="center"
        prop="pushStatus"
        width="100px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_push_status"
            :value="scope.row.pushStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="推送日期"
        align="center"
        prop="pushDate"
        width="120px"
      />
      <el-table-column
        label="推送人"
        align="center"
        prop="pushBy"
        width="120px"
      />
      <el-table-column
        label="明细导入状态"
        align="center"
        prop="importStatus"
        width="120px"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.regulator_report_column_flag"
            :value="scope.row.importStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="备注（说明）"
        align="center"
        prop="remark"
        width="200px"
      />
      <el-table-column label="操作" align="center" width="150px" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleImportDetail(scope.row)"
            size="small"
            style="margin-right: 10px"
            >人工导入明细</el-button
          >
          <el-popconfirm
            title="确认删除该条数据？"
            @confirm="handleDelete(scope.row)"
          >
            <el-button
              :disabled="scope.row.pushStatus == 1"
              type="text"
              style="color: #f56c6c"
              size="small"
              slot="reference"
              >删除</el-button
            >
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <ImportExcel
      :config="importConfig"
      @close="importConfig.visible = false"
      @uploadEnd="uploadEnd"
    />
  </div>
</template>

<script>
import {
  listEastBill,
  deleteEastData,
  pushEastData,
} from "@/api/reinsurance/regulatoryReport.js";
import { companyList } from "@/api/reinsurance/company";
import ImportExcel from "@/components/ImportExcel";

export default {
  name: "BillInformation",
  dicts: [
    "regulator_report_column_flag",
    "regulator_report_push_status",
    "regulator_report_data_source",
  ],
  components: { ImportExcel },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 字典表格数据
      reportList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ytzdbz: undefined,
        zbxgsdm: undefined,
        pushStatus: undefined,
        pushDate: undefined,
        importStatus: undefined,
        zdqq: undefined,
        zdzq: undefined,
        reportYear: undefined,
        zdbh: undefined,
      },
      companyData: [],
      ids: [],
      importVisible: true,
      importConfig: {
        accept: ".xls, .xlsx",
        tips: "仅允许导入xls 、xlsx格式文件。",
        title: "导入账单信息",
        visible: false,
        importUrl: "",
      },
    };
  },
  created() {
    this.queryParams.reportYear = this.$route.query.reportYear;
    this.handleQuery();
    companyList({ pageNum: 1, pageSize: 99999 }).then((response) => {
      this.companyData = response.rows;
    });
  },
  activated() {
    if (this.$route.query.reportYear !== this.queryParams.reportYear) {
      this.queryParams.reportYear = this.$route.query.reportYear;
      this.handleQuery();
    }
  },
  methods: {
    handlePush() {
      if (this.ids.length === 0) {
        this.$message({
          message: "请选择要推送的数据",
          type: "error",
        });
        return;
      }
      let arr = [];
      for (let item of this.ids) {
        if (item.pushStatus === 1 || item.importStatus === 0) {
          this.$message({
            message: "存在已推送或未导入明细的数据，请重新选择！",
            type: "error",
          });
          return;
        } else {
          arr.push(item.id);
        }
      }
      pushEastData(2, arr.join(",")).then((res) => {
        this.$message({
          message: "推送成功",
          type: "success",
        });
        this.getList();
      });
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item);
    },
    uploadEnd(res) {
      this.handleQuery();
      this.importConfig.visible = false;
    },
    handleExportDetail() {
      if (this.ids.length === 0) {
        this.$message({
          message: "请选择要导出的数据",
          type: "error",
        });
        return;
      }
      let arr = [];
      for (let item of this.ids) {
        arr.push(item.id);
      }
      this.download(
        "huida-reinsurance/regulatory/report/export/east/blzbbdmxb/" + arr.join(","),
        {},
        `再保保单明细表_${new Date().getTime()}.xlsx`
      );
    },
    handleImport() {
      this.importConfig.title = "导入再保账单信息";
      this.importConfig.importUrl =
        "/huida-reinsurance/regulatory/report/import/east/2";
      this.importConfig.visible = true;
    },
    handleImportDetail(data) {
      this.importConfig.title = "导入再保保单明细";
      this.importConfig.importUrl = "/huida-reinsurance/regulatory/report/import/east/blzbbdmxb/" + data.id;
      this.importConfig.visible = true;
    },
    handleDelete(data) {
      deleteEastData(2, data.id).then((response) => {
        this.$message({
          message: "删除成功",
          type: "success",
        });
        this.getList();
      });
    },
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listEastBill(this.queryParams).then((response) => {
        this.reportList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    handleExport() {
      this.download(
        "huida-reinsurance/regulatory/report/export/east/2",
        {
          ...this.queryParams,
        },
        `再保账单信息表_${new Date().getTime()}.xlsx`
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
  },
};
</script>
