<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">

      <el-form-item label="日期" prop="calcDate">
        <el-date-picker v-model="queryParams.calcDate" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="批次号" prop="batchNo">
        <el-input v-model="queryParams.batchNo" placeholder="请输入批次号" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['reinsurance:cedeoutLiability:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataCalcStatusList">
      <el-table-column label="序号" align="center" prop="id" min-width="100" />
      <el-table-column label="批次号" align="center" prop="batchNo" min-width="170" />
      <el-table-column label="计算时间" align="center" prop="createTime" min-width="150" />
      <el-table-column label="完成时间" align="center" prop="updateTime" min-width="150" />
      <el-table-column label="计算状态" align="center" prop="progress" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.progress_status" :value="scope.row.progress" />
        </template>
      </el-table-column>
      <el-table-column label="计算结果" align="center" prop="calcResult" min-width="220" />
      <el-table-column label="计算类型" align="center" prop="calcType" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.calc_type" :value="scope.row.calcType" />
        </template>
      </el-table-column>
      <el-table-column label="数据类型" align="center" prop="isBackTrack" min-width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.is_back_track" :value="scope.row.isBackTrack" />
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" prop="executor" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" @click="navAnomalousData(scope.row)" v-if="scope.row.failCount != 0">失败查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { dataCalcStatusList } from "@/api/reinsurance/batchLog.js";

export default {
  name: "DataStatus",
  dicts: ['progress_status', 'calc_type', 'is_back_track'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      dataCalcStatusList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        calcStartTime: null,
        calcEndTime: null,
        batchNo: null,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询分保责任列表 */
    getList() {
      this.loading = true;
      if (this.queryParams.calcDate && this.queryParams.calcDate.length === 2) {
        this.queryParams.calcStartTime = this.queryParams.calcDate[0]
        this.queryParams.calcEndTime = this.queryParams.calcDate[1]
      }else{
        this.queryParams.calcStartTime = null;
        this.queryParams.calcEndTime = null;
      }
      dataCalcStatusList(this.queryParams).then(response => {
        this.dataCalcStatusList = this.formatData(response.rows);
        this.total = response.total;
        this.loading = false;
      });
    },
    formatData(data) {
      data.forEach(item => {
        item.calcResult = '成功' + item.passCount + '条, 失败' + item.failCount + '条'
      })
      return data
    },
    // 跳转
    navAnomalousData(row) {
      this.$router.push({
        path: '/dataProcessing/anomalousData',
        query: {
          batchNo: row.batchNo,
        }
      })
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.queryParams = {
        id: null,
        batchNo: null,
        executeDate: null,
        executeDate: null,
        calcType: null,
        status: 0,
        passCount: null,
        failCount: null,
        executor: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("queryParams");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/huida-reinsurance/reinsurance/data/dataCalcStatusExport', {
        ...this.queryParams
      }, `dataCalcStatus_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
