<template>
  <div>
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small" inline>
      <el-divider content-position="left">再保方案基础信息</el-divider>
      <el-form-item label="虚拟合同编码" prop="virtualCode">
        <el-select v-model="form.virtualCode" @change="virtualCodeChange" :disabled="!isAdd" placeholder="请选择再保合同编码" filterable size="small" style="width:182.5px">
          <el-option v-for="i in virtualContractList" :label="i.virtualCode + ' ' + i.virtualName" :value="i.virtualCode" :key="i.virtualCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="再保方案名称" prop="programmeName">
        <el-input v-model="form.programmeName" :disabled="!isAdd" placeholder="请输入再保方案名称" size="small" style="width:182.5px"/>
      </el-form-item>
      <el-form-item v-if="form.programmeCode&&!isAdd" label="再保方案编码" prop="programmeCode">
        <el-input v-model="form.programmeCode" disabled style="width:182.5px" size="small"/>
      </el-form-item>
      <el-form-item label="分保方案类型" prop="cedeoutType">
        <el-select v-model="form.cedeoutType" :disabled="!isAdd" placeholder="请选择分保方案类型" filterable style="width:182.5px">
          <el-option v-for="i in dict.type.re_cedeout_type" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="再保方案状态" prop="status">
        <el-select v-model="form.status" :disabled="!isAdd" placeholder="请选择再保方案状态" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_programme_status" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.cedeoutCompanyNum" label="分保公司数" prop="cedeoutCompanyNum">
        <el-input v-model="form.cedeoutCompanyNum" disabled style="width:182.5px" size="small"/>
      </el-form-item>
      <el-form-item label="缴费方式" prop="payIntv">
        <el-select v-model="form.payIntv" :disabled="!isAdd" placeholder="请选择交费方式" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_pay_intv" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="分出模式" prop="cedeoutMode">
        <el-select v-model="form.cedeoutMode" :disabled="!isAdd" @change="cedeoutModeChange" placeholder="请选择分出模式" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_cedeout_mode" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="分保方式" prop="cedeoutWay">
        <el-select v-model="form.cedeoutWay" @change="cedeoutWayChange" :disabled="!isAdd" placeholder="请选择分保方式" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_cedeout_way" :label="i.label" :value="+i.value" :disabled="cedeoutWayDisabled(i.value)" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="累计风险编码" prop="addupRiskCode" v-if="form.cedeoutMode!=1">
        <el-select v-model="form.addupRiskCode" :disabled="!isAdd" placeholder="请选择累计风险编码" filterable style="width:182.5px">
          <el-option v-for="i in liabilityCodeList" :label="i.addupRiskCode + ' ' + i.addupRiskName" :value="i.addupRiskCode" :key="i.addupRiskCode"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否累计" prop="addupAmountType" v-if="form.cedeoutMode!=1">
        <el-select v-model="form.addupAmountType" :disabled="!isAdd" placeholder="请选择是否累计" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_addup_amount_type" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="溢额层数" v-if="form.cedeoutWay == 0||form.cedeoutWay == 2" prop="excessLevel">
        <el-select v-model="form.excessLevel" @change="excessLevelChange" :disabled="!isAdd" placeholder="请选择溢额层数" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.re_excess_level" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="是否为回溯方案" prop="backTrackStatus">
        <el-select v-model="form.backTrackStatus" :disabled="!isAdd" placeholder="请选择是否为回溯方案" filterable size="small" style="width:182.5px">
          <el-option v-for="i in dict.type.back_track_status" :label="i.label" :value="+i.value" :key="i.value"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="业务发生区间" prop="busiOccurDate" v-if="form.backTrackStatus == 1">
                <el-date-picker v-model="form.busiOccurDate" :disabled="!isAdd" filterable size="small" type="daterange" unlink-panels  range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期" value-format="yyyy-MM-dd" >
                </el-date-picker>
      </el-form-item>

      <template  v-if="form.cedeoutWay == 0||form.cedeoutWay == 2">
        <el-divider content-position="left">溢额线设置</el-divider>
        <el-table :data="form.excessObj" border size="small">
          <el-table-column label="层级"  align="center" prop="excessLevel"></el-table-column>
          <el-table-column label="数值"  align="center" prop="excessNum">
            <template slot-scope="scope">
              <el-input v-model="scope.row.excessNum" @input="excessNumChange(scope.row)" :disabled="!isAdd" placeholder="请输入溢额线数值" size="small"/>
            </template>
          </el-table-column>
          <el-table-column label="溢额线类型"  align="center" prop="excessType">
            <template slot-scope="scope">
              <el-select v-model="scope.row.excessType" @change="excessTypeChange(scope.row)" :disabled="!isAdd" placeholder="请选择溢额线类型" filterable size="small" style="width:100%">
                <el-option v-for="i in dict.type.re_excess_type" :label="i.label" :value="+i.value" :key="i.value"></el-option>
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </template>

      <template  v-if="form.companyShareRatioList&&!isAdd">
        <el-divider content-position="left">分保份额展示</el-divider>
        <el-table :data="companyShareRatioList" border size="small">
          <el-table-column v-for="(i,index) in form.companyShareRatioList[0]" :label="i" :prop="'company'+index" :key="index" align="center"></el-table-column>
        </el-table>
      </template>
      <template v-if="form.cedeoutWay == 1 || form.cedeoutWay == 2">
        <el-divider content-position="left">{{`自留比例设置（${form.cedeoutWay == 1?'成数':'混合'}）`}}</el-divider>
        <el-form-item  label="自留比例(%)" prop="selfScale">
          <el-input-number v-model="form.selfScale" :disabled="!isAdd" controls-position="right" :min="1" :max="100" size="small"  style="width:182.5px"/>
        </el-form-item>
      </template>
      <el-divider content-position="left">费率佣金率设置</el-divider>
      <el-table :data="form.programmeRateDTOList" border size="small">
        <el-table-column label="再保公司名称"  align="center" prop="companyName"></el-table-column>
        <el-table-column label="分出区域下限"  align="center" prop="minCedeoutAmount" width="200px">
          <template slot-scope="scope">
            <el-input v-model="scope.row.minCedeoutAmount" :disabled="!isAdd" placeholder="请输入分出区域下限" size="small"/>
          </template>
        </el-table-column>
        <el-table-column label="分出区域上限"  align="center" prop="maxCedeoutAmount" width="200px">
          <template slot-scope="scope">
            <el-input v-model="scope.row.maxCedeoutAmount" :disabled="!isAdd" placeholder="请输入分出区域上限" size="small"/>
          </template>
        </el-table-column>
        <el-table-column label="费率编码"  align="center" prop="cedeoutRateCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.cedeoutRateCode" :disabled="!isAdd" placeholder="请选择费率编码" filterable size="small">
              <el-option v-for="i in RateCodeListData['3']" :label="i.rateCode + ' ' + i.rateName" :value="i.rateCode" :key="i.rateCode"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="分保佣金率编码"  align="center" prop="commissionRateCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.commissionRateCode" :disabled="!isAdd" placeholder="请选择分保佣金率编码" filterable size="small">
              <el-option v-for="i in RateCodeListData['1']" :label="i.rateCode + ' ' + i.rateName" :value="i.rateCode" :key="i.rateCode"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="折扣率编码"  align="center" prop="disRateCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.disRateCode" :disabled="!isAdd" placeholder="请选择折扣率编码" filterable size="small">
              <el-option v-for="i in RateCodeListData['2']" :label="i.rateCode + ' ' + i.rateName" :value="i.rateCode" :key="i.rateCode"></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <el-divider content-position="left">险种责任方案设置</el-divider>
      <el-table :data="form.programmeLiabilityDTOList" border size="small" :height="form.programmeLiabilityDTOList.length>5?'245px':null">
        <el-table-column label="险种编码"  align="center" prop="riskCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.riskCode" @change="riskCodeChange(scope.row)" :disabled="!isAdd" value-key='value' placeholder="请选择险种" filterable size="small">
              <el-option v-for="i in dict.type.core_insurance_type" :label="i.value+ '-'+ i.label"  :value="i" :key="i.vaule"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="责任编码"  align="center" prop="liabilityCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.liabilityCode" @change="liabilityCodeChange(scope.row)" :disabled="!isAdd" placeholder="请选择责任编码" value-key='dictValue' filterable size="small">
              <el-option v-for="ii in LiabilitySelectObj[scope.row.riskCode.value]" :label="ii.dictValue + '-' +ii.dictLabel" :value="ii" :key="ii.dictValue"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="有效时间"  align="center" prop="startDate">
          <template slot-scope="scope">
            <el-date-picker v-model="scope.row.startDate" :disabled="!isAdd" value-format="yyyy-MM-dd" type="date" placeholder="开始日期" size="small" style="width:100%"></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="失效时间"  align="center" prop="endDate">
          <template slot-scope="scope">
            <el-date-picker v-model="scope.row.endDate" :disabled="!isAdd" value-format="yyyy-MM-dd" type="date" placeholder="失效时间" size="small" style="width:100%"></el-date-picker>
          </template>
        </el-table-column>
        <el-table-column label="日期匹配类型"  align="center" prop="mateDateColumn" width="170px">
          <template slot-scope="scope">
            <el-select v-model="scope.row.mateDateColumn" :disabled="!isAdd" placeholder="请选择日期匹配类型" filterable size="small">
              <el-option v-for="i in dict.type.re_mate_date_column" :label="i.label" :value="i.value" :key="i.value"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="状态"  align="center" prop="status" width="100px">
          <template slot-scope="scope">
            <el-select v-model="scope.row.status" :disabled="!isAdd" placeholder="请选择状态" filterable size="small">
              <el-option v-for="i in dict.type.re_programme_status" :label="i.label" :value="+i.value" :key="i.value"></el-option>
            </el-select>
          </template>
        </el-table-column>
     
        <el-table-column label="操作"  align="center" width="70px">
          <template slot-scope="scope">
            <el-button @click="addItem()" :disabled="!isAdd" class="el-icon-plus" type="text" size="mini" ></el-button>
            <el-button @click="deleteitem(scope.$index)" v-if="form.programmeLiabilityDTOList.length>1" :disabled="!isAdd" class="el-icon-delete" type="text" size="mini" ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div v-if="isAdd" style="text-align:right;padding-top:5px">
        <el-button @click="addItem()" class="el-icon-plus" type="primary" size="mini" >添加险种</el-button>
      </div>
      <!-- <pagination
        layout="total, prev, pager, next, jumper"
        v-show="form.programmeLiabilityDTOList.length"
        :total="form.programmeLiabilityDTOList.length"
        :page.sync="detailPage.pageNum"
        :limit.sync="detailPage.pageSize"
      /> -->
      <el-divider content-position="left">再保合约设置</el-divider>

      <el-table :data="form.programmeLiabilityContractRelDTOList" 
        :height="form.programmeLiabilityContractRelDTOList.length>5?'245px':null" border size="small">
      
        <el-table-column label="再保公司名称"  align="center" prop="companyName">
          <template slot-scope="scope" v-if="scope.row.companyCode&&scope.row.companyName">
            {{scope.row.companyCode +'-' +scope.row.companyName}}
          </template>
        </el-table-column>
        <el-table-column label="险种名称"  align="center" prop="riskName">
          <template slot-scope="scope" v-if="scope.row.riskCode&&scope.row.riskName">
            {{scope.row.riskCode +'-' +scope.row.riskName}}
          </template>
        </el-table-column>
        <el-table-column label="责任名称"  align="center" prop="liabilityName">
          <template slot-scope="scope" v-if="scope.row.liabilityCode&&scope.row.liabilityName">
            {{scope.row.liabilityCode +'-' +scope.row.liabilityName}}
          </template>
        </el-table-column>
        <el-table-column label="再保合约编码"  align="center" prop="liabilityCode">
          <template slot-scope="scope">
            <el-select v-model="scope.row.contractCode" :disabled="!isAdd" @visible-change='(val)=>{getLiabilityContractlist(val,scope.row)}' placeholder="请选择再保合约编码" filterable size="small">
                <el-option v-for="item in scope.row.LiabilityContractList" :key="item.contractCode" 
                :label="item.contractCode + ' ' + item.contractName" :value="item.contractCode" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </el-form>
    <div class="dialog-footer">
      <el-button @click="cancel" size="small">取 消</el-button>
      <el-button type="primary" @click="submitForm" v-if="isAdd" size="small">确 定</el-button>
    </div>
  </div>
</template>
<script>
import {
  addProgramme,
  getProgramme,
  RateCodeList,
  updateProgramme,
  VirtualCompanyList
} from "@/api/reinsurance/programme";
import {listVirtualContract} from "@/api/reinsurance/virtualContract";
import {getLiabilityCodeList} from "@/api/reinsurance/cedeoutLiability";
import {getLiabilitySelect} from "@/api/reinsurance/riskLiability";
import {getLiabilityContractlist} from "@/api/reinsurance/contractLiability";

export default {
  name: "ProgrammeEdit",
  dicts: [
    're_cedeout_type',
    're_programme_status',
    're_cedeout_mode',
    're_cedeout_way',
    're_addup_amount_type',
    're_pay_intv',
    're_excess_level',
    're_excess_type',
    'core_insurance_type',
    're_mate_date_column',
    'back_track_status',
  ],
  props: {
    params: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      //是否新增
      isAdd: false,
      //表单参数
      form: {
        id: null,
        batchNo: null,
        version: null,
        virtualCode: null,
        programmeCode: null,
        programmeName: null,
        cedeoutType: null,
        addupRiskCode: null,
        cedeoutMode: null,
        cedeoutWay: null,
        cedeoutCompanyNum: null,
        excessLevel: null,
        addupAmountType: null,
        payIntv: null,
        firstExcessType: null,
        firstExcessValue: null,
        secondExcessType: null,
        secondExcessValue: null,
        selfScale: null,
        status: 0,
        remark: null,
        isDel: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        backTrackStatus:null,
        excessObj:[],
        programmeRateDTOList:[],
        programmeLiabilityDTOList:[{
          riskCode:'',
          liabilityCode:'',
          startDate:'',
          endDate:'',
          mateDateColumn:'',
          status:'',
          contractCode:'',
        }],
        programmeLiabilityContractRelDTOList:[
          {
            programmeCode:'',
            programmeName:'',
            companyCode:'',
            companyName:'',
            riskCode:'',
            riskName:'',
            liabilityCode:'',
            liabilityName:'',
            contractCode:'',
            contractName:'',
          }
        ],
      },
      queryParams:{
          riskCode:'',
          liabilityCode:'',
      },
      startBusiOccurDate:null,
      endBusiOccurDate:null,
      busiOccurDate: [],
      // pickerOptions: {
      //     shortcuts: [{
      //       text: '最近一周',
      //       onClick(picker) {
      //         const end = new Date();
      //         const start = new Date();
      //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      //         picker.$emit('pick', [start, end]);
      //       }
      //     }, {
      //       text: '最近一个月',
      //       onClick(picker) {
      //         const end = new Date();
      //         const start = new Date();
      //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      //         picker.$emit('pick', [start, end]);
      //       }
      //     }, {
      //       text: '最近三个月',
      //       onClick(picker) {
      //         const end = new Date();
      //         const start = new Date();
      //         start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      //         picker.$emit('pick', [start, end]);
      //       }
      //     }]
      //   },

      //表单校验
      rules: {
        virtualCode: [
          { required: true, message: "虚拟合同编码不能为空", trigger: "change" }
        ],
        programmeName: [
          { required: true, message: "方案名称不能为空", trigger: "change" }
        ],
        cedeoutType: [
          { required: true, message: "分保方案类型不能为空", trigger: "change" }
        ],
        addupRiskCode: [
          { required: true, message: "累计风险编码不能为空", trigger: "change" }
        ],
        cedeoutMode: [
          { required: true, message: "分出模式不能为空", trigger: "change" }
        ],
        cedeoutWay: [
          { required: true, message: "分出方式不能为空", trigger: "change" }
        ],
        cedeoutCompanyNum: [
          { required: true, message: "分保公司数不能为空", trigger: "change" }
        ],
        excessLevel: [
          { required: true, message: "溢额层数不能为空", trigger: "change" }
        ],
        addupAmountType: [
          { required: true, message: "累计风险保额方式不能为空", trigger: "change" }
        ],
        payIntv: [
          { required: true, message: "缴费方式不能为空", trigger: "change" }
        ],
        selfScale: [
          { required: true, message: "自留比例不能为空", trigger: "blur" }
        ],
        busiOccurDate: [
          { required: true, message: "业务发生区间不能为空", trigger: "change" }
        ],
      },
      //虚拟合约列表
      virtualContractList: [],
      //累计风险编码列表
      liabilityCodeList:[],
      //分保份额展示
      companyShareRatioList:[],
      //费率、折扣率编码、分保佣金率
      RateCodeListData: {},
      //再保责任编码
      LiabilitySelectObj:{},
      //险种编码分页
      detailPage:{
        pageNum:0,
        pageSize:5
      },
    }
  },
  computed: {
    pagedData() {
      const start = (this.detailPage.pageNum - 1) * this.detailPage.pageSize;
      const end = start + this.detailPage.pageSize;
      return this.form.programmeLiabilityDTOList.slice(start, end);
    },
    cedeoutWayDisabled(i){
      return function(i){
        if(this.form.cedeoutMode==1 && i==0){
          return true
        }else{
          return false
        }
      }
    }
  },
  created() {
    if (this.params.optionType === 'add') {
      this.isAdd = true
    } else if (this.params.optionType === 'update') {
      this.isAdd = true
    }
    if (this.params.optionType === 'add') {
      this.reset()
    }
    this.updateEcho()
    this.loadVirtualContractList();
    this.loadRateCodeList();
    this.loadLiabilityCodeList();
    this.loadLiabilitySelect();
    // this.getLiabilityContractlist();
  },
  methods: {

    getLiabilityContractlist(val,row) {
      if(val){
        getLiabilityContractlist(row).then(response => {
          row.LiabilityContractList = response.data;
        });
      }
    },
    //再保责任
    loadLiabilitySelect() {
      getLiabilitySelect([]).then(response => {
        this.LiabilitySelectObj = response.data;
      });
    },
    //累计风险编码
    loadLiabilityCodeList() {
      getLiabilityCodeList().then(response => {
        this.liabilityCodeList = response.data;
      });
    },
    //费率
    loadRateCodeList(){
      RateCodeList().then(response => {
        this.RateCodeListData = response.data
      });
    },
    //虚拟合约
    loadVirtualContractList(){
      listVirtualContract({pageNum:1,pageSize:9999}).then(response => {
        this.virtualContractList = response.rows;
      });
    },
    //虚拟合约下拉框变更
    virtualCodeChange(val){
      this.form.programmeRateDTOList=[]
      VirtualCompanyList(val).then(res=>{
        let minNum = this.form.excessObj.find(i=> i.excessType == 0)?.excessNum
        let maxNum = this.form.excessObj.find(i=> i.excessType == 1)?.excessNum
        res.data.forEach(element => {
          let obj={
            companyCode:element.companyCode,
            companyName:element.companyName,
            maxCedeoutAmount:maxNum||'',
            minCedeoutAmount:minNum||'',
            cedeoutRateCode:'',
            commissionRateCode:'',
            disRateCode:'',
          }
          this.form.programmeRateDTOList.push(obj)
          this.makeProgrammeLiabilityContractRelDTOList()
        });
      })
      
    },
    //分出模式下拉框变更
    cedeoutModeChange(val){
      if(val=='1'){
        this.form.cedeoutWay = ''
        this.form.addupRiskCode = ''
        this.form.addupAmountType = ''
      }
    },
    //分保方式下拉框变更
    cedeoutWayChange(){
      if(this.form.cedeoutWay==1){
        this.form.excessObj = []
        this.form.excessLevel = null
      }
    },
    //溢额层数下拉框变更
    excessLevelChange(val){
      this.form.excessObj = []
      let obj1= { excessLevel:'1', excessNum:'',  excessType:0, }
      let obj2= { excessLevel:'2', excessNum:'99999999', excessType:1, }
      if(val==1){
        this.form.excessObj.push(obj1)
      }else{
        this.form.excessObj.push(obj1)
        this.form.excessObj.push(obj2)
      }
      this.form.excessObj.forEach(row=>{
        this.excessNumChange(row)
      })
    },
    //溢额线input失焦
    excessNumChange(row){
      this.form.programmeRateDTOList.forEach(i=>{
        if(row.excessType==1){
          i.maxCedeoutAmount = row.excessNum
        }else{
          i.minCedeoutAmount = row.excessNum
        }
      })
    },
    //溢额线类型下拉框变更
    excessTypeChange(row){
      if(row.excessType == 1){
        row.excessNum = '99999999'
      }else{
        row.excessNum = '0'
      }
      this.excessNumChange(row)
    },
    //责任编码下拉框变更
    liabilityCodeChange(){
      this.makeProgrammeLiabilityContractRelDTOList()
    },
    //险种编码下拉框变更
    riskCodeChange(row){
      row.liabilityCode=''
      row.liabilityList = []
      this.makeProgrammeLiabilityContractRelDTOList()
    },
    //添加险种编码数据行
    addItem(){
      let obj = {
        riskCode:'',
        liabilityCode:'',
        startDate:'',
        endDate:'',
        mateDateColumn:'',
        status:'',
        contractCode:'',
      }
      this.form.programmeLiabilityDTOList.push(obj)
    },
    //删除险种编码数据行
    deleteitem(index){
      this.form.programmeLiabilityDTOList.splice(index,1)
      this.makeProgrammeLiabilityContractRelDTOList()
    },
    isTrue(val){
      return val!==undefined&&val!==null&&val!==''
    },
    /** 提交按钮 */
    submitForm() {
      if (this.form.busiOccurDate && this.form.busiOccurDate.length === 2) {
                this.form.startBusiOccurDate = this.form.busiOccurDate[0]
                this.form.endBusiOccurDate = this.form.busiOccurDate[1]
      } else {
          this.form.startBusiOccurDate = null;
          this.form.endBusiOccurDate = null;
      }

      if((this.form.backTrackStatus==1) && (this.form.busiOccurDate==undefined)){
        this.$message.error('回溯方案业务发生区间不可为空')
        return
      }
    
      if((this.form.cedeoutWay==0||this.form.cedeoutWay==2)&&this.form.excessObj&&!this.form.excessObj.length){
        this.$message.error('溢额线设置不可为空')
        return
      }
      console.log(this.form.excessObj)
      console.log(this.form.programmeRateDTOList)
      if(this.form.programmeRateDTOList&&!this.form.programmeRateDTOList.length){
        this.$message.error('费率佣金率设置不可为空')
        return
      }
      let flag = false
      for (let index = 0; index < this.form.programmeRateDTOList.length; index++) {
        if(!this.isTrue(this.form.programmeRateDTOList[index].cedeoutRateCode) ||!this.isTrue(this.form.programmeRateDTOList[index].commissionRateCode)){
          flag = true
        }
      }
      if(flag){
        this.$message.error('费率、佣金率不可为空')
        return
      }
      if(this.form.programmeLiabilityDTOList&&!this.form.programmeLiabilityDTOList.length){
        this.$message.error('险种责任方案设置不可为空')
        return
      }
      console.log(this.programmeLiabilityDTOList)
      this.$refs["form"].validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.form))
          if(this.form.excessLevel){
            data.firstExcessType = data.excessObj[0].excessType
            data.firstExcessValue = data.excessObj[0].excessNum
            if(data.excessObj[1]){
              data.secondExcessType = data.excessObj[1].excessType
              data.secondExcessValue = data.excessObj[1].excessNum
            }
            delete data.excessObj
          }
          data.programmeRateDTOList.forEach(i=>{
            i.virtualCode = data.virtualCode
          })
          data.programmeLiabilityDTOList.forEach(i=>{
            i.virtualCode = data.virtualCode
            i.riskName = i.riskCode.label
            i.riskCode = i.riskCode.value
            i.liabilityName = i.liabilityCode.dictLabel
            i.liabilityCode = i.liabilityCode.dictValue
          })
          data.programmeLiabilityContractRelDTOList.forEach(i=>{
            i.programmeCode = data.programmeCode
            i.programmeName = data.programmeName
            i.virtualCode = data.virtualCode
            i.virtualName = data.virtualName
          })
          //新增
          if (this.params.optionType === 'add') {
            addProgramme(data).then(response => {
              this.$modal.msgSuccess("新增成功");
              //刷新列表
              this.$emit('ok', response.data)
            })
          }
          //修改
          if (this.params.optionType === 'update') {
            updateProgramme(data).then(response => {
              this.$modal.msgSuccess("修改成功");
              //刷新列表
              this.$emit('ok')
            })
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.$emit('cancel')
    },
    //修改数据回显
    updateEcho() {
      if (this.params.optionType === 'update' || this.params.optionType === 'detail') {
        let id = this.params.id
        getProgramme(id).then(response => {
          let form  = response.data;
          form.excessObj = []
          if(form.startBusiOccurDate===null&&form.endBusiOccurDate===null){
              form.busiOccurDate = []
          }else{
            form.busiOccurDate = [form.startBusiOccurDate,form.endBusiOccurDate]
          }

          if(form.firstExcessType!=null&&form.firstExcessType!=undefined){
            let obj= { excessLevel:'1', excessNum:form.firstExcessValue,  excessType:form.firstExcessType, }
            form.excessObj.push(obj)
          }
          if(form.secondExcessType!=null&&form.secondExcessType!=undefined){
            let obj= { excessLevel:'2', excessNum:form.secondExcessValue,  excessType:form.secondExcessType, }
            form.excessObj.push(obj)
          }
          form.programmeLiabilityDTOList.forEach(i=>{
            i.riskCode = {
              value:i.riskCode,
              label:i.riskName,
            }
            i.liabilityCode = {
              dictValue:i.liabilityCode,
              dictLabel:i.liabilityName,
            }
          })
          this.companyShareRatioList = []
          form.companyShareRatioList&&form.companyShareRatioList.forEach((i,index)=>{
            if(index>0){
              let obj ={}
              i.forEach((c,dindex)=>{
                obj['company'+dindex] =c
              })
              this.companyShareRatioList.push(obj)
            }
          })
          if(form.selfScale)form.selfScale = form.selfScale*100

          if(form.programmeLiabilityContractRelDTOList.length>0){
            form.programmeLiabilityContractRelDTOList.forEach(i=>{
              i.LiabilityContractList = [{
                contractCode:i.contractCode,
                contractName:i.contractName,
              }]
            })
          }
          this.form = form
        })
      }
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        batchNo: null,
        version: null,
        virtualCode: null,
        programmeCode: null,
        programmeName: null,
        cedeoutType: null,
        addupRiskCode: null,
        cedeoutMode: null,
        cedeoutWay: null,
        cedeoutCompanyNum: null,
        excessLevel: null,
        addupAmountType: null,
        payIntv: null,
        firstExcessType: null,
        firstExcessValue: null,
        secondExcessType: null,
        secondExcessValue: null,
        selfScale: null,
        status: 0,
        remark: null,
        isDel: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        busiOccurDate:[],
        startBusiOccurDate:null,
        endBusiOccurDate:null,
        backTrackStatus:null,
        excessObj:[],
        programmeRateDTOList:[],
        programmeLiabilityDTOList:[{
          riskCode:'',
          liabilityCode:'',
          startDate:'',
          endDate:'',
          mateDateColumn:'',
          status:'',
          contractCode:'',
        }],
        programmeLiabilityContractRelDTOList:[{
          programmeCode:'',
          programmeName:'',
          companyCode:'',
          companyName:'',
          riskCode:'',
          riskName:'',
          liabilityCode:'',
          liabilityName:'',
          contractCode:'',
          contractName:'',
        }],
      };
      this.resetForm("form");
    },
    makeProgrammeLiabilityContractRelDTOList(){
      let data = []
      if(this.form.programmeRateDTOList.length>0 && this.form.programmeLiabilityDTOList.length>0){
        this.form.programmeRateDTOList.forEach(rate=>{
          this.form.programmeLiabilityDTOList.forEach(liab=>{
            let obj={
              companyName:rate.companyName,
              companyCode:rate.companyCode,
              riskCode:liab.riskCode?.value,
              riskName:liab.riskCode?.label,
              liabilityCode:liab.liabilityCode?.dictValue,
              liabilityName:liab.liabilityCode?.dictLabel,
              LiabilityContractList:[],
            }
            data.push(obj)
          })
        })
      }
      this.form.programmeLiabilityContractRelDTOList.forEach(i=>{
        data.forEach(d=>{
          if(i.riskCode == d.riskCode && i.liabilityCode == d.liabilityCode){
            d.contractCode = i.contractCode
            d.LiabilityContractList = i.LiabilityContractList
          }else{
            d.LiabilityContractList = []
          }
        })
      })
      console.log(data)
      this.form.programmeLiabilityContractRelDTOList = data
    },
  }
}
</script>
<style scoped>
::v-deep .el-form-item {
  margin-bottom: 15px;
}
::v-deep .el-form-item__error {
  margin-top: 1px;
}
.el-divider--horizontal {
  margin: 22px 0 15px
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 30px;
}
</style>
