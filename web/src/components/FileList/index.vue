<template>
  <div style="padding-bottom: 20px">

    <el-form :model="queryParams" size="small" :inline="true">
      <el-form-item label="文件名" prop="originalFilename">
        <el-input v-model="queryParams.originalFilename" placeholder="请输入" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 文件拖拽上传 -->
    <el-upload action="" ref="upload" :http-request="customUpload" :show-file-list="false" multiple>
      <el-button size="mini" type="primary" :loading="uploadLoading">点击上传</el-button>
    </el-upload>

    <!-- 文件列表 -->
    <el-table :data="tableData" style="width: 100%;margin-top: 10px;" size="small">
      <el-table-column align="center" prop="originalFilename" label="文件名" show-overflow-tooltip />
      <el-table-column align="center" prop="size" label="文件大小" />
      <el-table-column label="操作" align="center" width="150">
        <template slot-scope="{row}">
          <el-button class="mr10" size="mini" type="text" icon="el-icon-download" @click="handleView(row)"
            :loading="download.fileId === row.fileId && download.loading">下载</el-button>
          <el-popconfirm title="确定删除吗？" @confirm="handleDel(row)">
            <el-button size="mini" type="text" icon="el-icon-delete" slot="reference">删除</el-button>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="loadFileList" />
  </div>
</template>
<script>
import {deleteFileApi, downloadFileApi, getFileListApi, uploadApi} from "@/api/reinsurance/file";
import {downloadFileHandler} from "@/api/tool/file";

export default {
  props: {
    params: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      //表格loading
      loading: false,
      //上传loading
      uploadLoading: false,
      //下载loading
      download: {
        fileId: null,
        loading: false
      },
      total: 0,
      tableData: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        originalFilename: null,
        fileType: null,
        businessCode: null
      }
    }
  },
  watch: {
    'params.businessCode': function (newVal) {
      this.queryParams.businessCode = newVal
    }
  },
  created() {
    this.queryParams.fileType = this.params.fileType
    this.queryParams.businessCode = this.params.businessCode
    //加载文件列表
    this.loadFileList()
  },
  methods: {
    //搜索按钮操作
    handleQuery() {
      this.queryParams.pageNum = 1
      this.loadFileList()
    },
    //重置按钮操作
    resetQuery() {
      this.queryParams.pageNum = 1
      this.queryParams.pageSize = 10
      this.queryParams.originalFilename = null
      this.handleQuery()
    },
    //加载文件列表
    loadFileList() {
      this.loading = true
      getFileListApi(this.queryParams).then(res => {
        this.tableData = res.rows
        this.total = res.total
      }).finally(() => {
        this.loading = false
      })
    },
    //自定义上传
    customUpload(data) {
      let params = {
        file: data.file,
        fileType: this.params.fileType,
        businessCode: this.params.businessCode
      }
      this.uploadLoading = true
      uploadApi(params).then(res => {
        this.$message.success('上传成功')
        this.loadFileList()
      }).finally(() => {
        this.uploadLoading = false
      })
    },
    //删除文件
    handleDel(row) {
      this.loading = true
      deleteFileApi(row.fileId).then(res => {
        this.$message.success('删除成功')
        this.loadFileList()
      })
    },
    //查看文件
    handleView(row) {
      this.download = {fileId: row.fileId, loading: true}
      downloadFileApi(row.fileId).then(res => {
        downloadFileHandler(res, row.originalFilename)
      }).finally(() => {
        this.download = {fileId: null, loading: false}
      })
    }
  }
}
</script>
<style scoped>

</style>
