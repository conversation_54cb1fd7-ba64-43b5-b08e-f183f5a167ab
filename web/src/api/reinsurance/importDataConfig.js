import request from '@/utils/request'

// 查询导入导出数据配置列表
export function listConfig(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/config/list',
    method: 'get',
    params: query
  })
}

// 新增导入导出数据配置
export function addConfig(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/config',
    method: 'post',
    data: data
  })
}

// 修改导入导出数据配置
export function updateConfig(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/config',
    method: 'put',
    data: data
  })
}

// 删除导入导出数据配置
export function delConfig(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/config/' + id,
    method: 'delete'
  })
}

// 导入数据配置
export function importDataConfig(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/importDataConfig',
    method: 'post',
    data: data
  })
}

// 查询导入导出数据配置日志列表
export function listConfigLog(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/log/list',
    method: 'get',
    params: query
  })
}

// 查询导入导出数据配置日志详细列表
export function listConfigLogDetail(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/importData/log/detail/list',
    method: 'get',
    params: query
  })
}

// 验证通过
export function validateSuccess(id) {
  return request({
    url: `/huida-reinsurance/reinsurance/importData/validateSuccess/${id}`,
    method: 'get'
  })
}

// 版本回退
export function validateBack(id) {
  return request({
    url: `/huida-reinsurance/reinsurance/importData/validateBack/${id}`,
    method: 'get'
  })
}
