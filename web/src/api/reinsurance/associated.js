import request from '@/utils/request'

// 修改险种公式配置
export function updateLiability(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/formula/liability',
    method: 'put',
    data: data
  })
}
// 新增险种公式配置
export function addLiability(data) {
  return request({
    url: '/huida-reinsurance/reinsurance/formula/liability',
    method: 'post',
    data: data
  })
}
// 获取险种公式配置详细信息
export function detailLiability(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/formula/liability/'+id,
    method: 'get',
  })
}
// 删除险种公式配置（批量删除）
export function deleteLiability(id) {
  return request({
    url: '/huida-reinsurance/reinsurance/formula/liability/'+id,
    method: 'delete',
  })
}
//险种公式列表
export function listLiability(query) {
  return request({
    url: '/huida-reinsurance/reinsurance/formula/liability/list',
    method: 'get',
    params: query
  })
}