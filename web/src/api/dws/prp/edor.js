import request from '@/utils/request'

// 查询再保保全险种明细列表
export function listEdor(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor/list',
    method: 'get',
    params: query
  })
}

// 查询再保保全险种明细详细
export function getEdor(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor/' + Id,
    method: 'get'
  })
}

// 新增再保保全险种明细
export function addEdor(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor',
    method: 'post',
    data: data
  })
}

// 修改再保保全险种明细
export function updateEdor(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor',
    method: 'put',
    data: data
  })
}

// 删除再保保全险种明细
export function delEdor(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor/' + Id,
    method: 'delete'
  })
}

// 推送再保保全险种明细
export function pushEdor(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor/push',
    method: 'post',
    data: data
  })
}

// 检查再保保全险种明细是否存在
export function existsEdor(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/edor/exists',
    method: 'post',
    data: data
  })
}
