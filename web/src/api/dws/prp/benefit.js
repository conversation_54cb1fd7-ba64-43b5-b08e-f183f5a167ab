import request from '@/utils/request'

// 查询再保生存金险种明细列表
export function listBenefit(query) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit/list',
    method: 'get',
    params: query
  })
}

// 查询再保生存金险种明细详细
export function getBenefit(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit/' + Id,
    method: 'get'
  })
}

// 新增再保生存金险种明细
export function addBenefit(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit',
    method: 'post',
    data: data
  })
}

// 修改再保生存金险种明细
export function updateBenefit(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit',
    method: 'put',
    data: data
  })
}

// 删除再保生存金险种明细
export function delBenefit(Id) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit/' + Id,
    method: 'delete'
  })
}

// 推送再保生存金险种明细
export function pushBenefit(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit/push',
    method: 'post',
    data: data
  })
}

// 检查再保生存金险种明细是否存在
export function existsBenefit(data) {
  return request({
    url: '/huida-reinsurance/dws/prp/benefit/exists',
    method: 'post',
    data: data
  })
}
