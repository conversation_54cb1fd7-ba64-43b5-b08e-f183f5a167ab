<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.FormulaItemMapper">
    
    <resultMap type="FormulaItemEntity" id="FormulaItemResult">
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="itemCode"    column="item_code"    />
        <result property="itemName"    column="item_name"    />
        <result property="itemEn"    column="item_en"    />
        <result property="formulaType"    column="formula_type"    />
        <result property="itemExplain"    column="item_explain"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectFormulaItemVo">
        select id, batch_no, version, item_code, item_name, item_en, formula_type, item_explain, status, remark, is_del, create_by, create_time, update_by, update_time from t_formula_item
    </sql>

    <select id="selectFormulaItemList" parameterType="FormulaItemQuery" resultMap="FormulaItemResult">
        <include refid="selectFormulaItemVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="itemCode != null  and itemCode != ''"> and item_code = #{itemCode}</if>
            <if test="itemName != null  and itemName != ''"> and item_name like concat('%', #{itemName}, '%')</if>
            <if test="itemEn != null  and itemEn != ''"> and item_en = #{itemEn}</if>
            <if test="formulaType != null"> and formula_type = #{formulaType}</if>
            <if test="itemExplain != null  and itemExplain != ''"> and item_explain = #{itemExplain}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
            and is_del = 0
        </where>
    </select>
    
    <select id="selectFormulaItemById" parameterType="Long" resultMap="FormulaItemResult">
        <include refid="selectFormulaItemVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertFormulaItem" parameterType="FormulaItemEntity" useGeneratedKeys="true" keyProperty="id">
        insert into t_formula_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="itemCode != null and itemCode != ''">item_code,</if>
            <if test="itemName != null and itemName != ''">item_name,</if>
            <if test="itemEn != null and itemEn != ''">item_en,</if>
            <if test="formulaType != null">formula_type,</if>
            <if test="itemExplain != null and itemExplain != ''">item_explain,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="itemCode != null and itemCode != ''">#{itemCode},</if>
            <if test="itemName != null and itemName != ''">#{itemName},</if>
            <if test="itemEn != null and itemEn != ''">#{itemEn},</if>
            <if test="formulaType != null">#{formulaType},</if>
            <if test="itemExplain != null and itemExplain != ''">#{itemExplain},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateFormulaItem" parameterType="FormulaItemEntity">
        update t_formula_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="itemCode != null and itemCode != ''">item_code = #{itemCode},</if>
            <if test="itemName != null and itemName != ''">item_name = #{itemName},</if>
            <if test="itemEn != null and itemEn != ''">item_en = #{itemEn},</if>
            <if test="formulaType != null">formula_type = #{formulaType},</if>
            <if test="itemExplain != null and itemExplain != ''">item_explain = #{itemExplain},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="deleteFormulaItemById" parameterType="Long">
        update t_formula_item set is_del = 1 where id = #{id}
    </update>

    <update id="deleteFormulaItemByIds" parameterType="String">
        update t_formula_item set is_del = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>