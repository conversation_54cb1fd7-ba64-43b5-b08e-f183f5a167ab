<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.CedeoutProgrammeRateTrackMapper">
    
    <resultMap type="CedeoutProgrammeRateTrackEntity" id="CedeoutProgrammeRateTrackResult">
        <result property="trackId"    column="track_id"    />
        <result property="id"    column="id"    />
        <result property="batchNo"    column="batch_no"    />
        <result property="version"    column="version"    />
        <result property="companyCode"    column="company_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="virtualCode"    column="virtual_code"    />
        <result property="programmeCode"    column="programme_code"    />
        <result property="minCedeoutAmount"    column="min_cedeout_amount"    />
        <result property="maxCedeoutAmount"    column="max_cedeout_amount"    />
        <result property="cedeoutRateCode"    column="cedeout_rate_code"    />
        <result property="commissionRateCode"    column="commission_rate_code"    />
        <result property="disRateCode"    column="dis_rate_code"    />
        <result property="status"    column="status"    />
        <result property="remark"    column="remark"    />
        <result property="isDel"    column="is_del"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCedeoutProgrammeRateTrackVo">
        select track_id, id, batch_no, version, company_code, company_name, virtual_code, programme_code, min_cedeout_amount, max_cedeout_amount, cedeout_rate_code, commission_rate_code, dis_rate_code, status, remark, is_del, create_by, create_time, update_by, update_time from t_cedeout_programme_rate_track
    </sql>

    <select id="selectCedeoutProgrammeRateTrackList" parameterType="CedeoutProgrammeRateTrackQuery" resultMap="CedeoutProgrammeRateTrackResult">
        <include refid="selectCedeoutProgrammeRateTrackVo"/>
        <where>  
            <if test="batchNo != null  and batchNo != ''"> and batch_no = #{batchNo}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="virtualCode != null  and virtualCode != ''"> and virtual_code = #{virtualCode}</if>
            <if test="programmeCode != null  and programmeCode != ''"> and programme_code = #{programmeCode}</if>
            <if test="minCedeoutAmount != null "> and min_cedeout_amount = #{minCedeoutAmount}</if>
            <if test="maxCedeoutAmount != null "> and max_cedeout_amount = #{maxCedeoutAmount}</if>
            <if test="cedeoutRateCode != null  and cedeoutRateCode != ''"> and cedeout_rate_code = #{cedeoutRateCode}</if>
            <if test="commissionRateCode != null  and commissionRateCode != ''"> and commission_rate_code = #{commissionRateCode}</if>
            <if test="disRateCode != null  and disRateCode != ''"> and dis_rate_code = #{disRateCode}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="isDel != null "> and is_del = #{isDel}</if>
        </where>
    </select>
    
    <select id="selectCedeoutProgrammeRateTrackByTrackId" parameterType="Long" resultMap="CedeoutProgrammeRateTrackResult">
        <include refid="selectCedeoutProgrammeRateTrackVo"/>
        where track_id = #{trackId}
    </select>
        
    <insert id="insertCedeoutProgrammeRateTrack" parameterType="CedeoutProgrammeRateTrackEntity" useGeneratedKeys="true" keyProperty="trackId">
        insert into t_cedeout_programme_rate_track
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="batchNo != null and batchNo != ''">batch_no,</if>
            <if test="version != null">version,</if>
            <if test="companyCode != null and companyCode != ''">company_code,</if>
            <if test="companyName != null and companyName != ''">company_name,</if>
            <if test="virtualCode != null and virtualCode != ''">virtual_code,</if>
            <if test="programmeCode != null and programmeCode != ''">programme_code,</if>
            <if test="minCedeoutAmount != null">min_cedeout_amount,</if>
            <if test="maxCedeoutAmount != null">max_cedeout_amount,</if>
            <if test="cedeoutRateCode != null and cedeoutRateCode != ''">cedeout_rate_code,</if>
            <if test="commissionRateCode != null and commissionRateCode != ''">commission_rate_code,</if>
            <if test="disRateCode != null">dis_rate_code,</if>
            <if test="status != null">status,</if>
            <if test="remark != null">remark,</if>
            <if test="isDel != null">is_del,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="batchNo != null and batchNo != ''">#{batchNo},</if>
            <if test="version != null">#{version},</if>
            <if test="companyCode != null and companyCode != ''">#{companyCode},</if>
            <if test="companyName != null and companyName != ''">#{companyName},</if>
            <if test="virtualCode != null and virtualCode != ''">#{virtualCode},</if>
            <if test="programmeCode != null and programmeCode != ''">#{programmeCode},</if>
            <if test="minCedeoutAmount != null">#{minCedeoutAmount},</if>
            <if test="maxCedeoutAmount != null">#{maxCedeoutAmount},</if>
            <if test="cedeoutRateCode != null and cedeoutRateCode != ''">#{cedeoutRateCode},</if>
            <if test="commissionRateCode != null and commissionRateCode != ''">#{commissionRateCode},</if>
            <if test="disRateCode != null">#{disRateCode},</if>
            <if test="status != null">#{status},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCedeoutProgrammeRateTrack" parameterType="CedeoutProgrammeRateTrackEntity">
        update t_cedeout_programme_rate_track
        <trim prefix="SET" suffixOverrides=",">
            <if test="id != null">id = #{id},</if>
            <if test="batchNo != null and batchNo != ''">batch_no = #{batchNo},</if>
            <if test="version != null">version = #{version},</if>
            <if test="companyCode != null and companyCode != ''">company_code = #{companyCode},</if>
            <if test="companyName != null and companyName != ''">company_name = #{companyName},</if>
            <if test="virtualCode != null and virtualCode != ''">virtual_code = #{virtualCode},</if>
            <if test="programmeCode != null and programmeCode != ''">programme_code = #{programmeCode},</if>
            <if test="minCedeoutAmount != null">min_cedeout_amount = #{minCedeoutAmount},</if>
            <if test="maxCedeoutAmount != null">max_cedeout_amount = #{maxCedeoutAmount},</if>
            <if test="cedeoutRateCode != null and cedeoutRateCode != ''">cedeout_rate_code = #{cedeoutRateCode},</if>
            <if test="commissionRateCode != null and commissionRateCode != ''">commission_rate_code = #{commissionRateCode},</if>
            <if test="disRateCode != null">dis_rate_code = #{disRateCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="isDel != null">is_del = #{isDel},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where track_id = #{trackId}
    </update>

    <delete id="deleteCedeoutProgrammeRateTrackByTrackId" parameterType="Long">
        delete from t_cedeout_programme_rate_track where track_id = #{trackId}
    </delete>

    <delete id="deleteCedeoutProgrammeRateTrackByTrackIds" parameterType="String">
        delete from t_cedeout_programme_rate_track where track_id in 
        <foreach item="trackId" collection="array" open="(" separator="," close=")">
            #{trackId}
        </foreach>
    </delete>
</mapper>