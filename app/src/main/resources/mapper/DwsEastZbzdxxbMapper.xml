<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsEastZbzdxxbMapper">
    
    <resultMap type="DwsEastZbzdxxbEntity" id="DwsEastZbzdxxbResult">
        <result property="id"    column="ID"    />
        <result property="lsh"    column="LSH"    />
        <result property="bxjgdm"    column="BXJGDM"    />
        <result property="bxjgmc"    column="BXJGMC"    />
        <result property="zdfl"    column="ZDFL"    />
        <result property="zdbh"    column="ZDBH"    />
        <result property="ytzdbz"    column="YTZDBZ"    />
        <result property="zdqq"    column="ZDQQ"    />
        <result property="zdzq"    column="ZDZQ"    />
        <result property="zbxgsdm"    column="ZBXGSDM"    />
        <result property="zbxgsmc"    column="ZBXGSMC"    />
        <result property="ybxgsdm"    column="YBXGSDM"    />
        <result property="ybxgsmc"    column="YBXGSMC"    />
        <result property="xnhtbz"    column="XNHTBZ"    />
        <result property="zbxhthm"    column="ZBXHTHM"    />
        <result property="zbxhtmc"    column="ZBXHTMC"    />
        <result property="fbf"    column="FBF"    />
        <result property="fbyj"    column="FBYJ"    />
        <result property="thfbf"    column="THFBF"    />
        <result property="thfbyj"    column="THFBYJ"    />
        <result property="thtbj"    column="THTBJ"    />
        <result property="thlpk"    column="THLPK"    />
        <result property="thmqj"    column="THMQJ"    />
        <result property="thscj"    column="THSCJ"    />
        <result property="jszt"    column="JSZT"    />
        <result property="jsrq"    column="JSRQ"    />
        <result property="hbdm"    column="HBDM"    />
        <result property="jshl"    column="JSHL"    />
        <result property="zdjylsbh"    column="ZDJYLSBH"    />
        <result property="cjrq"    column="CJRQ"    />
        <result property="sjbspch"    column="SJBSPCH"    />
        <result property="managecom"    column="MANAGECOM"    />
        <result property="settleBillNo"    column="SETTLE_BILL_NO"    />
        <result property="importStatus"    column="IMPORT_STATUS"    />
        <result property="reportYear"    column="REPORT_YEAR"    />
        <result property="reportMonth"    column="REPORT_MONTH"    />
        <result property="accountPeriod"    column="ACCOUNT_PERIOD"    />
        <result property="dataSource"    column="DATA_SOURCE"    />
        <result property="pushStatus"    column="PUSH_STATUS"    />
        <result property="pushDate"    column="PUSH_DATE"    />
        <result property="pushBy"    column="PUSH_BY"    />
        <result property="remark"    column="REMARK"    />
        <result property="isDel"    column="IS_DEL"    />
        <result property="createBy"    column="CREATE_BY"    />
        <result property="createTime"    column="CREATE_TIME"    />
        <result property="updateBy"    column="UPDATE_BY"    />
        <result property="updateTime"    column="UPDATE_TIME"    />
    </resultMap>

    <sql id="selectDwsEastZbzdxxbVo">
        select ID, LSH, BXJGDM, BXJGMC, ZDFL, ZDBH, YTZDBZ, ZDQQ, ZDZQ, ZBXGSDM, ZBXGSMC, YBXGSDM, YBXGSMC, XNHTBZ, ZBXHTHM, ZBXHTMC, FBF, FBYJ, THFBF, THFBYJ, THTBJ, THLPK, THMQJ, THSCJ, JSZT, JSRQ, HBDM, JSHL, ZDJYLSBH, CJRQ, SJBSPCH, MANAGECOM, SETTLE_BILL_NO, IMPORT_STATUS, REPORT_YEAR, REPORT_MONTH, ACCOUNT_PERIOD, DATA_SOURCE, PUSH_STATUS, PUSH_DATE, PUSH_BY, REMARK, IS_DEL, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME from T_DWS_EAST_ZBZDXXB
    </sql>

    <select id="selectDwsEastZbzdxxbList" parameterType="DwsEastZbzdxxbQuery" resultMap="DwsEastZbzdxxbResult">
        <include refid="selectDwsEastZbzdxxbVo"/>
        <where>  
            <if test="lsh != null  and lsh != ''"> and LSH = #{lsh}</if>
            <if test="bxjgdm != null  and bxjgdm != ''"> and BXJGDM = #{bxjgdm}</if>
            <if test="bxjgmc != null  and bxjgmc != ''"> and BXJGMC = #{bxjgmc}</if>
            <if test="zdfl != null  and zdfl != ''"> and ZDFL = #{zdfl}</if>
            <if test="zdbh != null  and zdbh != ''"> and ZDBH = #{zdbh}</if>
            <if test="ytzdbz != null  and ytzdbz != ''"> and YTZDBZ = #{ytzdbz}</if>
            <if test="zdqq != null  and zdqq != ''"> and str_to_date(ZDQQ, '%Y%m%d') &gt;= #{zdqq}</if>
            <if test="zdzq != null  and zdzq != ''"> and str_to_date(ZDZQ, '%Y%m%d') &lt;= #{zdzq}</if>
            <if test="zbxgsdm != null  and zbxgsdm != ''"> and ZBXGSDM = #{zbxgsdm}</if>
            <if test="zbxgsmc != null  and zbxgsmc != ''"> and ZBXGSMC = #{zbxgsmc}</if>
            <if test="ybxgsdm != null  and ybxgsdm != ''"> and YBXGSDM = #{ybxgsdm}</if>
            <if test="ybxgsmc != null  and ybxgsmc != ''"> and YBXGSMC = #{ybxgsmc}</if>
            <if test="xnhtbz != null  and xnhtbz != ''"> and XNHTBZ = #{xnhtbz}</if>
            <if test="zbxhthm != null  and zbxhthm != ''"> and ZBXHTHM = #{zbxhthm}</if>
            <if test="zbxhtmc != null  and zbxhtmc != ''"> and ZBXHTMC = #{zbxhtmc}</if>
            <if test="fbf != null "> and FBF = #{fbf}</if>
            <if test="fbyj != null "> and FBYJ = #{fbyj}</if>
            <if test="thfbf != null "> and THFBF = #{thfbf}</if>
            <if test="thfbyj != null "> and THFBYJ = #{thfbyj}</if>
            <if test="thtbj != null "> and THTBJ = #{thtbj}</if>
            <if test="thlpk != null "> and THLPK = #{thlpk}</if>
            <if test="thmqj != null "> and THMQJ = #{thmqj}</if>
            <if test="thscj != null "> and THSCJ = #{thscj}</if>
            <if test="jszt != null  and jszt != ''"> and JSZT = #{jszt}</if>
            <if test="jsrq != null  and jsrq != ''"> and JSRQ = #{jsrq}</if>
            <if test="hbdm != null  and hbdm != ''"> and HBDM = #{hbdm}</if>
            <if test="jshl != null "> and JSHL = #{jshl}</if>
            <if test="zdjylsbh != null  and zdjylsbh != ''"> and ZDJYLSBH = #{zdjylsbh}</if>
            <if test="cjrq != null  and cjrq != ''"> and CJRQ = #{cjrq}</if>
            <if test="sjbspch != null  and sjbspch != ''"> and SJBSPCH = #{sjbspch}</if>
            <if test="managecom != null  and managecom != ''"> and MANAGECOM = #{managecom}</if>
            <if test="settleBillNo != null and settleBillNo != ''"> and SETTLE_BILL_NO = #{settleBillNo}</if>
            <if test="importStatus != null "> and IMPORT_STATUS = #{importStatus}</if>
            <if test="reportYear != null "> and REPORT_YEAR = #{reportYear}</if>
            <if test="reportMonth != null "> and REPORT_MONTH = #{reportMonth}</if>
            <if test="accountPeriod != null  and accountPeriod != ''"> and ACCOUNT_PERIOD = #{accountPeriod}</if>
            <if test="dataSource != null "> and DATA_SOURCE = #{dataSource}</if>
            <if test="pushStatus != null "> and PUSH_STATUS = #{pushStatus}</if>
            <if test="pushDate != null "> and PUSH_DATE = #{pushDate}</if>
            <if test="pushBy != null  and pushBy != ''"> and PUSH_BY = #{pushBy}</if>
            and IS_DEL = 0
        </where>
        order by LSH
    </select>
    
    <select id="selectDwsEastZbzdxxbById" parameterType="Long" resultMap="DwsEastZbzdxxbResult">
        <include refid="selectDwsEastZbzdxxbVo"/>
        where ID = #{id}
    </select>
    
    <select id="selectDwsEastZbzdxxbListByIds" parameterType="String" resultMap="DwsEastZbzdxxbResult">
        <include refid="selectDwsEastZbzdxxbVo"/> where IS_DEL = 0 and ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    
    <select id="selectDwsEastZbzdxxbExists" parameterType="DwsEastZbzdxxbQuery" resultType="java.lang.Integer">
        select count(*) from T_DWS_EAST_ZBZDXXB where IS_DEL = 0 and YTZDBZ = #{ytzdbz} and REPORT_YEAR = #{reportYear} and REPORT_MONTH = #{reportMonth}
    </select>
    
    <select id="selectAnnualReportShouldPushStatus" resultType="java.lang.Integer">
		select (case when notPushedCount>=0 and alreadyPushedCount=0 then 0 when notPushedCount>0 and alreadyPushedCount>0 then 1 else 2 end) as PUSH_STATUS 
		from (select ifnull(sum(if(PUSH_STATUS = 0, TOTAL, 0)), 0) as notPushedCount, ifnull(sum(if(PUSH_STATUS = 1, TOTAL, 0)),0) as alreadyPushedCount 
		from (select PUSH_STATUS, count(*) as TOTAL from T_DWS_EAST_ZBZDXXB where IS_DEL=0 and REPORT_YEAR=#{reportYear} group by PUSH_STATUS)v )t 
    </select>
    
    <resultMap type="String" id="DwsEastZbzdxxbLshResult">
        <result property="lsh"    column="LSH"    />
    </resultMap>
    <select id="selectPushBeforeCheckEmpty" resultMap="DwsEastZbzdxxbLshResult">
        select LSH from T_DWS_EAST_ZBZDXXB where id in <foreach item="id" collection="array" open="(" separator="," close=")">#{id}</foreach> 
        and (LSH is null or BXJGDM is null or BXJGMC is null or ZDFL is null or ZDBH is null or YTZDBZ is null or ZDQQ is null or ZDZQ is null or ZBXGSDM is null or ZBXGSMC is null or YBXGSDM is null or YBXGSMC is null or XNHTBZ is null or ZBXHTHM is null or ZBXHTMC is null or FBF is null or FBYJ is null or THFBF is null or THFBYJ is null or THTBJ is null or THLPK is null or THMQJ is null or THSCJ is null or JSZT is null or JSRQ is null or HBDM is null or JSHL is null or ZDJYLSBH is null or CJRQ is null or SJBSPCH is null or MANAGECOM is null)
    </select>
    
    <insert id="insertDwsEastZbzdxxb" parameterType="DwsEastZbzdxxbEntity" useGeneratedKeys="true" keyProperty="id">
        insert into T_DWS_EAST_ZBZDXXB
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="lsh != null and lsh != ''">LSH,</if>
            <if test="bxjgdm != null">BXJGDM,</if>
            <if test="bxjgmc != null">BXJGMC,</if>
            <if test="zdfl != null">ZDFL,</if>
            <if test="zdbh != null">ZDBH,</if>
            <if test="ytzdbz != null">YTZDBZ,</if>
            <if test="zdqq != null">ZDQQ,</if>
            <if test="zdzq != null">ZDZQ,</if>
            <if test="zbxgsdm != null">ZBXGSDM,</if>
            <if test="zbxgsmc != null">ZBXGSMC,</if>
            <if test="ybxgsdm != null">YBXGSDM,</if>
            <if test="ybxgsmc != null">YBXGSMC,</if>
            <if test="xnhtbz != null">XNHTBZ,</if>
            <if test="zbxhthm != null">ZBXHTHM,</if>
            <if test="zbxhtmc != null">ZBXHTMC,</if>
            <if test="fbf != null">FBF,</if>
            <if test="fbyj != null">FBYJ,</if>
            <if test="thfbf != null">THFBF,</if>
            <if test="thfbyj != null">THFBYJ,</if>
            <if test="thtbj != null">THTBJ,</if>
            <if test="thlpk != null">THLPK,</if>
            <if test="thmqj != null">THMQJ,</if>
            <if test="thscj != null">THSCJ,</if>
            <if test="jszt != null">JSZT,</if>
            <if test="jsrq != null">JSRQ,</if>
            <if test="hbdm != null">HBDM,</if>
            <if test="jshl != null">JSHL,</if>
            <if test="zdjylsbh != null">ZDJYLSBH,</if>
            <if test="cjrq != null">CJRQ,</if>
            <if test="sjbspch != null">SJBSPCH,</if>
            <if test="managecom != null">MANAGECOM,</if>
            <if test="settleBillNo != null">SETTLE_BILL_NO,</if>
            <if test="importStatus != null">IMPORT_STATUS,</if>
            <if test="reportYear != null">REPORT_YEAR,</if>
            <if test="reportMonth != null ">REPORT_MONTH,</if>
            <if test="accountPeriod != null">ACCOUNT_PERIOD,</if>
            <if test="dataSource != null">DATA_SOURCE,</if>
            <if test="pushStatus != null">PUSH_STATUS,</if>
            <if test="pushDate != null">PUSH_DATE,</if>
            <if test="pushBy != null">PUSH_BY,</if>
            <if test="remark != null">REMARK,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="createBy != null">CREATE_BY,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateBy != null">UPDATE_BY,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="lsh != null and lsh != ''">#{lsh},</if>
            <if test="bxjgdm != null">#{bxjgdm},</if>
            <if test="bxjgmc != null">#{bxjgmc},</if>
            <if test="zdfl != null">#{zdfl},</if>
            <if test="zdbh != null">#{zdbh},</if>
            <if test="ytzdbz != null">#{ytzdbz},</if>
            <if test="zdqq != null">#{zdqq},</if>
            <if test="zdzq != null">#{zdzq},</if>
            <if test="zbxgsdm != null">#{zbxgsdm},</if>
            <if test="zbxgsmc != null">#{zbxgsmc},</if>
            <if test="ybxgsdm != null">#{ybxgsdm},</if>
            <if test="ybxgsmc != null">#{ybxgsmc},</if>
            <if test="xnhtbz != null">#{xnhtbz},</if>
            <if test="zbxhthm != null">#{zbxhthm},</if>
            <if test="zbxhtmc != null">#{zbxhtmc},</if>
            <if test="fbf != null">#{fbf},</if>
            <if test="fbyj != null">#{fbyj},</if>
            <if test="thfbf != null">#{thfbf},</if>
            <if test="thfbyj != null">#{thfbyj},</if>
            <if test="thtbj != null">#{thtbj},</if>
            <if test="thlpk != null">#{thlpk},</if>
            <if test="thmqj != null">#{thmqj},</if>
            <if test="thscj != null">#{thscj},</if>
            <if test="jszt != null">#{jszt},</if>
            <if test="jsrq != null">#{jsrq},</if>
            <if test="hbdm != null">#{hbdm},</if>
            <if test="jshl != null">#{jshl},</if>
            <if test="zdjylsbh != null">#{zdjylsbh},</if>
            <if test="cjrq != null">#{cjrq},</if>
            <if test="sjbspch != null">#{sjbspch},</if>
            <if test="managecom != null">#{managecom},</if>
            <if test="settleBillNo != null">#{settleBillNo},</if>
            <if test="importStatus != null">#{importStatus},</if>
            <if test="reportYear != null">#{reportYear},</if>
            <if test="reportMonth != null">#{reportMonth},</if>
            <if test="accountPeriod != null">#{accountPeriod},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="pushDate != null">#{pushDate},</if>
            <if test="pushBy != null">#{pushBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="isDel != null">#{isDel},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>
	
	<insert id="insertBatchDwsEastZbzdxxb" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    	insert into T_DWS_EAST_ZBZDXXB(LSH, BXJGDM, BXJGMC, ZDFL, ZDBH, YTZDBZ, ZDQQ, ZDZQ, ZBXGSDM, ZBXGSMC, YBXGSDM, YBXGSMC, XNHTBZ, ZBXHTHM, ZBXHTMC, FBF, FBYJ, THFBF, THFBYJ, THTBJ, THLPK, THMQJ, THSCJ, JSZT, JSRQ, HBDM, JSHL, ZDJYLSBH, CJRQ, SJBSPCH, MANAGECOM, SETTLE_BILL_NO, IMPORT_STATUS, REPORT_YEAR, REPORT_MONTH, ACCOUNT_PERIOD, DATA_SOURCE, PUSH_STATUS, PUSH_DATE, PUSH_BY, REMARK, CREATE_BY, CREATE_TIME, UPDATE_BY, UPDATE_TIME)values
    	<foreach collection="list" index="index" item="item" separator=",">
        	(#{item.lsh}, #{item.bxjgdm}, #{item.bxjgmc}, #{item.zdfl}, #{item.zdbh}, #{item.ytzdbz}, #{item.zdqq}, #{item.zdzq}, #{item.zbxgsdm}, #{item.zbxgsmc}, #{item.ybxgsdm}, #{item.ybxgsmc}, #{item.xnhtbz}, #{item.zbxhthm}, #{item.zbxhtmc}, #{item.fbf}, #{item.fbyj}, #{item.thfbf}, #{item.thfbyj}, #{item.thtbj}, #{item.thlpk}, #{item.thmqj}, #{item.thscj}, #{item.jszt}, #{item.jsrq}, #{item.hbdm}, #{item.jshl}, #{item.zdjylsbh}, #{item.cjrq}, #{item.sjbspch}, #{item.managecom}, #{item.settleBillNo}, #{item.importStatus}, #{item.reportYear}, #{item.reportMonth}, #{item.accountPeriod}, #{item.dataSource}, #{item.pushStatus}, #{item.pushDate}, #{item.pushBy}, #{item.remark},  #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>
    
    <update id="updateDwsEastZbzdxxb" parameterType="DwsEastZbzdxxbEntity">
        update T_DWS_EAST_ZBZDXXB
        <trim prefix="SET" suffixOverrides=",">
            <if test="lsh != null and lsh != ''">LSH = #{lsh},</if>
            <if test="bxjgdm != null">BXJGDM = #{bxjgdm},</if>
            <if test="bxjgmc != null">BXJGMC = #{bxjgmc},</if>
            <if test="zdfl != null">ZDFL = #{zdfl},</if>
            <if test="zdbh != null">ZDBH = #{zdbh},</if>
            <if test="ytzdbz != null">YTZDBZ = #{ytzdbz},</if>
            <if test="zdqq != null">ZDQQ = #{zdqq},</if>
            <if test="zdzq != null">ZDZQ = #{zdzq},</if>
            <if test="zbxgsdm != null">ZBXGSDM = #{zbxgsdm},</if>
            <if test="zbxgsmc != null">ZBXGSMC = #{zbxgsmc},</if>
            <if test="ybxgsdm != null">YBXGSDM = #{ybxgsdm},</if>
            <if test="ybxgsmc != null">YBXGSMC = #{ybxgsmc},</if>
            <if test="xnhtbz != null">XNHTBZ = #{xnhtbz},</if>
            <if test="zbxhthm != null">ZBXHTHM = #{zbxhthm},</if>
            <if test="zbxhtmc != null">ZBXHTMC = #{zbxhtmc},</if>
            <if test="fbf != null">FBF = #{fbf},</if>
            <if test="fbyj != null">FBYJ = #{fbyj},</if>
            <if test="thfbf != null">THFBF = #{thfbf},</if>
            <if test="thfbyj != null">THFBYJ = #{thfbyj},</if>
            <if test="thtbj != null">THTBJ = #{thtbj},</if>
            <if test="thlpk != null">THLPK = #{thlpk},</if>
            <if test="thmqj != null">THMQJ = #{thmqj},</if>
            <if test="thscj != null">THSCJ = #{thscj},</if>
            <if test="jszt != null">JSZT = #{jszt},</if>
            <if test="jsrq != null">JSRQ = #{jsrq},</if>
            <if test="hbdm != null">HBDM = #{hbdm},</if>
            <if test="jshl != null">JSHL = #{jshl},</if>
            <if test="zdjylsbh != null">ZDJYLSBH = #{zdjylsbh},</if>
            <if test="cjrq != null">CJRQ = #{cjrq},</if>
            <if test="sjbspch != null">SJBSPCH = #{sjbspch},</if>
            <if test="managecom != null">MANAGECOM = #{managecom},</if>
            <if test="settleBillNo != null">SETTLE_BILL_NO = #{settleBillNo},</if>
            <if test="importStatus != null">IMPORT_STATUS = #{importStatus},</if>
            <if test="reportYear != null">REPORT_YEAR = #{reportYear},</if>
            <if test="reportMonth != null">REPORT_MONTH = #{reportMonth},</if>
            <if test="accountPeriod != null">ACCOUNT_PERIOD = #{accountPeriod},</if>
            <if test="dataSource != null">DATA_SOURCE = #{dataSource},</if>
            <if test="pushStatus != null">PUSH_STATUS = #{pushStatus},</if>
            <if test="pushDate != null">PUSH_DATE = #{pushDate},</if>
            <if test="pushBy != null">PUSH_BY = #{pushBy},</if>
            <if test="remark != null">REMARK = #{remark},</if>
            <if test="isDel != null">IS_DEL = #{isDel},</if>
            <if test="createBy != null">CREATE_BY = #{createBy},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime},</if>
            <if test="updateBy != null">UPDATE_BY = #{updateBy},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime},</if>
        </trim>
        where ID = #{id}
    </update>
	
	<update id="updateZbzdxxbImportStatusByLsh">
        update T_DWS_EAST_ZBZDXXB set IMPORT_STATUS = #{importStatus}, UPDATE_BY = #{updateBy}, UPDATE_TIME = #{updateTime},
        <trim prefix="" suffixOverrides=",">
        	<if test="remark != null">REMARK = #{remark},</if>
        </trim>
         where LSH = #{lsh}
    </update>
    
	<update id="updateDwsEastZbzdxxbPushStatus">
        update T_DWS_EAST_ZBZDXXB set PUSH_STATUS = #{pushStatus}, PUSH_BY = #{pushBy}, PUSH_DATE = now() where ID in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
        	#{id}
        </foreach>
    </update>
    
    <delete id="deleteDwsEastZbzdxxbById" parameterType="Long">
        delete from T_DWS_EAST_ZBZDXXB where ID = #{id}
    </delete>

    <delete id="deleteDwsEastZbzdxxbByIds" parameterType="String">
        delete from T_DWS_EAST_ZBZDXXB where ID in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>