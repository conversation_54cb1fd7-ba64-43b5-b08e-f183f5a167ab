<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpBenefitMapper">
    
    <resultMap type="DwsPrpBenefitEntity" id="DwsPrpBenefitResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="GrpPolicyNo"    column="GrpPolicyNo"    />
        <result property="GPFlag"    column="GPFlag"    />
        <result property="PolicyNo"    column="PolicyNo"    />
        <result property="ProductNo"    column="ProductNo"    />
        <result property="PolYear"    column="PolYear"    />
        <result property="ProductCode"    column="ProductCode"    />
        <result property="LiabilityCode"    column="LiabilityCode"    />
        <result property="LiabilityName"    column="LiabilityName"    />
        <result property="GetLiabilityCode"    column="GetLiabilityCode"    />
        <result property="GetLiabilityName"    column="GetLiabilityName"    />
        <result property="TermType"    column="TermType"    />
        <result property="WDNo"    column="WDNo"    />
        <result property="InsuredNo"    column="InsuredNo"    />
        <result property="InsuredName"    column="InsuredName"    />
        <result property="InsuredSex"    column="InsuredSex"    />
        <result property="InsuredCertType"    column="InsuredCertType"    />
        <result property="InsuredCertNo"    column="InsuredCertNo"    />
        <result property="OccupationType"    column="OccupationType"    />
        <result property="AppntAge"    column="AppntAge"    />
        <result property="PreAge"    column="PreAge"    />
        <result property="BenefitDate"    column="BenefitDate"    />
        <result property="SaparateFlag"    column="SaparateFlag"    />
        <result property="BenefitClass"    column="BenefitClass"    />
        <result property="BenefitAmount"    column="BenefitAmount"    />
        <result property="EnterAccDate"    column="EnterAccDate"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReinsurMode"    column="ReinsurMode"    />
        <result property="BackBenefitAmount"    column="BackBenefitAmount"    />
        <result property="BackDate"    column="BackDate"    />
        <result property="Currency"    column="Currency"    />
        <result property="ReComputationsDate"    column="ReComputationsDate"    />
        <result property="AccountGetDate"    column="AccountGetDate"    />
        <result property="AccTransNo"    column="AccTransNo"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpBenefitVo">
        select Id, TransactionNo, CompanyCode, GrpPolicyNo, GPFlag, PolicyNo, ProductNo, PolYear, ProductCode, LiabilityCode, LiabilityName, GetLiabilityCode, GetLiabilityName, TermType, WDNo, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, BenefitDate, SaparateFlag, BenefitClass, BenefitAmount, EnterAccDate, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, BackBenefitAmount, BackDate, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_benefit
    </sql>

    <select id="selectDwsPrpBenefitList" parameterType="DwsPrpBenefitQuery" resultMap="DwsPrpBenefitResult">
        <include refid="selectDwsPrpBenefitVo"/>
        <where>
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo = #{TransactionNo}</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="GrpPolicyNo != null  and GrpPolicyNo != ''"> and GrpPolicyNo = #{GrpPolicyNo}</if>
            <if test="GPFlag != null  and GPFlag != ''"> and GPFlag = #{GPFlag}</if>
            <if test="PolicyNo != null  and PolicyNo != ''"> and PolicyNo = #{PolicyNo}</if>
            <if test="ProductNo != null  and ProductNo != ''"> and ProductNo = #{ProductNo}</if>
            <if test="PolYear != null "> and PolYear = #{PolYear}</if>
            <if test="ProductCode != null  and ProductCode != ''"> and ProductCode = #{ProductCode}</if>
            <if test="LiabilityCode != null  and LiabilityCode != ''"> and LiabilityCode = #{LiabilityCode}</if>
            <if test="LiabilityName != null  and LiabilityName != ''"> and LiabilityName like concat('%', #{LiabilityName}, '%')</if>
            <if test="GetLiabilityCode != null  and GetLiabilityCode != ''"> and GetLiabilityCode = #{GetLiabilityCode}</if>
            <if test="GetLiabilityName != null  and GetLiabilityName != ''"> and GetLiabilityName like concat('%', #{GetLiabilityName}, '%')</if>
            <if test="TermType != null  and TermType != ''"> and TermType = #{TermType}</if>
            <if test="WDNo != null  and WDNo != ''"> and WDNo = #{WDNo}</if>
            <if test="InsuredNo != null  and InsuredNo != ''"> and InsuredNo = #{InsuredNo}</if>
            <if test="InsuredName != null  and InsuredName != ''"> and InsuredName like concat('%', #{InsuredName}, '%')</if>
            <if test="InsuredSex != null  and InsuredSex != ''"> and InsuredSex = #{InsuredSex}</if>
            <if test="InsuredCertType != null  and InsuredCertType != ''"> and InsuredCertType = #{InsuredCertType}</if>
            <if test="InsuredCertNo != null  and InsuredCertNo != ''"> and InsuredCertNo = #{InsuredCertNo}</if>
            <if test="OccupationType != null  and OccupationType != ''"> and OccupationType = #{OccupationType}</if>
            <if test="AppntAge != null "> and AppntAge = #{AppntAge}</if>
            <if test="PreAge != null "> and PreAge = #{PreAge}</if>
            <if test="BenefitDate != null "> and BenefitDate = #{BenefitDate}</if>
            <if test="SaparateFlag != null  and SaparateFlag != ''"> and SaparateFlag = #{SaparateFlag}</if>
            <if test="BenefitClass != null  and BenefitClass != ''"> and BenefitClass = #{BenefitClass}</if>
            <if test="BenefitAmount != null "> and BenefitAmount = #{BenefitAmount}</if>
            <if test="EnterAccDate != null "> and EnterAccDate = #{EnterAccDate}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo = #{ReInsuranceContNo}</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode = #{ReinsurerCode}</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReinsurMode != null  and ReinsurMode != ''"> and ReinsurMode = #{ReinsurMode}</if>
            <if test="BackBenefitAmount != null "> and BackBenefitAmount = #{BackBenefitAmount}</if>
            <if test="BackDate != null "> and BackDate = #{BackDate}</if>
            <if test="Currency != null  and Currency != ''"> and Currency = #{Currency}</if>
            <if test="ReComputationsDate != null "> and ReComputationsDate = #{ReComputationsDate}</if>
            <if test="AccountGetDate != null "> and AccountGetDate = #{AccountGetDate}</if>
            <if test="AccTransNo != null  and AccTransNo != ''"> and AccTransNo = #{AccTransNo}</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark = #{Remark}</if>
            <if test="IsDel != null "> and IsDel = #{IsDel}</if>
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpBenefitById" parameterType="Long" resultMap="DwsPrpBenefitResult">
        <include refid="selectDwsPrpBenefitVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpBenefitByIds" parameterType="String" resultMap="DwsPrpBenefitResult">
        <include refid="selectDwsPrpBenefitVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpBenefit" parameterType="DwsPrpBenefitEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_benefit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo,</if>
            <if test="GPFlag != null">GPFlag,</if>
            <if test="PolicyNo != null">PolicyNo,</if>
            <if test="ProductNo != null">ProductNo,</if>
            <if test="PolYear != null">PolYear,</if>
            <if test="ProductCode != null">ProductCode,</if>
            <if test="LiabilityCode != null">LiabilityCode,</if>
            <if test="LiabilityName != null">LiabilityName,</if>
            <if test="GetLiabilityCode != null">GetLiabilityCode,</if>
            <if test="GetLiabilityName != null">GetLiabilityName,</if>
            <if test="TermType != null">TermType,</if>
            <if test="WDNo != null">WDNo,</if>
            <if test="InsuredNo != null">InsuredNo,</if>
            <if test="InsuredName != null">InsuredName,</if>
            <if test="InsuredSex != null">InsuredSex,</if>
            <if test="InsuredCertType != null">InsuredCertType,</if>
            <if test="InsuredCertNo != null">InsuredCertNo,</if>
            <if test="OccupationType != null">OccupationType,</if>
            <if test="AppntAge != null">AppntAge,</if>
            <if test="PreAge != null">PreAge,</if>
            <if test="BenefitDate != null">BenefitDate,</if>
            <if test="SaparateFlag != null">SaparateFlag,</if>
            <if test="BenefitClass != null">BenefitClass,</if>
            <if test="BenefitAmount != null">BenefitAmount,</if>
            <if test="EnterAccDate != null">EnterAccDate,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReinsurMode != null">ReinsurMode,</if>
            <if test="BackBenefitAmount != null">BackBenefitAmount,</if>
            <if test="BackDate != null">BackDate,</if>
            <if test="Currency != null">Currency,</if>
            <if test="ReComputationsDate != null">ReComputationsDate,</if>
            <if test="AccountGetDate != null">AccountGetDate,</if>
            <if test="AccTransNo != null">AccTransNo,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
            <if test="CreateTime != null">CreateTime,</if>
            <if test="UpdateBy != null">UpdateBy,</if>
            <if test="UpdateTime != null">UpdateTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="GrpPolicyNo != null">#{GrpPolicyNo},</if>
            <if test="GPFlag != null">#{GPFlag},</if>
            <if test="PolicyNo != null">#{PolicyNo},</if>
            <if test="ProductNo != null">#{ProductNo},</if>
            <if test="PolYear != null">#{PolYear},</if>
            <if test="ProductCode != null">#{ProductCode},</if>
            <if test="LiabilityCode != null">#{LiabilityCode},</if>
            <if test="LiabilityName != null">#{LiabilityName},</if>
            <if test="GetLiabilityCode != null">#{GetLiabilityCode},</if>
            <if test="GetLiabilityName != null">#{GetLiabilityName},</if>
            <if test="TermType != null">#{TermType},</if>
            <if test="WDNo != null">#{WDNo},</if>
            <if test="InsuredNo != null">#{InsuredNo},</if>
            <if test="InsuredName != null">#{InsuredName},</if>
            <if test="InsuredSex != null">#{InsuredSex},</if>
            <if test="InsuredCertType != null">#{InsuredCertType},</if>
            <if test="InsuredCertNo != null">#{InsuredCertNo},</if>
            <if test="OccupationType != null">#{OccupationType},</if>
            <if test="AppntAge != null">#{AppntAge},</if>
            <if test="PreAge != null">#{PreAge},</if>
            <if test="BenefitDate != null">#{BenefitDate},</if>
            <if test="SaparateFlag != null">#{SaparateFlag},</if>
            <if test="BenefitClass != null">#{BenefitClass},</if>
            <if test="BenefitAmount != null">#{BenefitAmount},</if>
            <if test="EnterAccDate != null">#{EnterAccDate},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReinsurMode != null">#{ReinsurMode},</if>
            <if test="BackBenefitAmount != null">#{BackBenefitAmount},</if>
            <if test="BackDate != null">#{BackDate},</if>
            <if test="Currency != null">#{Currency},</if>
            <if test="ReComputationsDate != null">#{ReComputationsDate},</if>
            <if test="AccountGetDate != null">#{AccountGetDate},</if>
            <if test="AccTransNo != null">#{AccTransNo},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
            <if test="CreateTime != null">#{CreateTime},</if>
            <if test="UpdateBy != null">#{UpdateBy},</if>
            <if test="UpdateTime != null">#{UpdateTime},</if>
        </trim>
    </insert>

    <insert id="insertBatchDwsPrpBenefit" parameterType="java.util.List">
        insert into t_dws_prp_benefit (TransactionNo, CompanyCode, GrpPolicyNo, GPFlag, PolicyNo, ProductNo, PolYear, ProductCode, LiabilityCode, LiabilityName, GetLiabilityCode, GetLiabilityName, TermType, WDNo, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, BenefitDate, SaparateFlag, BenefitClass, BenefitAmount, EnterAccDate, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, BackBenefitAmount, BackDate, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy) values
        <foreach item="item" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.GrpPolicyNo}, #{item.GPFlag}, #{item.PolicyNo}, #{item.ProductNo}, #{item.PolYear}, #{item.ProductCode}, #{item.LiabilityCode}, #{item.LiabilityName}, #{item.GetLiabilityCode}, #{item.GetLiabilityName}, #{item.TermType}, #{item.WDNo}, #{item.InsuredNo}, #{item.InsuredName}, #{item.InsuredSex}, #{item.InsuredCertType}, #{item.InsuredCertNo}, #{item.OccupationType}, #{item.AppntAge}, #{item.PreAge}, #{item.BenefitDate}, #{item.SaparateFlag}, #{item.BenefitClass}, #{item.BenefitAmount}, #{item.EnterAccDate}, #{item.ReInsuranceContNo}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReinsurMode}, #{item.BackBenefitAmount}, #{item.BackDate}, #{item.Currency}, #{item.ReComputationsDate}, #{item.AccountGetDate}, #{item.AccTransNo}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.IsDel}, #{item.CreateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpBenefit" parameterType="DwsPrpBenefitEntity">
        update t_dws_prp_benefit
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo = #{GrpPolicyNo},</if>
            <if test="GPFlag != null">GPFlag = #{GPFlag},</if>
            <if test="PolicyNo != null">PolicyNo = #{PolicyNo},</if>
            <if test="ProductNo != null">ProductNo = #{ProductNo},</if>
            <if test="PolYear != null">PolYear = #{PolYear},</if>
            <if test="ProductCode != null">ProductCode = #{ProductCode},</if>
            <if test="LiabilityCode != null">LiabilityCode = #{LiabilityCode},</if>
            <if test="LiabilityName != null">LiabilityName = #{LiabilityName},</if>
            <if test="GetLiabilityCode != null">GetLiabilityCode = #{GetLiabilityCode},</if>
            <if test="GetLiabilityName != null">GetLiabilityName = #{GetLiabilityName},</if>
            <if test="TermType != null">TermType = #{TermType},</if>
            <if test="WDNo != null">WDNo = #{WDNo},</if>
            <if test="InsuredNo != null">InsuredNo = #{InsuredNo},</if>
            <if test="InsuredName != null">InsuredName = #{InsuredName},</if>
            <if test="InsuredSex != null">InsuredSex = #{InsuredSex},</if>
            <if test="InsuredCertType != null">InsuredCertType = #{InsuredCertType},</if>
            <if test="InsuredCertNo != null">InsuredCertNo = #{InsuredCertNo},</if>
            <if test="OccupationType != null">OccupationType = #{OccupationType},</if>
            <if test="AppntAge != null">AppntAge = #{AppntAge},</if>
            <if test="PreAge != null">PreAge = #{PreAge},</if>
            <if test="BenefitDate != null">BenefitDate = #{BenefitDate},</if>
            <if test="SaparateFlag != null">SaparateFlag = #{SaparateFlag},</if>
            <if test="BenefitClass != null">BenefitClass = #{BenefitClass},</if>
            <if test="BenefitAmount != null">BenefitAmount = #{BenefitAmount},</if>
            <if test="EnterAccDate != null">EnterAccDate = #{EnterAccDate},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReinsurMode != null">ReinsurMode = #{ReinsurMode},</if>
            <if test="BackBenefitAmount != null">BackBenefitAmount = #{BackBenefitAmount},</if>
            <if test="BackDate != null">BackDate = #{BackDate},</if>
            <if test="Currency != null">Currency = #{Currency},</if>
            <if test="ReComputationsDate != null">ReComputationsDate = #{ReComputationsDate},</if>
            <if test="AccountGetDate != null">AccountGetDate = #{AccountGetDate},</if>
            <if test="AccTransNo != null">AccTransNo = #{AccTransNo},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            <if test="UpdateTime != null">UpdateTime = #{UpdateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpBenefitById" parameterType="Long">
        delete from t_dws_prp_benefit where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpBenefitByIds" parameterType="String">
        delete from t_dws_prp_benefit where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpBenefitCountByAccTransNo" resultType="Integer">
        select count(*) from t_dws_prp_benefit where IsDel = 0 and AccTransNo = #{AccTransNo}
    </select>

    <select id="selectDwsPrpBenefitListByAccTransNo" resultMap="DwsPrpBenefitResult">
        <include refid="selectDwsPrpBenefitVo"/> where IsDel = 0 and AccTransNo = #{AccTransNo} order by TransactionNo limit #{startRows}, #{pageSize}
    </select>

    <delete id="deleteDwsPrpBenefitByAccTransNo" parameterType="String">
        delete from t_dws_prp_benefit where AccTransNo = #{AccTransNo}
    </delete>

    <update id="updateDwsPrpBenefitPushStatus">
        update t_dws_prp_benefit set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now() where AccTransNo in
        <foreach item="accTransNo" collection="accTransNos" open="(" separator="," close=")">
            #{accTransNo}
        </foreach>
    </update>

</mapper>
