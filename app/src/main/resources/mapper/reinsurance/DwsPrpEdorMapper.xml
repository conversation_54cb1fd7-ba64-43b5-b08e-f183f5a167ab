<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpEdorMapper">
    
    <resultMap type="DwsPrpEdorEntity" id="DwsPrpEdorResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="GrpPolicyNo"    column="GrpPolicyNo"    />
        <result property="GrpProductNo"    column="GrpProductNo"    />
        <result property="PolicyNo"    column="PolicyNo"    />
        <result property="ProductNo"    column="ProductNo"    />
        <result property="GPFlag"    column="GPFlag"    />
        <result property="MainProductNo"    column="MainProductNo"    />
        <result property="MainProductFlag"    column="MainProductFlag"    />
        <result property="ProductCode"    column="ProductCode"    />
        <result property="LiabilityCode"    column="LiabilityCode"    />
        <result property="LiabilityName"    column="LiabilityName"    />
        <result property="Classification"    column="Classification"    />
        <result property="TermType"    column="TermType"    />
        <result property="ManageCom"    column="ManageCom"    />
        <result property="PolYear"    column="PolYear"    />
        <result property="SignDate"    column="SignDate"    />
        <result property="EffDate"    column="EffDate"    />
        <result property="InvalidDate"    column="InvalidDate"    />
        <result property="UWConclusion"    column="UWConclusion"    />
        <result property="PolStatus"    column="PolStatus"    />
        <result property="Status"    column="Status"    />
        <result property="BasicSumInsured"    column="BasicSumInsured"    />
        <result property="RiskAmnt"    column="RiskAmnt"    />
        <result property="Premium"    column="Premium"    />
        <result property="AccountValue"    column="AccountValue"    />
        <result property="FacultativeFlag"    column="FacultativeFlag"    />
        <result property="AnonymousFlag"    column="AnonymousFlag"    />
        <result property="WaiverFlag"    column="WaiverFlag"    />
        <result property="WaiverPrem"    column="WaiverPrem"    />
        <result property="FinalCashValue"    column="FinalCashValue"    />
        <result property="FinalLiabilityReserve"    column="FinalLiabilityReserve"    />
        <result property="InsurePeoples"    column="InsurePeoples"    />
        <result property="InsuredNo"    column="InsuredNo"    />
        <result property="InsuredName"    column="InsuredName"    />
        <result property="InsuredSex"    column="InsuredSex"    />
        <result property="InsuredCertType"    column="InsuredCertType"    />
        <result property="InsuredCertNo"    column="InsuredCertNo"    />
        <result property="OccupationType"    column="OccupationType"    />
        <result property="AppntAge"    column="AppntAge"    />
        <result property="PreAge"    column="PreAge"    />
        <result property="ProfessionalFee"    column="ProfessionalFee"    />
        <result property="SubStandardFee"    column="SubStandardFee"    />
        <result property="EMRate"    column="EMRate"    />
        <result property="ProjectFlag"    column="ProjectFlag"    />
        <result property="EndorAcceptNo"    column="EndorAcceptNo"    />
        <result property="EndorsementNo"    column="EndorsementNo"    />
        <result property="EdorType"    column="EdorType"    />
        <result property="EdorValiDate"    column="EdorValiDate"    />
        <result property="EdorConfDate"    column="EdorConfDate"    />
        <result property="EdorMoney"    column="EdorMoney"    />
        <result property="PreInsuredAge"    column="PreInsuredAge"    />
        <result property="PreBasicSumInsured"    column="PreBasicSumInsured"    />
        <result property="PreRiskAmnt"    column="PreRiskAmnt"    />
        <result property="PreReinsuranceAmnt"    column="PreReinsuranceAmnt"    />
        <result property="PreRetentionAmount"    column="PreRetentionAmount"    />
        <result property="PrePremium"    column="PrePremium"    />
        <result property="PreAccountValue"    column="PreAccountValue"    />
        <result property="PreWaiverPrem"    column="PreWaiverPrem"    />
        <result property="ProjectAcreageChange"    column="ProjectAcreageChange"    />
        <result property="ProjectCostChange"    column="ProjectCostChange"    />
        <result property="SaparateFlag"    column="SaparateFlag"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReinsurMode"    column="ReinsurMode"    />
        <result property="QuotaSharePercentage"    column="QuotaSharePercentage"    />
        <result property="ReinsuranceAmntChange"    column="ReinsuranceAmntChange"    />
        <result property="RetentionAmount"    column="RetentionAmount"    />
        <result property="ReinsurancePremiumChange"    column="ReinsurancePremiumChange"    />
        <result property="ReinsuranceCommssionChange"    column="ReinsuranceCommssionChange"    />
        <result property="Currency"    column="Currency"    />
        <result property="ReComputationsDate"    column="ReComputationsDate"    />
        <result property="AccountGetDate"    column="AccountGetDate"    />
        <result property="AccTransNo"    column="AccTransNo"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpEdorVo">
        select Id, TransactionNo, CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, TermType, ManageCom, PolYear, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsurePeoples, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, EndorAcceptNo, EndorsementNo, EdorType, EdorValiDate, EdorConfDate, EdorMoney, PreInsuredAge, PreBasicSumInsured, PreRiskAmnt, PreReinsuranceAmnt, PreRetentionAmount, PrePremium, PreAccountValue, PreWaiverPrem, ProjectAcreageChange, ProjectCostChange, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, QuotaSharePercentage, ReinsuranceAmntChange, RetentionAmount, ReinsurancePremiumChange, ReinsuranceCommssionChange, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_edor
    </sql>

    <select id="selectDwsPrpEdorList" parameterType="DwsPrpEdorQuery" resultMap="DwsPrpEdorResult">
        <include refid="selectDwsPrpEdorVo"/>
        <where>
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo = #{TransactionNo}</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="GrpPolicyNo != null  and GrpPolicyNo != ''"> and GrpPolicyNo = #{GrpPolicyNo}</if>
            <if test="GrpProductNo != null  and GrpProductNo != ''"> and GrpProductNo = #{GrpProductNo}</if>
            <if test="PolicyNo != null  and PolicyNo != ''"> and PolicyNo = #{PolicyNo}</if>
            <if test="ProductNo != null  and ProductNo != ''"> and ProductNo = #{ProductNo}</if>
            <if test="GPFlag != null  and GPFlag != ''"> and GPFlag = #{GPFlag}</if>
            <if test="MainProductNo != null  and MainProductNo != ''"> and MainProductNo = #{MainProductNo}</if>
            <if test="MainProductFlag != null  and MainProductFlag != ''"> and MainProductFlag = #{MainProductFlag}</if>
            <if test="ProductCode != null  and ProductCode != ''"> and ProductCode = #{ProductCode}</if>
            <if test="LiabilityCode != null  and LiabilityCode != ''"> and LiabilityCode = #{LiabilityCode}</if>
            <if test="LiabilityName != null  and LiabilityName != ''"> and LiabilityName like concat('%', #{LiabilityName}, '%')</if>
            <if test="Classification != null  and Classification != ''"> and Classification = #{Classification}</if>
            <if test="TermType != null  and TermType != ''"> and TermType = #{TermType}</if>
            <if test="ManageCom != null  and ManageCom != ''"> and ManageCom = #{ManageCom}</if>
            <if test="PolYear != null "> and PolYear = #{PolYear}</if>
            <if test="SignDate != null "> and SignDate = #{SignDate}</if>
            <if test="EffDate != null "> and EffDate = #{EffDate}</if>
            <if test="InvalidDate != null "> and InvalidDate = #{InvalidDate}</if>
            <if test="UWConclusion != null  and UWConclusion != ''"> and UWConclusion = #{UWConclusion}</if>
            <if test="PolStatus != null  and PolStatus != ''"> and PolStatus = #{PolStatus}</if>
            <if test="Status != null  and Status != ''"> and Status = #{Status}</if>
            <if test="BasicSumInsured != null "> and BasicSumInsured = #{BasicSumInsured}</if>
            <if test="RiskAmnt != null "> and RiskAmnt = #{RiskAmnt}</if>
            <if test="Premium != null "> and Premium = #{Premium}</if>
            <if test="AccountValue != null "> and AccountValue = #{AccountValue}</if>
            <if test="FacultativeFlag != null  and FacultativeFlag != ''"> and FacultativeFlag = #{FacultativeFlag}</if>
            <if test="AnonymousFlag != null  and AnonymousFlag != ''"> and AnonymousFlag = #{AnonymousFlag}</if>
            <if test="WaiverFlag != null  and WaiverFlag != ''"> and WaiverFlag = #{WaiverFlag}</if>
            <if test="WaiverPrem != null "> and WaiverPrem = #{WaiverPrem}</if>
            <if test="FinalCashValue != null "> and FinalCashValue = #{FinalCashValue}</if>
            <if test="FinalLiabilityReserve != null "> and FinalLiabilityReserve = #{FinalLiabilityReserve}</if>
            <if test="InsurePeoples != null "> and InsurePeoples = #{InsurePeoples}</if>
            <if test="InsuredNo != null  and InsuredNo != ''"> and InsuredNo = #{InsuredNo}</if>
            <if test="InsuredName != null  and InsuredName != ''"> and InsuredName like concat('%', #{InsuredName}, '%')</if>
            <if test="InsuredSex != null  and InsuredSex != ''"> and InsuredSex = #{InsuredSex}</if>
            <if test="InsuredCertType != null  and InsuredCertType != ''"> and InsuredCertType = #{InsuredCertType}</if>
            <if test="InsuredCertNo != null  and InsuredCertNo != ''"> and InsuredCertNo = #{InsuredCertNo}</if>
            <if test="OccupationType != null  and OccupationType != ''"> and OccupationType = #{OccupationType}</if>
            <if test="AppntAge != null "> and AppntAge = #{AppntAge}</if>
            <if test="PreAge != null "> and PreAge = #{PreAge}</if>
            <if test="ProfessionalFee != null "> and ProfessionalFee = #{ProfessionalFee}</if>
            <if test="SubStandardFee != null "> and SubStandardFee = #{SubStandardFee}</if>
            <if test="EMRate != null "> and EMRate = #{EMRate}</if>
            <if test="ProjectFlag != null  and ProjectFlag != ''"> and ProjectFlag = #{ProjectFlag}</if>
            <if test="EndorAcceptNo != null  and EndorAcceptNo != ''"> and EndorAcceptNo = #{EndorAcceptNo}</if>
            <if test="EndorsementNo != null  and EndorsementNo != ''"> and EndorsementNo = #{EndorsementNo}</if>
            <if test="EdorType != null  and EdorType != ''"> and EdorType = #{EdorType}</if>
            <if test="EdorValiDate != null "> and EdorValiDate = #{EdorValiDate}</if>
            <if test="EdorConfDate != null "> and EdorConfDate = #{EdorConfDate}</if>
            <if test="EdorMoney != null "> and EdorMoney = #{EdorMoney}</if>
            <if test="PreInsuredAge != null "> and PreInsuredAge = #{PreInsuredAge}</if>
            <if test="PreBasicSumInsured != null "> and PreBasicSumInsured = #{PreBasicSumInsured}</if>
            <if test="PreRiskAmnt != null "> and PreRiskAmnt = #{PreRiskAmnt}</if>
            <if test="PreReinsuranceAmnt != null "> and PreReinsuranceAmnt = #{PreReinsuranceAmnt}</if>
            <if test="PreRetentionAmount != null "> and PreRetentionAmount = #{PreRetentionAmount}</if>
            <if test="PrePremium != null "> and PrePremium = #{PrePremium}</if>
            <if test="PreAccountValue != null "> and PreAccountValue = #{PreAccountValue}</if>
            <if test="PreWaiverPrem != null "> and PreWaiverPrem = #{PreWaiverPrem}</if>
            <if test="ProjectAcreageChange != null "> and ProjectAcreageChange = #{ProjectAcreageChange}</if>
            <if test="ProjectCostChange != null "> and ProjectCostChange = #{ProjectCostChange}</if>
            <if test="SaparateFlag != null  and SaparateFlag != ''"> and SaparateFlag = #{SaparateFlag}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo = #{ReInsuranceContNo}</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode = #{ReinsurerCode}</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReinsurMode != null  and ReinsurMode != ''"> and ReinsurMode = #{ReinsurMode}</if>
            <if test="QuotaSharePercentage != null  and QuotaSharePercentage != ''"> and QuotaSharePercentage = #{QuotaSharePercentage}</if>
            <if test="ReinsuranceAmntChange != null "> and ReinsuranceAmntChange = #{ReinsuranceAmntChange}</if>
            <if test="RetentionAmount != null "> and RetentionAmount = #{RetentionAmount}</if>
            <if test="ReinsurancePremiumChange != null "> and ReinsurancePremiumChange = #{ReinsurancePremiumChange}</if>
            <if test="ReinsuranceCommssionChange != null "> and ReinsuranceCommssionChange = #{ReinsuranceCommssionChange}</if>
            <if test="Currency != null  and Currency != ''"> and Currency = #{Currency}</if>
            <if test="ReComputationsDate != null "> and ReComputationsDate = #{ReComputationsDate}</if>
            <if test="AccountGetDate != null "> and AccountGetDate = #{AccountGetDate}</if>
            <if test="AccTransNo != null  and AccTransNo != ''"> and AccTransNo = #{AccTransNo}</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark = #{Remark}</if>
            <if test="IsDel != null "> and IsDel = #{IsDel}</if>
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpEdorById" parameterType="Long" resultMap="DwsPrpEdorResult">
        <include refid="selectDwsPrpEdorVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpEdorByIds" parameterType="String" resultMap="DwsPrpEdorResult">
        <include refid="selectDwsPrpEdorVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpEdor" parameterType="DwsPrpEdorEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_edor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo,</if>
            <if test="GrpProductNo != null">GrpProductNo,</if>
            <if test="PolicyNo != null">PolicyNo,</if>
            <if test="ProductNo != null">ProductNo,</if>
            <if test="GPFlag != null">GPFlag,</if>
            <if test="MainProductNo != null">MainProductNo,</if>
            <if test="MainProductFlag != null">MainProductFlag,</if>
            <if test="ProductCode != null">ProductCode,</if>
            <if test="LiabilityCode != null">LiabilityCode,</if>
            <if test="LiabilityName != null">LiabilityName,</if>
            <if test="Classification != null">Classification,</if>
            <if test="TermType != null">TermType,</if>
            <if test="ManageCom != null">ManageCom,</if>
            <if test="PolYear != null">PolYear,</if>
            <if test="SignDate != null">SignDate,</if>
            <if test="EffDate != null">EffDate,</if>
            <if test="InvalidDate != null">InvalidDate,</if>
            <if test="UWConclusion != null">UWConclusion,</if>
            <if test="PolStatus != null">PolStatus,</if>
            <if test="Status != null">Status,</if>
            <if test="BasicSumInsured != null">BasicSumInsured,</if>
            <if test="RiskAmnt != null">RiskAmnt,</if>
            <if test="Premium != null">Premium,</if>
            <if test="AccountValue != null">AccountValue,</if>
            <if test="FacultativeFlag != null">FacultativeFlag,</if>
            <if test="AnonymousFlag != null">AnonymousFlag,</if>
            <if test="WaiverFlag != null">WaiverFlag,</if>
            <if test="WaiverPrem != null">WaiverPrem,</if>
            <if test="FinalCashValue != null">FinalCashValue,</if>
            <if test="FinalLiabilityReserve != null">FinalLiabilityReserve,</if>
            <if test="InsurePeoples != null">InsurePeoples,</if>
            <if test="InsuredNo != null">InsuredNo,</if>
            <if test="InsuredName != null">InsuredName,</if>
            <if test="InsuredSex != null">InsuredSex,</if>
            <if test="InsuredCertType != null">InsuredCertType,</if>
            <if test="InsuredCertNo != null">InsuredCertNo,</if>
            <if test="OccupationType != null">OccupationType,</if>
            <if test="AppntAge != null">AppntAge,</if>
            <if test="PreAge != null">PreAge,</if>
            <if test="ProfessionalFee != null">ProfessionalFee,</if>
            <if test="SubStandardFee != null">SubStandardFee,</if>
            <if test="EMRate != null">EMRate,</if>
            <if test="ProjectFlag != null">ProjectFlag,</if>
            <if test="EndorAcceptNo != null">EndorAcceptNo,</if>
            <if test="EndorsementNo != null">EndorsementNo,</if>
            <if test="EdorType != null">EdorType,</if>
            <if test="EdorValiDate != null">EdorValiDate,</if>
            <if test="EdorConfDate != null">EdorConfDate,</if>
            <if test="EdorMoney != null">EdorMoney,</if>
            <if test="PreInsuredAge != null">PreInsuredAge,</if>
            <if test="PreBasicSumInsured != null">PreBasicSumInsured,</if>
            <if test="PreRiskAmnt != null">PreRiskAmnt,</if>
            <if test="PreReinsuranceAmnt != null">PreReinsuranceAmnt,</if>
            <if test="PreRetentionAmount != null">PreRetentionAmount,</if>
            <if test="PrePremium != null">PrePremium,</if>
            <if test="PreAccountValue != null">PreAccountValue,</if>
            <if test="PreWaiverPrem != null">PreWaiverPrem,</if>
            <if test="ProjectAcreageChange != null">ProjectAcreageChange,</if>
            <if test="ProjectCostChange != null">ProjectCostChange,</if>
            <if test="SaparateFlag != null">SaparateFlag,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReinsurMode != null">ReinsurMode,</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage,</if>
            <if test="ReinsuranceAmntChange != null">ReinsuranceAmntChange,</if>
            <if test="RetentionAmount != null">RetentionAmount,</if>
            <if test="ReinsurancePremiumChange != null">ReinsurancePremiumChange,</if>
            <if test="ReinsuranceCommssionChange != null">ReinsuranceCommssionChange,</if>
            <if test="Currency != null">Currency,</if>
            <if test="ReComputationsDate != null">ReComputationsDate,</if>
            <if test="AccountGetDate != null">AccountGetDate,</if>
            <if test="AccTransNo != null">AccTransNo,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
            <if test="CreateTime != null">CreateTime,</if>
            <if test="UpdateBy != null">UpdateBy,</if>
            <if test="UpdateTime != null">UpdateTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="GrpPolicyNo != null">#{GrpPolicyNo},</if>
            <if test="GrpProductNo != null">#{GrpProductNo},</if>
            <if test="PolicyNo != null">#{PolicyNo},</if>
            <if test="ProductNo != null">#{ProductNo},</if>
            <if test="GPFlag != null">#{GPFlag},</if>
            <if test="MainProductNo != null">#{MainProductNo},</if>
            <if test="MainProductFlag != null">#{MainProductFlag},</if>
            <if test="ProductCode != null">#{ProductCode},</if>
            <if test="LiabilityCode != null">#{LiabilityCode},</if>
            <if test="LiabilityName != null">#{LiabilityName},</if>
            <if test="Classification != null">#{Classification},</if>
            <if test="TermType != null">#{TermType},</if>
            <if test="ManageCom != null">#{ManageCom},</if>
            <if test="PolYear != null">#{PolYear},</if>
            <if test="SignDate != null">#{SignDate},</if>
            <if test="EffDate != null">#{EffDate},</if>
            <if test="InvalidDate != null">#{InvalidDate},</if>
            <if test="UWConclusion != null">#{UWConclusion},</if>
            <if test="PolStatus != null">#{PolStatus},</if>
            <if test="Status != null">#{Status},</if>
            <if test="BasicSumInsured != null">#{BasicSumInsured},</if>
            <if test="RiskAmnt != null">#{RiskAmnt},</if>
            <if test="Premium != null">#{Premium},</if>
            <if test="AccountValue != null">#{AccountValue},</if>
            <if test="FacultativeFlag != null">#{FacultativeFlag},</if>
            <if test="AnonymousFlag != null">#{AnonymousFlag},</if>
            <if test="WaiverFlag != null">#{WaiverFlag},</if>
            <if test="WaiverPrem != null">#{WaiverPrem},</if>
            <if test="FinalCashValue != null">#{FinalCashValue},</if>
            <if test="FinalLiabilityReserve != null">#{FinalLiabilityReserve},</if>
            <if test="InsurePeoples != null">#{InsurePeoples},</if>
            <if test="InsuredNo != null">#{InsuredNo},</if>
            <if test="InsuredName != null">#{InsuredName},</if>
            <if test="InsuredSex != null">#{InsuredSex},</if>
            <if test="InsuredCertType != null">#{InsuredCertType},</if>
            <if test="InsuredCertNo != null">#{InsuredCertNo},</if>
            <if test="OccupationType != null">#{OccupationType},</if>
            <if test="AppntAge != null">#{AppntAge},</if>
            <if test="PreAge != null">#{PreAge},</if>
            <if test="ProfessionalFee != null">#{ProfessionalFee},</if>
            <if test="SubStandardFee != null">#{SubStandardFee},</if>
            <if test="EMRate != null">#{EMRate},</if>
            <if test="ProjectFlag != null">#{ProjectFlag},</if>
            <if test="EndorAcceptNo != null">#{EndorAcceptNo},</if>
            <if test="EndorsementNo != null">#{EndorsementNo},</if>
            <if test="EdorType != null">#{EdorType},</if>
            <if test="EdorValiDate != null">#{EdorValiDate},</if>
            <if test="EdorConfDate != null">#{EdorConfDate},</if>
            <if test="EdorMoney != null">#{EdorMoney},</if>
            <if test="PreInsuredAge != null">#{PreInsuredAge},</if>
            <if test="PreBasicSumInsured != null">#{PreBasicSumInsured},</if>
            <if test="PreRiskAmnt != null">#{PreRiskAmnt},</if>
            <if test="PreReinsuranceAmnt != null">#{PreReinsuranceAmnt},</if>
            <if test="PreRetentionAmount != null">#{PreRetentionAmount},</if>
            <if test="PrePremium != null">#{PrePremium},</if>
            <if test="PreAccountValue != null">#{PreAccountValue},</if>
            <if test="PreWaiverPrem != null">#{PreWaiverPrem},</if>
            <if test="ProjectAcreageChange != null">#{ProjectAcreageChange},</if>
            <if test="ProjectCostChange != null">#{ProjectCostChange},</if>
            <if test="SaparateFlag != null">#{SaparateFlag},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReinsurMode != null">#{ReinsurMode},</if>
            <if test="QuotaSharePercentage != null">#{QuotaSharePercentage},</if>
            <if test="ReinsuranceAmntChange != null">#{ReinsuranceAmntChange},</if>
            <if test="RetentionAmount != null">#{RetentionAmount},</if>
            <if test="ReinsurancePremiumChange != null">#{ReinsurancePremiumChange},</if>
            <if test="ReinsuranceCommssionChange != null">#{ReinsuranceCommssionChange},</if>
            <if test="Currency != null">#{Currency},</if>
            <if test="ReComputationsDate != null">#{ReComputationsDate},</if>
            <if test="AccountGetDate != null">#{AccountGetDate},</if>
            <if test="AccTransNo != null">#{AccTransNo},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
            <if test="CreateTime != null">#{CreateTime},</if>
            <if test="UpdateBy != null">#{UpdateBy},</if>
            <if test="UpdateTime != null">#{UpdateTime},</if>
        </trim>
    </insert>

    <insert id="insertBatchDwsPrpEdor" parameterType="java.util.List">
        insert into t_dws_prp_edor (TransactionNo, CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, TermType, ManageCom, PolYear, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsurePeoples, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, EndorAcceptNo, EndorsementNo, EdorType, EdorValiDate, EdorConfDate, EdorMoney, PreInsuredAge, PreBasicSumInsured, PreRiskAmnt, PreReinsuranceAmnt, PreRetentionAmount, PrePremium, PreAccountValue, PreWaiverPrem, ProjectAcreageChange, ProjectCostChange, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, QuotaSharePercentage, ReinsuranceAmntChange, RetentionAmount, ReinsurancePremiumChange, ReinsuranceCommssionChange, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy) values
        <foreach item="item" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.GrpPolicyNo}, #{item.GrpProductNo}, #{item.PolicyNo}, #{item.ProductNo}, #{item.GPFlag}, #{item.MainProductNo}, #{item.MainProductFlag}, #{item.ProductCode}, #{item.LiabilityCode}, #{item.LiabilityName}, #{item.Classification}, #{item.TermType}, #{item.ManageCom}, #{item.PolYear}, #{item.SignDate}, #{item.EffDate}, #{item.InvalidDate}, #{item.UWConclusion}, #{item.PolStatus}, #{item.Status}, #{item.BasicSumInsured}, #{item.RiskAmnt}, #{item.Premium}, #{item.AccountValue}, #{item.FacultativeFlag}, #{item.AnonymousFlag}, #{item.WaiverFlag}, #{item.WaiverPrem}, #{item.FinalCashValue}, #{item.FinalLiabilityReserve}, #{item.InsurePeoples}, #{item.InsuredNo}, #{item.InsuredName}, #{item.InsuredSex}, #{item.InsuredCertType}, #{item.InsuredCertNo}, #{item.OccupationType}, #{item.AppntAge}, #{item.PreAge}, #{item.ProfessionalFee}, #{item.SubStandardFee}, #{item.EMRate}, #{item.ProjectFlag}, #{item.EndorAcceptNo}, #{item.EndorsementNo}, #{item.EdorType}, #{item.EdorValiDate}, #{item.EdorConfDate}, #{item.EdorMoney}, #{item.PreInsuredAge}, #{item.PreBasicSumInsured}, #{item.PreRiskAmnt}, #{item.PreReinsuranceAmnt}, #{item.PreRetentionAmount}, #{item.PrePremium}, #{item.PreAccountValue}, #{item.PreWaiverPrem}, #{item.ProjectAcreageChange}, #{item.ProjectCostChange}, #{item.SaparateFlag}, #{item.ReInsuranceContNo}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReinsurMode}, #{item.QuotaSharePercentage}, #{item.ReinsuranceAmntChange}, #{item.RetentionAmount}, #{item.ReinsurancePremiumChange}, #{item.ReinsuranceCommssionChange}, #{item.Currency}, #{item.ReComputationsDate}, #{item.AccountGetDate}, #{item.AccTransNo}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.IsDel}, #{item.CreateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpEdor" parameterType="DwsPrpEdorEntity">
        update t_dws_prp_edor
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo = #{GrpPolicyNo},</if>
            <if test="GrpProductNo != null">GrpProductNo = #{GrpProductNo},</if>
            <if test="PolicyNo != null">PolicyNo = #{PolicyNo},</if>
            <if test="ProductNo != null">ProductNo = #{ProductNo},</if>
            <if test="GPFlag != null">GPFlag = #{GPFlag},</if>
            <if test="MainProductNo != null">MainProductNo = #{MainProductNo},</if>
            <if test="MainProductFlag != null">MainProductFlag = #{MainProductFlag},</if>
            <if test="ProductCode != null">ProductCode = #{ProductCode},</if>
            <if test="LiabilityCode != null">LiabilityCode = #{LiabilityCode},</if>
            <if test="LiabilityName != null">LiabilityName = #{LiabilityName},</if>
            <if test="Classification != null">Classification = #{Classification},</if>
            <if test="TermType != null">TermType = #{TermType},</if>
            <if test="ManageCom != null">ManageCom = #{ManageCom},</if>
            <if test="PolYear != null">PolYear = #{PolYear},</if>
            <if test="SignDate != null">SignDate = #{SignDate},</if>
            <if test="EffDate != null">EffDate = #{EffDate},</if>
            <if test="InvalidDate != null">InvalidDate = #{InvalidDate},</if>
            <if test="UWConclusion != null">UWConclusion = #{UWConclusion},</if>
            <if test="PolStatus != null">PolStatus = #{PolStatus},</if>
            <if test="Status != null">Status = #{Status},</if>
            <if test="BasicSumInsured != null">BasicSumInsured = #{BasicSumInsured},</if>
            <if test="RiskAmnt != null">RiskAmnt = #{RiskAmnt},</if>
            <if test="Premium != null">Premium = #{Premium},</if>
            <if test="AccountValue != null">AccountValue = #{AccountValue},</if>
            <if test="FacultativeFlag != null">FacultativeFlag = #{FacultativeFlag},</if>
            <if test="AnonymousFlag != null">AnonymousFlag = #{AnonymousFlag},</if>
            <if test="WaiverFlag != null">WaiverFlag = #{WaiverFlag},</if>
            <if test="WaiverPrem != null">WaiverPrem = #{WaiverPrem},</if>
            <if test="FinalCashValue != null">FinalCashValue = #{FinalCashValue},</if>
            <if test="FinalLiabilityReserve != null">FinalLiabilityReserve = #{FinalLiabilityReserve},</if>
            <if test="InsurePeoples != null">InsurePeoples = #{InsurePeoples},</if>
            <if test="InsuredNo != null">InsuredNo = #{InsuredNo},</if>
            <if test="InsuredName != null">InsuredName = #{InsuredName},</if>
            <if test="InsuredSex != null">InsuredSex = #{InsuredSex},</if>
            <if test="InsuredCertType != null">InsuredCertType = #{InsuredCertType},</if>
            <if test="InsuredCertNo != null">InsuredCertNo = #{InsuredCertNo},</if>
            <if test="OccupationType != null">OccupationType = #{OccupationType},</if>
            <if test="AppntAge != null">AppntAge = #{AppntAge},</if>
            <if test="PreAge != null">PreAge = #{PreAge},</if>
            <if test="ProfessionalFee != null">ProfessionalFee = #{ProfessionalFee},</if>
            <if test="SubStandardFee != null">SubStandardFee = #{SubStandardFee},</if>
            <if test="EMRate != null">EMRate = #{EMRate},</if>
            <if test="ProjectFlag != null">ProjectFlag = #{ProjectFlag},</if>
            <if test="EndorAcceptNo != null">EndorAcceptNo = #{EndorAcceptNo},</if>
            <if test="EndorsementNo != null">EndorsementNo = #{EndorsementNo},</if>
            <if test="EdorType != null">EdorType = #{EdorType},</if>
            <if test="EdorValiDate != null">EdorValiDate = #{EdorValiDate},</if>
            <if test="EdorConfDate != null">EdorConfDate = #{EdorConfDate},</if>
            <if test="EdorMoney != null">EdorMoney = #{EdorMoney},</if>
            <if test="PreInsuredAge != null">PreInsuredAge = #{PreInsuredAge},</if>
            <if test="PreBasicSumInsured != null">PreBasicSumInsured = #{PreBasicSumInsured},</if>
            <if test="PreRiskAmnt != null">PreRiskAmnt = #{PreRiskAmnt},</if>
            <if test="PreReinsuranceAmnt != null">PreReinsuranceAmnt = #{PreReinsuranceAmnt},</if>
            <if test="PreRetentionAmount != null">PreRetentionAmount = #{PreRetentionAmount},</if>
            <if test="PrePremium != null">PrePremium = #{PrePremium},</if>
            <if test="PreAccountValue != null">PreAccountValue = #{PreAccountValue},</if>
            <if test="PreWaiverPrem != null">PreWaiverPrem = #{PreWaiverPrem},</if>
            <if test="ProjectAcreageChange != null">ProjectAcreageChange = #{ProjectAcreageChange},</if>
            <if test="ProjectCostChange != null">ProjectCostChange = #{ProjectCostChange},</if>
            <if test="SaparateFlag != null">SaparateFlag = #{SaparateFlag},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReinsurMode != null">ReinsurMode = #{ReinsurMode},</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage = #{QuotaSharePercentage},</if>
            <if test="ReinsuranceAmntChange != null">ReinsuranceAmntChange = #{ReinsuranceAmntChange},</if>
            <if test="RetentionAmount != null">RetentionAmount = #{RetentionAmount},</if>
            <if test="ReinsurancePremiumChange != null">ReinsurancePremiumChange = #{ReinsurancePremiumChange},</if>
            <if test="ReinsuranceCommssionChange != null">ReinsuranceCommssionChange = #{ReinsuranceCommssionChange},</if>
            <if test="Currency != null">Currency = #{Currency},</if>
            <if test="ReComputationsDate != null">ReComputationsDate = #{ReComputationsDate},</if>
            <if test="AccountGetDate != null">AccountGetDate = #{AccountGetDate},</if>
            <if test="AccTransNo != null">AccTransNo = #{AccTransNo},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            <if test="UpdateTime != null">UpdateTime = #{UpdateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpEdorById" parameterType="Long">
        delete from t_dws_prp_edor where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpEdorByIds" parameterType="String">
        delete from t_dws_prp_edor where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpEdorCountByAccTransNo" resultType="Integer">
        select count(*) from t_dws_prp_edor where IsDel = 0 and AccTransNo = #{AccTransNo}
    </select>

    <select id="selectDwsPrpEdorListByAccTransNo" resultMap="DwsPrpEdorResult">
        <include refid="selectDwsPrpEdorVo"/> where IsDel = 0 and AccTransNo = #{AccTransNo} order by TransactionNo limit #{startRows}, #{pageSize}
    </select>

    <delete id="deleteDwsPrpEdorByAccTransNo" parameterType="String">
        delete from t_dws_prp_edor where AccTransNo = #{AccTransNo}
    </delete>

    <insert id="insertDwsPrpEdorFormTrade" parameterType="DwsPrpAccountDTO">
        insert into t_dws_prp_edor (CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, TermType, ManageCom, PolYear, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsurePeoples, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, EndorAcceptNo, EndorsementNo, EdorType, EdorValiDate, EdorConfDate, EdorMoney, PreInsuredAge, PreBasicSumInsured, PreRiskAmnt, PreReinsuranceAmnt, PreRetentionAmount, PrePremium, PreAccountValue, PreWaiverPrem, ProjectAcreageChange, ProjectCostChange, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, QuotaSharePercentage, ReinsuranceAmntChange, RetentionAmount, ReinsurancePremiumChange, ReinsuranceCommssionChange, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime)

    </insert>

    <insert id="insertPreDwsPrpEdorFormTrade" parameterType="DwsPrpAccountDTO">
        insert into t_dws_prp_edor (CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, TermType, ManageCom, PolYear, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsurePeoples, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, EndorAcceptNo, EndorsementNo, EdorType, EdorValiDate, EdorConfDate, EdorMoney, PreInsuredAge, PreBasicSumInsured, PreRiskAmnt, PreReinsuranceAmnt, PreRetentionAmount, PrePremium, PreAccountValue, PreWaiverPrem, ProjectAcreageChange, ProjectCostChange, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, QuotaSharePercentage, ReinsuranceAmntChange, RetentionAmount, ReinsurancePremiumChange, ReinsuranceCommssionChange, Currency, ReComputationsDate, AccountGetDate, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime)

    </insert>

    <resultMap type="Long" id="DwsPrpEdorIdResult">
        <result property="Id"    column="Id"    />
    </resultMap>
    <select id="selectWaitSetTransNoDwsPrpEdorListByAccTransNo" resultMap="DwsPrpEdorIdResult">
        select Id from t_dws_prp_edor where IsDel = 0 and AccTransNo = #{AccTransNo} and TransactionNo is null limit #{limit}
    </select>

    <update id="updateDwsPrpEdorPushStatus">
        update t_dws_prp_edor set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now() where AccTransNo in
        <foreach item="accTransNo" collection="accTransNos" open="(" separator="," close=")">
            #{accTransNo}
        </foreach>
    </update>

</mapper>
