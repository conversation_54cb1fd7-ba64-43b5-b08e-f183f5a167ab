<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpContMapper">
    
    <resultMap type="DwsPrpContEntity" id="DwsPrpContResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="GrpPolicyNo"    column="GrpPolicyNo"    />
        <result property="GrpProductNo"    column="GrpProductNo"    />
        <result property="PolicyNo"    column="PolicyNo"    />
        <result property="ProductNo"    column="ProductNo"    />
        <result property="GPFlag"    column="GPFlag"    />
        <result property="MainProductNo"    column="MainProductNo"    />
        <result property="MainProductFlag"    column="MainProductFlag"    />
        <result property="ProductCode"    column="ProductCode"    />
        <result property="LiabilityCode"    column="LiabilityCode"    />
        <result property="LiabilityName"    column="LiabilityName"    />
        <result property="Classification"    column="Classification"    />
        <result property="EventType"    column="EventType"    />
        <result property="PolYear"    column="PolYear"    />
        <result property="RenewalTimes"    column="RenewalTimes"    />
        <result property="TermType"    column="TermType"    />
        <result property="ManageCom"    column="ManageCom"    />
        <result property="SignDate"    column="SignDate"    />
        <result property="EffDate"    column="EffDate"    />
        <result property="InvalidDate"    column="InvalidDate"    />
        <result property="UWConclusion"    column="UWConclusion"    />
        <result property="PolStatus"    column="PolStatus"    />
        <result property="Status"    column="Status"    />
        <result property="BasicSumInsured"    column="BasicSumInsured"    />
        <result property="RiskAmnt"    column="RiskAmnt"    />
        <result property="Premium"    column="Premium"    />
        <result property="AccountValue"    column="AccountValue"    />
        <result property="FacultativeFlag"    column="FacultativeFlag"    />
        <result property="AnonymousFlag"    column="AnonymousFlag"    />
        <result property="WaiverFlag"    column="WaiverFlag"    />
        <result property="WaiverPrem"    column="WaiverPrem"    />
        <result property="FinalCashValue"    column="FinalCashValue"    />
        <result property="FinalLiabilityReserve"    column="FinalLiabilityReserve"    />
        <result property="InsuredNo"    column="InsuredNo"    />
        <result property="InsuredName"    column="InsuredName"    />
        <result property="InsuredSex"    column="InsuredSex"    />
        <result property="InsuredCertType"    column="InsuredCertType"    />
        <result property="InsuredCertNo"    column="InsuredCertNo"    />
        <result property="OccupationType"    column="OccupationType"    />
        <result property="AppntAge"    column="AppntAge"    />
        <result property="PreAge"    column="PreAge"    />
        <result property="ProfessionalFee"    column="ProfessionalFee"    />
        <result property="SubStandardFee"    column="SubStandardFee"    />
        <result property="EMRate"    column="EMRate"    />
        <result property="ProjectFlag"    column="ProjectFlag"    />
        <result property="InsurePeoples"    column="InsurePeoples"    />
        <result property="SaparateFlag"    column="SaparateFlag"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ReinsurMode"    column="ReinsurMode"    />
        <result property="RetentionAmount"    column="RetentionAmount"    />
        <result property="ReinsuranceAmnt"    column="ReinsuranceAmnt"    />
        <result property="QuotaSharePercentage"    column="QuotaSharePercentage"    />
        <result property="ReinsurancePremium"    column="ReinsurancePremium"    />
        <result property="ReinsuranceCommssion"    column="ReinsuranceCommssion"    />
        <result property="ReComputationsDate"    column="ReComputationsDate"    />
        <result property="AccountGetDate"    column="AccountGetDate"    />
        <result property="Currency"    column="Currency"    />
        <result property="AccTransNo"    column="AccTransNo"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpContVo">
        select Id, TransactionNo, CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, EventType, PolYear, RenewalTimes, TermType, ManageCom, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, InsurePeoples, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, RetentionAmount, ReinsuranceAmnt, QuotaSharePercentage, ReinsurancePremium, ReinsuranceCommssion, ReComputationsDate, AccountGetDate, Currency, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_cont
    </sql>

    <select id="selectDwsPrpContList" parameterType="DwsPrpContQuery" resultMap="DwsPrpContResult">
        <include refid="selectDwsPrpContVo"/>
        <where>  
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo like concat('%', #{TransactionNo}, '%')</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="GrpPolicyNo != null  and GrpPolicyNo != ''"> and GrpPolicyNo like concat('%', #{GrpPolicyNo}, '%')</if>
            <if test="GrpProductNo != null  and GrpProductNo != ''"> and GrpProductNo like concat('%', #{GrpProductNo}, '%')</if>
            <if test="PolicyNo != null  and PolicyNo != ''"> and PolicyNo like concat('%', #{PolicyNo}, '%')</if>
            <if test="ProductNo != null  and ProductNo != ''"> and ProductNo like concat('%', #{ProductNo}, '%')</if>
            <if test="GPFlag != null  and GPFlag != ''"> and GPFlag = #{GPFlag}</if>
            <if test="MainProductNo != null  and MainProductNo != ''"> and MainProductNo like concat('%', #{MainProductNo}, '%')</if>
            <if test="MainProductFlag != null  and MainProductFlag != ''"> and MainProductFlag = #{MainProductFlag}</if>
            <if test="ProductCode != null  and ProductCode != ''"> and ProductCode like concat('%', #{ProductCode}, '%')</if>
            <if test="LiabilityCode != null  and LiabilityCode != ''"> and LiabilityCode like concat('%', #{LiabilityCode}, '%')</if>
            <if test="LiabilityName != null  and LiabilityName != ''"> and LiabilityName like concat('%', #{LiabilityName}, '%')</if>
            <if test="Classification != null  and Classification != ''"> and Classification like concat('%', #{Classification}, '%')</if>
            <if test="EventType != null  and EventType != ''"> and EventType = #{EventType}</if>
            <if test="PolYear != null "> and PolYear = #{PolYear}</if>
            <if test="RenewalTimes != null "> and RenewalTimes = #{RenewalTimes}</if>
            <if test="TermType != null  and TermType != ''"> and TermType = #{TermType}</if>
            <if test="ManageCom != null  and ManageCom != ''"> and ManageCom = #{ManageCom}</if>
            <if test="SignDate != null "> and SignDate = #{SignDate}</if>
            <if test="EffDate != null "> and EffDate = #{EffDate}</if>
            <if test="InvalidDate != null "> and InvalidDate = #{InvalidDate}</if>
            <if test="UWConclusion != null  and UWConclusion != ''"> and UWConclusion = #{UWConclusion}</if>
            <if test="PolStatus != null  and PolStatus != ''"> and PolStatus = #{PolStatus}</if>
            <if test="Status != null  and Status != ''"> and Status = #{Status}</if>
            <if test="BasicSumInsured != null "> and BasicSumInsured = #{BasicSumInsured}</if>
            <if test="RiskAmnt != null "> and RiskAmnt = #{RiskAmnt}</if>
            <if test="Premium != null "> and Premium = #{Premium}</if>
            <if test="AccountValue != null "> and AccountValue = #{AccountValue}</if>
            <if test="FacultativeFlag != null  and FacultativeFlag != ''"> and FacultativeFlag = #{FacultativeFlag}</if>
            <if test="AnonymousFlag != null  and AnonymousFlag != ''"> and AnonymousFlag = #{AnonymousFlag}</if>
            <if test="WaiverFlag != null  and WaiverFlag != ''"> and WaiverFlag = #{WaiverFlag}</if>
            <if test="WaiverPrem != null "> and WaiverPrem = #{WaiverPrem}</if>
            <if test="FinalCashValue != null "> and FinalCashValue = #{FinalCashValue}</if>
            <if test="FinalLiabilityReserve != null "> and FinalLiabilityReserve = #{FinalLiabilityReserve}</if>
            <if test="InsuredNo != null  and InsuredNo != ''"> and InsuredNo like concat('%', #{InsuredNo}, '%')</if>
            <if test="InsuredName != null  and InsuredName != ''"> and InsuredName like concat('%', #{InsuredName}, '%')</if>
            <if test="InsuredSex != null  and InsuredSex != ''"> and InsuredSex = #{InsuredSex}</if>
            <if test="InsuredCertType != null  and InsuredCertType != ''"> and InsuredCertType = #{InsuredCertType}</if>
            <if test="InsuredCertNo != null  and InsuredCertNo != ''"> and InsuredCertNo like concat('%', #{InsuredCertNo}, '%')</if>
            <if test="OccupationType != null  and OccupationType != ''"> and OccupationType = #{OccupationType}</if>
            <if test="AppntAge != null "> and AppntAge = #{AppntAge}</if>
            <if test="PreAge != null "> and PreAge = #{PreAge}</if>
            <if test="ProfessionalFee != null "> and ProfessionalFee = #{ProfessionalFee}</if>
            <if test="SubStandardFee != null "> and SubStandardFee = #{SubStandardFee}</if>
            <if test="EMRate != null "> and EMRate = #{EMRate}</if>
            <if test="ProjectFlag != null  and ProjectFlag != ''"> and ProjectFlag = #{ProjectFlag}</if>
            <if test="InsurePeoples != null "> and InsurePeoples = #{InsurePeoples}</if>
            <if test="SaparateFlag != null  and SaparateFlag != ''"> and SaparateFlag = #{SaparateFlag}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo like concat('%', #{ReInsuranceContNo}, '%')</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode like concat('%', #{ReinsurerCode}, '%')</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ReinsurMode != null  and ReinsurMode != ''"> and ReinsurMode = #{ReinsurMode}</if>
            <if test="RetentionAmount != null "> and RetentionAmount = #{RetentionAmount}</if>
            <if test="ReinsuranceAmnt != null "> and ReinsuranceAmnt = #{ReinsuranceAmnt}</if>
            <if test="QuotaSharePercentage != null  and QuotaSharePercentage != ''"> and QuotaSharePercentage = #{QuotaSharePercentage}</if>
            <if test="ReinsurancePremium != null "> and ReinsurancePremium = #{ReinsurancePremium}</if>
            <if test="ReinsuranceCommssion != null "> and ReinsuranceCommssion = #{ReinsuranceCommssion}</if>
            <if test="ReComputationsDate != null "> and ReComputationsDate = #{ReComputationsDate}</if>
            <if test="AccountGetDate != null "> and AccountGetDate = #{AccountGetDate}</if>
            <if test="Currency != null  and Currency != ''"> and Currency = #{Currency}</if>
            <if test="AccTransNo != null  and AccTransNo != ''"> and AccTransNo like concat('%', #{AccTransNo}, '%')</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark like concat('%', #{Remark}, '%')</if>
            <if test="CreateBy != null  and CreateBy != ''"> and CreateBy = #{CreateBy}</if>
            <if test="CreateTime != null "> and CreateTime = #{CreateTime}</if>
            <if test="UpdateBy != null  and UpdateBy != ''"> and UpdateBy = #{UpdateBy}</if>
            <if test="UpdateTime != null "> and UpdateTime = #{UpdateTime}</if>
            and IsDel = 0
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpContById" parameterType="Long" resultMap="DwsPrpContResult">
        <include refid="selectDwsPrpContVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpContByIds" parameterType="Long" resultMap="DwsPrpContResult">
        <include refid="selectDwsPrpContVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpCont" parameterType="DwsPrpContEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_cont
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo,</if>
            <if test="GrpProductNo != null">GrpProductNo,</if>
            <if test="PolicyNo != null">PolicyNo,</if>
            <if test="ProductNo != null">ProductNo,</if>
            <if test="GPFlag != null">GPFlag,</if>
            <if test="MainProductNo != null">MainProductNo,</if>
            <if test="MainProductFlag != null">MainProductFlag,</if>
            <if test="ProductCode != null">ProductCode,</if>
            <if test="LiabilityCode != null">LiabilityCode,</if>
            <if test="LiabilityName != null">LiabilityName,</if>
            <if test="Classification != null">Classification,</if>
            <if test="EventType != null">EventType,</if>
            <if test="PolYear != null">PolYear,</if>
            <if test="RenewalTimes != null">RenewalTimes,</if>
            <if test="TermType != null">TermType,</if>
            <if test="ManageCom != null">ManageCom,</if>
            <if test="SignDate != null">SignDate,</if>
            <if test="EffDate != null">EffDate,</if>
            <if test="InvalidDate != null">InvalidDate,</if>
            <if test="UWConclusion != null">UWConclusion,</if>
            <if test="PolStatus != null">PolStatus,</if>
            <if test="Status != null">Status,</if>
            <if test="BasicSumInsured != null">BasicSumInsured,</if>
            <if test="RiskAmnt != null">RiskAmnt,</if>
            <if test="Premium != null">Premium,</if>
            <if test="AccountValue != null">AccountValue,</if>
            <if test="FacultativeFlag != null">FacultativeFlag,</if>
            <if test="AnonymousFlag != null">AnonymousFlag,</if>
            <if test="WaiverFlag != null">WaiverFlag,</if>
            <if test="WaiverPrem != null">WaiverPrem,</if>
            <if test="FinalCashValue != null">FinalCashValue,</if>
            <if test="FinalLiabilityReserve != null">FinalLiabilityReserve,</if>
            <if test="InsuredNo != null">InsuredNo,</if>
            <if test="InsuredName != null">InsuredName,</if>
            <if test="InsuredSex != null">InsuredSex,</if>
            <if test="InsuredCertType != null">InsuredCertType,</if>
            <if test="InsuredCertNo != null">InsuredCertNo,</if>
            <if test="OccupationType != null">OccupationType,</if>
            <if test="AppntAge != null">AppntAge,</if>
            <if test="PreAge != null">PreAge,</if>
            <if test="ProfessionalFee != null">ProfessionalFee,</if>
            <if test="SubStandardFee != null">SubStandardFee,</if>
            <if test="EMRate != null">EMRate,</if>
            <if test="ProjectFlag != null">ProjectFlag,</if>
            <if test="InsurePeoples != null">InsurePeoples,</if>
            <if test="SaparateFlag != null">SaparateFlag,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ReinsurMode != null">ReinsurMode,</if>
            <if test="RetentionAmount != null">RetentionAmount,</if>
            <if test="ReinsuranceAmnt != null">ReinsuranceAmnt,</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage,</if>
            <if test="ReinsurancePremium != null">ReinsurancePremium,</if>
            <if test="ReinsuranceCommssion != null">ReinsuranceCommssion,</if>
            <if test="ReComputationsDate != null">ReComputationsDate,</if>
            <if test="AccountGetDate != null">AccountGetDate,</if>
            <if test="Currency != null">Currency,</if>
            <if test="AccTransNo != null">AccTransNo,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
            <if test="CreateTime != null">CreateTime,</if>
            <if test="UpdateBy != null">UpdateBy,</if>
            <if test="UpdateTime != null">UpdateTime,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="GrpPolicyNo != null">#{GrpPolicyNo},</if>
            <if test="GrpProductNo != null">#{GrpProductNo},</if>
            <if test="PolicyNo != null">#{PolicyNo},</if>
            <if test="ProductNo != null">#{ProductNo},</if>
            <if test="GPFlag != null">#{GPFlag},</if>
            <if test="MainProductNo != null">#{MainProductNo},</if>
            <if test="MainProductFlag != null">#{MainProductFlag},</if>
            <if test="ProductCode != null">#{ProductCode},</if>
            <if test="LiabilityCode != null">#{LiabilityCode},</if>
            <if test="LiabilityName != null">#{LiabilityName},</if>
            <if test="Classification != null">#{Classification},</if>
            <if test="EventType != null">#{EventType},</if>
            <if test="PolYear != null">#{PolYear},</if>
            <if test="RenewalTimes != null">#{RenewalTimes},</if>
            <if test="TermType != null">#{TermType},</if>
            <if test="ManageCom != null">#{ManageCom},</if>
            <if test="SignDate != null">#{SignDate},</if>
            <if test="EffDate != null">#{EffDate},</if>
            <if test="InvalidDate != null">#{InvalidDate},</if>
            <if test="UWConclusion != null">#{UWConclusion},</if>
            <if test="PolStatus != null">#{PolStatus},</if>
            <if test="Status != null">#{Status},</if>
            <if test="BasicSumInsured != null">#{BasicSumInsured},</if>
            <if test="RiskAmnt != null">#{RiskAmnt},</if>
            <if test="Premium != null">#{Premium},</if>
            <if test="AccountValue != null">#{AccountValue},</if>
            <if test="FacultativeFlag != null">#{FacultativeFlag},</if>
            <if test="AnonymousFlag != null">#{AnonymousFlag},</if>
            <if test="WaiverFlag != null">#{WaiverFlag},</if>
            <if test="WaiverPrem != null">#{WaiverPrem},</if>
            <if test="FinalCashValue != null">#{FinalCashValue},</if>
            <if test="FinalLiabilityReserve != null">#{FinalLiabilityReserve},</if>
            <if test="InsuredNo != null">#{InsuredNo},</if>
            <if test="InsuredName != null">#{InsuredName},</if>
            <if test="InsuredSex != null">#{InsuredSex},</if>
            <if test="InsuredCertType != null">#{InsuredCertType},</if>
            <if test="InsuredCertNo != null">#{InsuredCertNo},</if>
            <if test="OccupationType != null">#{OccupationType},</if>
            <if test="AppntAge != null">#{AppntAge},</if>
            <if test="PreAge != null">#{PreAge},</if>
            <if test="ProfessionalFee != null">#{ProfessionalFee},</if>
            <if test="SubStandardFee != null">#{SubStandardFee},</if>
            <if test="EMRate != null">#{EMRate},</if>
            <if test="ProjectFlag != null">#{ProjectFlag},</if>
            <if test="InsurePeoples != null">#{InsurePeoples},</if>
            <if test="SaparateFlag != null">#{SaparateFlag},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ReinsurMode != null">#{ReinsurMode},</if>
            <if test="RetentionAmount != null">#{RetentionAmount},</if>
            <if test="ReinsuranceAmnt != null">#{ReinsuranceAmnt},</if>
            <if test="QuotaSharePercentage != null">#{QuotaSharePercentage},</if>
            <if test="ReinsurancePremium != null">#{ReinsurancePremium},</if>
            <if test="ReinsuranceCommssion != null">#{ReinsuranceCommssion},</if>
            <if test="ReComputationsDate != null">#{ReComputationsDate},</if>
            <if test="AccountGetDate != null">#{AccountGetDate},</if>
            <if test="Currency != null">#{Currency},</if>
            <if test="AccTransNo != null">#{AccTransNo},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
            <if test="CreateTime != null">#{CreateTime},</if>
            <if test="UpdateBy != null">#{UpdateBy},</if>
            <if test="UpdateTime != null">#{UpdateTime},</if>
        </trim>
    </insert>

    <insert id="insertBatchDwsPrpCont" parameterType="java.util.List">
        insert into t_dws_prp_cont(TransactionNo, CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, EventType, PolYear, RenewalTimes, TermType, ManageCom, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, InsurePeoples, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, RetentionAmount, ReinsuranceAmnt, QuotaSharePercentage, ReinsurancePremium, ReinsuranceCommssion, ReComputationsDate, AccountGetDate, Currency, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.GrpPolicyNo}, #{item.GrpProductNo}, #{item.PolicyNo}, #{item.ProductNo}, #{item.GPFlag}, #{item.MainProductNo}, #{item.MainProductFlag}, #{item.ProductCode}, #{item.LiabilityCode}, #{item.LiabilityName}, #{item.Classification}, #{item.EventType}, #{item.PolYear}, #{item.RenewalTimes}, #{item.TermType}, #{item.ManageCom}, #{item.SignDate}, #{item.EffDate}, #{item.InvalidDate}, #{item.UWConclusion}, #{item.PolStatus}, #{item.Status}, #{item.BasicSumInsured}, #{item.RiskAmnt}, #{item.Premium}, #{item.AccountValue}, #{item.FacultativeFlag}, #{item.AnonymousFlag}, #{item.WaiverFlag}, #{item.WaiverPrem}, #{item.FinalCashValue}, #{item.FinalLiabilityReserve}, #{item.InsuredNo}, #{item.InsuredName}, #{item.InsuredSex}, #{item.InsuredCertType}, #{item.InsuredCertNo}, #{item.OccupationType}, #{item.AppntAge}, #{item.PreAge}, #{item.ProfessionalFee}, #{item.SubStandardFee}, #{item.EMRate}, #{item.ProjectFlag}, #{item.InsurePeoples}, #{item.SaparateFlag}, #{item.ReInsuranceContNo}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ReinsurMode}, #{item.RetentionAmount}, #{item.ReinsuranceAmnt}, #{item.QuotaSharePercentage}, #{item.ReinsurancePremium}, #{item.ReinsuranceCommssion}, #{item.ReComputationsDate}, #{item.AccountGetDate}, #{item.Currency}, #{item.AccTransNo}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.IsDel}, #{item.CreateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpCont" parameterType="DwsPrpContEntity">
        update t_dws_prp_cont
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="GrpPolicyNo != null">GrpPolicyNo = #{GrpPolicyNo},</if>
            <if test="GrpProductNo != null">GrpProductNo = #{GrpProductNo},</if>
            <if test="PolicyNo != null">PolicyNo = #{PolicyNo},</if>
            <if test="ProductNo != null">ProductNo = #{ProductNo},</if>
            <if test="GPFlag != null">GPFlag = #{GPFlag},</if>
            <if test="MainProductNo != null">MainProductNo = #{MainProductNo},</if>
            <if test="MainProductFlag != null">MainProductFlag = #{MainProductFlag},</if>
            <if test="ProductCode != null">ProductCode = #{ProductCode},</if>
            <if test="LiabilityCode != null">LiabilityCode = #{LiabilityCode},</if>
            <if test="LiabilityName != null">LiabilityName = #{LiabilityName},</if>
            <if test="Classification != null">Classification = #{Classification},</if>
            <if test="EventType != null">EventType = #{EventType},</if>
            <if test="PolYear != null">PolYear = #{PolYear},</if>
            <if test="RenewalTimes != null">RenewalTimes = #{RenewalTimes},</if>
            <if test="TermType != null">TermType = #{TermType},</if>
            <if test="ManageCom != null">ManageCom = #{ManageCom},</if>
            <if test="SignDate != null">SignDate = #{SignDate},</if>
            <if test="EffDate != null">EffDate = #{EffDate},</if>
            <if test="InvalidDate != null">InvalidDate = #{InvalidDate},</if>
            <if test="UWConclusion != null">UWConclusion = #{UWConclusion},</if>
            <if test="PolStatus != null">PolStatus = #{PolStatus},</if>
            <if test="Status != null">Status = #{Status},</if>
            <if test="BasicSumInsured != null">BasicSumInsured = #{BasicSumInsured},</if>
            <if test="RiskAmnt != null">RiskAmnt = #{RiskAmnt},</if>
            <if test="Premium != null">Premium = #{Premium},</if>
            <if test="AccountValue != null">AccountValue = #{AccountValue},</if>
            <if test="FacultativeFlag != null">FacultativeFlag = #{FacultativeFlag},</if>
            <if test="AnonymousFlag != null">AnonymousFlag = #{AnonymousFlag},</if>
            <if test="WaiverFlag != null">WaiverFlag = #{WaiverFlag},</if>
            <if test="WaiverPrem != null">WaiverPrem = #{WaiverPrem},</if>
            <if test="FinalCashValue != null">FinalCashValue = #{FinalCashValue},</if>
            <if test="FinalLiabilityReserve != null">FinalLiabilityReserve = #{FinalLiabilityReserve},</if>
            <if test="InsuredNo != null">InsuredNo = #{InsuredNo},</if>
            <if test="InsuredName != null">InsuredName = #{InsuredName},</if>
            <if test="InsuredSex != null">InsuredSex = #{InsuredSex},</if>
            <if test="InsuredCertType != null">InsuredCertType = #{InsuredCertType},</if>
            <if test="InsuredCertNo != null">InsuredCertNo = #{InsuredCertNo},</if>
            <if test="OccupationType != null">OccupationType = #{OccupationType},</if>
            <if test="AppntAge != null">AppntAge = #{AppntAge},</if>
            <if test="PreAge != null">PreAge = #{PreAge},</if>
            <if test="ProfessionalFee != null">ProfessionalFee = #{ProfessionalFee},</if>
            <if test="SubStandardFee != null">SubStandardFee = #{SubStandardFee},</if>
            <if test="EMRate != null">EMRate = #{EMRate},</if>
            <if test="ProjectFlag != null">ProjectFlag = #{ProjectFlag},</if>
            <if test="InsurePeoples != null">InsurePeoples = #{InsurePeoples},</if>
            <if test="SaparateFlag != null">SaparateFlag = #{SaparateFlag},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ReinsurMode != null">ReinsurMode = #{ReinsurMode},</if>
            <if test="RetentionAmount != null">RetentionAmount = #{RetentionAmount},</if>
            <if test="ReinsuranceAmnt != null">ReinsuranceAmnt = #{ReinsuranceAmnt},</if>
            <if test="QuotaSharePercentage != null">QuotaSharePercentage = #{QuotaSharePercentage},</if>
            <if test="ReinsurancePremium != null">ReinsurancePremium = #{ReinsurancePremium},</if>
            <if test="ReinsuranceCommssion != null">ReinsuranceCommssion = #{ReinsuranceCommssion},</if>
            <if test="ReComputationsDate != null">ReComputationsDate = #{ReComputationsDate},</if>
            <if test="AccountGetDate != null">AccountGetDate = #{AccountGetDate},</if>
            <if test="Currency != null">Currency = #{Currency},</if>
            <if test="AccTransNo != null">AccTransNo = #{AccTransNo},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            <if test="UpdateTime != null">UpdateTime = #{UpdateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpContById" parameterType="Long">
        delete from t_dws_prp_cont where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpContByIds" parameterType="String">
        delete from t_dws_prp_cont where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpContCountByAccTransNo" resultType="Integer">
        select count(*) from t_dws_prp_cont where IsDel = 0 and AccTransNo = #{AccTransNo}
    </select>

    <select id="selectDwsPrpContListByAccTransNo" resultMap="DwsPrpContResult">
        <include refid="selectDwsPrpContVo"/> where IsDel = 0 and AccTransNo = #{AccTransNo} order by TransactionNo limit #{startRows}, #{pageSize}
    </select>

    <delete id="deleteDwsPrpContByAccTransNo" parameterType="String">
        delete from t_dws_prp_cont where AccTransNo = #{AccTransNo}
    </delete>

    <insert id="insertDwsPrpContFormTrade" parameterType="DwsPrpAccountDTO">
        insert into t_dws_prp_cont (CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, EventType, PolYear, RenewalTimes, TermType, ManageCom, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, InsurePeoples, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, RetentionAmount, ReinsuranceAmnt, QuotaSharePercentage, ReinsurancePremium, ReinsuranceCommssion, ReComputationsDate, AccountGetDate, Currency, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime)

    </insert>

    <insert id="insertPreDwsPrpContFormTrade" parameterType="DwsPrpAccountDTO">
        insert into t_dws_prp_cont (CompanyCode, GrpPolicyNo, GrpProductNo, PolicyNo, ProductNo, GPFlag, MainProductNo, MainProductFlag, ProductCode, LiabilityCode, LiabilityName, Classification, EventType, PolYear, RenewalTimes, TermType, ManageCom, SignDate, EffDate, InvalidDate, UWConclusion, PolStatus, Status, BasicSumInsured, RiskAmnt, Premium, AccountValue, FacultativeFlag, AnonymousFlag, WaiverFlag, WaiverPrem, FinalCashValue, FinalLiabilityReserve, InsuredNo, InsuredName, InsuredSex, InsuredCertType, InsuredCertNo, OccupationType, AppntAge, PreAge, ProfessionalFee, SubStandardFee, EMRate, ProjectFlag, InsurePeoples, SaparateFlag, ReInsuranceContNo, ReinsurerCode, ReinsurerName, ReinsurMode, RetentionAmount, ReinsuranceAmnt, QuotaSharePercentage, ReinsurancePremium, ReinsuranceCommssion, ReComputationsDate, AccountGetDate, Currency, AccTransNo, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime)

    </insert>

    <resultMap type="Long" id="DwsPrpContIdResult">
        <result property="Id"    column="Id"    />
    </resultMap>
    <select id="selectWaitSetTransNoDwsPrpContListByAccTransNo" resultMap="DwsPrpContIdResult">
        select Id from t_dws_prp_cont where IsDel = 0 and AccTransNo = #{AccTransNo} and TransactionNo is null limit #{limit}
    </select>

    <update id="updateDwsPrpContPushStatus">
        update t_dws_prp_cont set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now() where AccTransNo in
        <foreach item="accTransNo" collection="accTransNos" open="(" separator="," close=")">
            #{accTransNo}
        </foreach>
    </update>


</mapper>
