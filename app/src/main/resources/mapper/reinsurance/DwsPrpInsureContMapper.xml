<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.reinsurance.mapper.DwsPrpInsureContMapper">
    
    <resultMap type="DwsPrpInsureContEntity" id="DwsPrpInsureContResult">
        <result property="Id"    column="Id"    />
        <result property="TransactionNo"    column="TransactionNo"    />
        <result property="CompanyCode"    column="CompanyCode"    />
        <result property="ReInsuranceContNo"    column="ReInsuranceContNo"    />
        <result property="ReInsuranceContName"    column="ReInsuranceContName"    />
        <result property="ReInsuranceContTitle"    column="ReInsuranceContTitle"    />
        <result property="MainReInsuranceContNo"    column="MainReInsuranceContNo"    />
        <result property="ContOrAmendmentType"    column="ContOrAmendmentType"    />
        <result property="ContAttribute"    column="ContAttribute"    />
        <result property="ContStatus"    column="ContStatus"    />
        <result property="TreatyOrFacultativeFlag"    column="TreatyOrFacultativeFlag"    />
        <result property="ContSigndate"    column="ContSigndate"    />
        <result property="PeriodFrom"    column="PeriodFrom"    />
        <result property="PeriodTo"    column="PeriodTo"    />
        <result property="ContType"    column="ContType"    />
        <result property="ReinsurerCode"    column="ReinsurerCode"    />
        <result property="ReinsurerName"    column="ReinsurerName"    />
        <result property="ChargeType"    column="ChargeType"    />
        <result property="ReportYear"    column="ReportYear"    />
        <result property="ReportMonth"    column="ReportMonth"    />
        <result property="AccountPeriod"    column="AccountPeriod"    />
        <result property="DataSource"    column="DataSource"    />
        <result property="PushStatus"    column="PushStatus"    />
        <result property="PushDate"    column="PushDate"    />
        <result property="PushBy"    column="PushBy"    />
        <result property="Remark"    column="Remark"    />
        <result property="IsDel"    column="IsDel"    />
        <result property="CreateBy"    column="CreateBy"    />
        <result property="CreateTime"    column="CreateTime"    />
        <result property="UpdateBy"    column="UpdateBy"    />
        <result property="UpdateTime"    column="UpdateTime"    />
    </resultMap>

    <sql id="selectDwsPrpInsureContVo">
        select Id, TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ContAttribute, ContStatus, TreatyOrFacultativeFlag, ContSigndate, PeriodFrom, PeriodTo, ContType, ReinsurerCode, ReinsurerName, ChargeType, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy, CreateTime, UpdateBy, UpdateTime from t_dws_prp_insure_cont
    </sql>

    <select id="selectDwsPrpInsureContList" parameterType="DwsPrpInsureContQuery" resultMap="DwsPrpInsureContResult">
        <include refid="selectDwsPrpInsureContVo"/>
        <where>  
            <if test="Id != null "> and Id = #{Id}</if>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo like concat('%', #{TransactionNo}, '%')</if>
            <if test="CompanyCode != null  and CompanyCode != ''"> and CompanyCode = #{CompanyCode}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo like concat('%', #{ReInsuranceContNo}, '%')</if>
            <if test="ReInsuranceContName != null  and ReInsuranceContName != ''"> and ReInsuranceContName like concat('%', #{ReInsuranceContName}, '%')</if>
            <if test="ReInsuranceContTitle != null  and ReInsuranceContTitle != ''"> and ReInsuranceContTitle like concat('%', #{ReInsuranceContTitle}, '%')</if>
            <if test="MainReInsuranceContNo != null  and MainReInsuranceContNo != ''"> and MainReInsuranceContNo like concat('%', #{MainReInsuranceContNo}, '%')</if>
            <if test="ContOrAmendmentType != null  and ContOrAmendmentType != ''"> and ContOrAmendmentType = #{ContOrAmendmentType}</if>
            <if test="ContAttribute != null  and ContAttribute != ''"> and ContAttribute = #{ContAttribute}</if>
            <if test="ContStatus != null  and ContStatus != ''"> and ContStatus = #{ContStatus}</if>
            <if test="TreatyOrFacultativeFlag != null  and TreatyOrFacultativeFlag != ''"> and TreatyOrFacultativeFlag = #{TreatyOrFacultativeFlag}</if>
            <if test="ContSigndate != null "> and ContSigndate = #{ContSigndate}</if>
            <if test="PeriodFrom != null "> and PeriodFrom = #{PeriodFrom}</if>
            <if test="PeriodTo != null "> and PeriodTo = #{PeriodTo}</if>
            <if test="ContType != null  and ContType != ''"> and ContType = #{ContType}</if>
            <if test="ReinsurerCode != null  and ReinsurerCode != ''"> and ReinsurerCode like concat('%', #{ReinsurerCode}, '%')</if>
            <if test="ReinsurerName != null  and ReinsurerName != ''"> and ReinsurerName like concat('%', #{ReinsurerName}, '%')</if>
            <if test="ChargeType != null  and ChargeType != ''"> and ChargeType = #{ChargeType}</if>
            <if test="ReportYear != null "> and ReportYear = #{ReportYear}</if>
            <if test="ReportMonth != null "> and ReportMonth = #{ReportMonth}</if>
            <if test="AccountPeriod != null  and AccountPeriod != ''"> and AccountPeriod = #{AccountPeriod}</if>
            <if test="DataSource != null "> and DataSource = #{DataSource}</if>
            <if test="PushStatus != null "> and PushStatus = #{PushStatus}</if>
            <if test="PushDate != null "> and PushDate = #{PushDate}</if>
            <if test="PushBy != null  and PushBy != ''"> and PushBy = #{PushBy}</if>
            <if test="Remark != null  and Remark != ''"> and Remark like concat('%', #{Remark}, '%')</if>
            <if test="CreateBy != null  and CreateBy != ''"> and CreateBy = #{CreateBy}</if>
            <if test="CreateTime != null "> and CreateTime = #{CreateTime}</if>
            <if test="UpdateBy != null  and UpdateBy != ''"> and UpdateBy = #{UpdateBy}</if>
            <if test="UpdateTime != null "> and UpdateTime = #{UpdateTime}</if>
            and IsDel = 0
        </where>
        order by Id desc
    </select>
    
    <select id="selectDwsPrpInsureContById" parameterType="Long" resultMap="DwsPrpInsureContResult">
        <include refid="selectDwsPrpInsureContVo"/>
        where Id = #{Id}
    </select>

    <select id="selectDwsPrpInsureContByIds" parameterType="Long" resultMap="DwsPrpInsureContResult">
        <include refid="selectDwsPrpInsureContVo"/>
        where Id in
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </select>

    <insert id="insertDwsPrpInsureCont" parameterType="DwsPrpInsureContEntity" useGeneratedKeys="true" keyProperty="Id">
        insert into t_dws_prp_insure_cont
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo,</if>
            <if test="CompanyCode != null">CompanyCode,</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo,</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName,</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle,</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo,</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType,</if>
            <if test="ContAttribute != null">ContAttribute,</if>
            <if test="ContStatus != null">ContStatus,</if>
            <if test="TreatyOrFacultativeFlag != null">TreatyOrFacultativeFlag,</if>
            <if test="ContSigndate != null">ContSigndate,</if>
            <if test="PeriodFrom != null">PeriodFrom,</if>
            <if test="PeriodTo != null">PeriodTo,</if>
            <if test="ContType != null">ContType,</if>
            <if test="ReinsurerCode != null">ReinsurerCode,</if>
            <if test="ReinsurerName != null">ReinsurerName,</if>
            <if test="ChargeType != null">ChargeType,</if>
            <if test="ReportYear != null">ReportYear,</if>
            <if test="ReportMonth != null">ReportMonth,</if>
            <if test="AccountPeriod != null">AccountPeriod,</if>
            <if test="DataSource != null">DataSource,</if>
            <if test="PushStatus != null">PushStatus,</if>
            <if test="PushDate != null">PushDate,</if>
            <if test="PushBy != null">PushBy,</if>
            <if test="Remark != null">Remark,</if>
            <if test="IsDel != null">IsDel,</if>
            <if test="CreateBy != null">CreateBy,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="TransactionNo != null">#{TransactionNo},</if>
            <if test="CompanyCode != null">#{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">#{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">#{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">#{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">#{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">#{ContOrAmendmentType},</if>
            <if test="ContAttribute != null">#{ContAttribute},</if>
            <if test="ContStatus != null">#{ContStatus},</if>
            <if test="TreatyOrFacultativeFlag != null">#{TreatyOrFacultativeFlag},</if>
            <if test="ContSigndate != null">#{ContSigndate},</if>
            <if test="PeriodFrom != null">#{PeriodFrom},</if>
            <if test="PeriodTo != null">#{PeriodTo},</if>
            <if test="ContType != null">#{ContType},</if>
            <if test="ReinsurerCode != null">#{ReinsurerCode},</if>
            <if test="ReinsurerName != null">#{ReinsurerName},</if>
            <if test="ChargeType != null">#{ChargeType},</if>
            <if test="ReportYear != null">#{ReportYear},</if>
            <if test="ReportMonth != null">#{ReportMonth},</if>
            <if test="AccountPeriod != null">#{AccountPeriod},</if>
            <if test="DataSource != null">#{DataSource},</if>
            <if test="PushStatus != null">#{PushStatus},</if>
            <if test="PushDate != null">#{PushDate},</if>
            <if test="PushBy != null">#{PushBy},</if>
            <if test="Remark != null">#{Remark},</if>
            <if test="IsDel != null">#{IsDel},</if>
            <if test="CreateBy != null">#{CreateBy},</if>
        </trim>
    </insert>

    <insert id="insertBatchDwsPrpInsureCont" parameterType="java.util.List">
        insert into t_dws_prp_insure_cont(TransactionNo, CompanyCode, ReInsuranceContNo, ReInsuranceContName, ReInsuranceContTitle, MainReInsuranceContNo, ContOrAmendmentType, ContAttribute, ContStatus, TreatyOrFacultativeFlag, ContSigndate, PeriodFrom, PeriodTo, ContType, ReinsurerCode, ReinsurerName, ChargeType, ReportYear, ReportMonth, AccountPeriod, DataSource, PushStatus, PushDate, PushBy, Remark, IsDel, CreateBy) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.TransactionNo}, #{item.CompanyCode}, #{item.ReInsuranceContNo}, #{item.ReInsuranceContName}, #{item.ReInsuranceContTitle}, #{item.MainReInsuranceContNo}, #{item.ContOrAmendmentType}, #{item.ContAttribute}, #{item.ContStatus}, #{item.TreatyOrFacultativeFlag}, #{item.ContSigndate}, #{item.PeriodFrom}, #{item.PeriodTo}, #{item.ContType}, #{item.ReinsurerCode}, #{item.ReinsurerName}, #{item.ChargeType}, #{item.ReportYear}, #{item.ReportMonth}, #{item.AccountPeriod}, #{item.DataSource}, #{item.PushStatus}, #{item.PushDate}, #{item.PushBy}, #{item.Remark}, #{item.IsDel}, #{item.CreateBy})
        </foreach>
    </insert>

    <update id="updateDwsPrpInsureCont" parameterType="DwsPrpInsureContEntity">
        update t_dws_prp_insure_cont
        <trim prefix="SET" suffixOverrides=",">
            <if test="TransactionNo != null">TransactionNo = #{TransactionNo},</if>
            <if test="CompanyCode != null">CompanyCode = #{CompanyCode},</if>
            <if test="ReInsuranceContNo != null">ReInsuranceContNo = #{ReInsuranceContNo},</if>
            <if test="ReInsuranceContName != null">ReInsuranceContName = #{ReInsuranceContName},</if>
            <if test="ReInsuranceContTitle != null">ReInsuranceContTitle = #{ReInsuranceContTitle},</if>
            <if test="MainReInsuranceContNo != null">MainReInsuranceContNo = #{MainReInsuranceContNo},</if>
            <if test="ContOrAmendmentType != null">ContOrAmendmentType = #{ContOrAmendmentType},</if>
            <if test="ContAttribute != null">ContAttribute = #{ContAttribute},</if>
            <if test="ContStatus != null">ContStatus = #{ContStatus},</if>
            <if test="TreatyOrFacultativeFlag != null">TreatyOrFacultativeFlag = #{TreatyOrFacultativeFlag},</if>
            <if test="ContSigndate != null">ContSigndate = #{ContSigndate},</if>
            <if test="PeriodFrom != null">PeriodFrom = #{PeriodFrom},</if>
            <if test="PeriodTo != null">PeriodTo = #{PeriodTo},</if>
            <if test="ContType != null">ContType = #{ContType},</if>
            <if test="ReinsurerCode != null">ReinsurerCode = #{ReinsurerCode},</if>
            <if test="ReinsurerName != null">ReinsurerName = #{ReinsurerName},</if>
            <if test="ChargeType != null">ChargeType = #{ChargeType},</if>
            <if test="ReportYear != null">ReportYear = #{ReportYear},</if>
            <if test="ReportMonth != null">ReportMonth = #{ReportMonth},</if>
            <if test="AccountPeriod != null">AccountPeriod = #{AccountPeriod},</if>
            <if test="DataSource != null">DataSource = #{DataSource},</if>
            <if test="PushStatus != null">PushStatus = #{PushStatus},</if>
            <if test="PushDate != null">PushDate = #{PushDate},</if>
            <if test="PushBy != null">PushBy = #{PushBy},</if>
            <if test="Remark != null">Remark = #{Remark},</if>
            <if test="IsDel != null">IsDel = #{IsDel},</if>
            <if test="UpdateBy != null">UpdateBy = #{UpdateBy},</if>
            <if test="UpdateTime != null">UpdateTime = #{UpdateTime},</if>
        </trim>
        where Id = #{Id}
    </update>

    <delete id="deleteDwsPrpInsureContById" parameterType="Long">
        delete from t_dws_prp_insure_cont where Id = #{Id}
    </delete>

    <delete id="deleteDwsPrpInsureContByIds" parameterType="String">
        delete from t_dws_prp_insure_cont where Id in 
        <foreach item="Id" collection="array" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </delete>

    <select id="selectDwsPrpInsureContExists" parameterType="DwsPrpInsureContQuery" resultType="int">
        select count(1) from t_dws_prp_insure_cont
        <where>
            <if test="TransactionNo != null  and TransactionNo != ''"> and TransactionNo = #{TransactionNo}</if>
            <if test="ReInsuranceContNo != null  and ReInsuranceContNo != ''"> and ReInsuranceContNo = #{ReInsuranceContNo}</if>
            <if test="ReportYear != null "> and ReportYear = #{ReportYear}</if>
            <if test="ReportMonth != null "> and ReportMonth = #{ReportMonth}</if>
            and IsDel = 0
        </where>
    </select>

    <update id="updateDwsPrpInsureContPushStatus">
        update t_dws_prp_insure_cont set PushStatus = #{pushStatus}, PushBy = #{pushBy}, PushDate = now()
        where Id in
        <foreach item="Id" collection="Ids" open="(" separator="," close=")">
            #{Id}
        </foreach>
    </update>

    <select id="selectAnnualReportShouldPushStatus" parameterType="int" resultType="Integer">
        select (case when notPushedCount>=0 and alreadyPushedCount=0 then 0 when notPushedCount>0 and alreadyPushedCount>0 then 1 else 2 end) as PushStatus
        from (select ifnull(sum(if(PushStatus = 0, total, 0)), 0) as notPushedCount, ifnull(sum(if(PushStatus = 1, total, 0)),0) as alreadyPushedCount
        from (select PushStatus, count(*) as total from t_dws_prp_insure_cont where IsDel = 0 and ReportYear = #{ReportYear} group by PushStatus)v )t
    </select>

</mapper>
