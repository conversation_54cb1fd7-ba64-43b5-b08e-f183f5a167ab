package com.reinsurance.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.StarRocksConnector;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.spring.SpringUtils;
import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.dto.excel.DwsPrpEdorImportXlsDTO;
import com.reinsurance.enums.BasicDataEnums.RedisKeyModule;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.service.IDwsPrpAccountService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * 保单登记保全变更信息明细导入处理类
 * <AUTHOR>
 *
 */
@Slf4j
public class DwsPrpEdorReadListener implements ReadListener<DwsPrpEdorImportXlsDTO> {

	private String executor;

	private Date importDate;

	private long insertRows = 0;

	private DwsPrpAccountEntity account;

	private IRedisService redisService;

	private StarRocksConnector starRocksConnector;

	private IDwsPrpAccountService dwsPrpAccountService;

	private static final int BATCH_SAVE_COUNT = 10000;

    private List<DwsPrpEdorImportXlsDTO> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_SAVE_COUNT);

    public DwsPrpEdorReadListener(DwsPrpAccountEntity account, IRedisService redisService, StarRocksConnector starRocksConnector) {
        this.account = account;
        this.importDate = new Date();
        this.redisService = redisService;
        this.starRocksConnector = starRocksConnector;
        this.dwsPrpAccountService = SpringUtils.getBean(IDwsPrpAccountService.class);
    }
    
	@Override
	public void invoke(DwsPrpEdorImportXlsDTO edor, AnalysisContext context) {
		edor.setCreateBy(executor);
		edor.setUpdateBy(executor);
		edor.setCreateTime(importDate);
		edor.setUpdateTime(importDate);
		edor.setAccTransNo(account.getTransactionNo());
		cachedDataList.add(edor);
		if (cachedDataList.size() >= BATCH_SAVE_COUNT) {
			List<String> transNos = redisService.getUniqueCodes(RedisKeyModule.PRP, account.getReinsurerCode(), cachedDataList.size());
			if(CollUtil.isEmpty(transNos) || cachedDataList.size() != transNos.size()) {
				log.info("再保导入保单登记保全变更信息明细生成流水号失败, account:{}", ReinsuJsonUtil.toJsonString(account));
			}else {
				int index = 0;
				for(DwsPrpEdorImportXlsDTO item : cachedDataList) {
					item.setTransactionNo(transNos.get(index));
					index++;
				}
				try {
					StarRocksOperationResult starRocksResult = starRocksConnector.insert("t_dws_prp_edor", ReinsuJsonUtil.toJsonString(cachedDataList));
					String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
					if(!("success").equalsIgnoreCase(status)) {//操作失败
						log.info("再保导入保单登记保全变更信息明细失败, account:{}, starRocksResult:{}", ReinsuJsonUtil.toJsonString(account), ReinsuJsonUtil.toJsonString(starRocksResult));
					}else {
						insertRows += starRocksResult.getNumberTotalRows();
						log.info("再保导入保单登记保全变更信息明细处理中, insertRows:{}", insertRows);
					}
				}catch(Exception e) {
					log.error("再保导入保单登记保全变更信息明细失败, account:{}, 失败原因: ", ReinsuJsonUtil.toJsonString(account), e);
				}
			}
			cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_SAVE_COUNT);
		}
	}

	@Override
	public void doAfterAllAnalysed(AnalysisContext context) {
		if(cachedDataList.size() <= 0) {
			log.info("再保导入保单登记保全变更信息明细结束, 未解析到数据");
			return;
		}
		try {
			int totalRows = context.readRowHolder().getRowIndex();
			List<String> transNos = redisService.getUniqueCodes(RedisKeyModule.PRP, account.getReinsurerCode(), cachedDataList.size());
			if(CollUtil.isEmpty(transNos) || cachedDataList.size() != transNos.size()) {
				log.info("再保导入保单登记保全变更信息明细生成交易编码失败, account:{}", ReinsuJsonUtil.toJsonString(account));
				return;
			}
			int index = 0;
			for(DwsPrpEdorImportXlsDTO item : cachedDataList) {
				item.setTransactionNo(transNos.get(index));
				index++;
			}
			StarRocksOperationResult starRocksResult = starRocksConnector.insert("t_dws_prp_edor", ReinsuJsonUtil.toJsonString(cachedDataList));
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.info("再保导入保单登记保全变更信息明细失败, account:{}, starRocksResult:{}", ReinsuJsonUtil.toJsonString(account), ReinsuJsonUtil.toJsonString(starRocksResult));
				return;
			}
			insertRows += starRocksResult.getNumberTotalRows();

			account.setUpdateBy(executor);
			account.setUpdateTime(DateUtils.getNowDate());
			account.setContImportStatus(CedeoutEnums.导入状态_已导入.getValue());
			account.setContImportRemark("totalRows:" + totalRows + ";insertRows:" + insertRows);
			DwsPrpAccountDTO dwsPrpAccountDTO = ReinsuObjectUtil.convertModel(account, DwsPrpAccountDTO.class);
			int updateAccountRows = dwsPrpAccountService.updateAccountImportStatusByTransactionNo(dwsPrpAccountDTO);
			log.info("再保导入保单登记保全变更信息明细完成, totalRows:{}, insertRows:{}, updateAccountRows:{}", totalRows, insertRows, updateAccountRows);
		}catch(Exception e) {
			log.error("再保导入保单登记保全变更信息明细失败, account:{}, 失败原因: ", ReinsuJsonUtil.toJsonString(account), e);
		}
	}

}
