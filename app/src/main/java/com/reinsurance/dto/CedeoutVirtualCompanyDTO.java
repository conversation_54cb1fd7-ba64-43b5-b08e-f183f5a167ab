package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;

/**
 * 虚拟合同再保公司关系对象 t_cedeout_virtual_company
 * 
 * <AUTHOR>
 * @date 2024-04-10
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutVirtualCompanyDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 虚拟合同Id */
    @Excel(name = "虚拟合同Id")
    private Long virtualId;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    private Long companyId;

    /** 再保公司编码 */
    @Excel(name = "再保公司编码")
    private String companyCode;

    /** 再保公司名称 */
    @Excel(name = "再保公司名称")
    private String companyName;
    /** 分保比例 */
    @Excel(name = "分保比例")
    private BigDecimal cedeoutScale;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0==有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
}
