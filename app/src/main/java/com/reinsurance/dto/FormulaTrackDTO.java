package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 计算公式轨迹对象 t_formula_track
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class FormulaTrackDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 公式编码 */
    @Excel(name = "公式编码")
    private String formulaCode;

    /** 公式名称 */
    @Excel(name = "公式名称")
    private String formulaName;

    /** 算法类型 */
    @Excel(name = "算法类型")
    private Integer formulaType;

    /** 分出方式（0=溢额,1=成数,2=混合） */
    @Excel(name = "分出方式", readConverterExp = "0=溢额,1=成数,2=混合")
    private Integer cedeoutWay;

    /** 缴费方式（12=年交） */
    @Excel(name = "缴费方式", readConverterExp = "12=年交")
    private String payIntv;

    /** 业务类型（0=新单,1=续期,2=保全,3=理赔,4=满期） */
    @Excel(name = "业务类型", readConverterExp = "0=新单,1=续期,2=保全,3=理赔,4=满期")
    private Integer busiType;

    /** 计算公式 */
    @Excel(name = "计算公式")
    private String formulaValue;

    /** 公式文本 */
    @Excel(name = "公式文本")
    private String formulaText;

    /** 公式说明 */
    private String formulaDesc;

    /** 公式富文本 */
    private String formulaRichText;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
