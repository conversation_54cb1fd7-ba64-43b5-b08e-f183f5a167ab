package com.reinsurance.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.excel.annotation.ExcelProperty;
import com.reinsurance.dto.PrpBaseDTO;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.enums.CedeoutEnums;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.math.BigDecimal;
import java.util.Date;

/**
 * 再保保全险种明细对象
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpEdorImportXlsDTO extends PrpBaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @ExcelProperty("保险机构代码")
    private String CompanyCode;

    /** 团体保单号 */
    @JsonProperty("GrpPolicyNo")
    @ExcelProperty("团体保单号")
    private String GrpPolicyNo;

    /** 团单保险险种号码 */
    @JsonProperty("GrpProductNo")
    @ExcelProperty("团单保险险种号码")
    private String GrpProductNo;

    /** 个人保单号 */
    @JsonProperty("PolicyNo")
    @ExcelProperty("个人保单号")
    private String PolicyNo;

    /** 个单保险险种号码 */
    @JsonProperty("ProductNo")
    @ExcelProperty("个单保险险种号码")
    private String ProductNo;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    @ExcelProperty("团个性质")
    private String GPFlag;

    /** 主险保险险种号码 */
    @JsonProperty("MainProductNo")
    @ExcelProperty("主险保险险种号码")
    private String MainProductNo;

    /** 主附险性质代码（1=主险,2=附加险,3=不区分） */
    @JsonProperty("MainProductFlag")
    @ExcelProperty("主附险性质代码")
    private String MainProductFlag;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    @ExcelProperty("产品编码")
    private String ProductCode;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    @ExcelProperty("责任代码")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    @ExcelProperty("责任名称")
    private String LiabilityName;

    /** 责任分类代码 */
    @JsonProperty("Classification")
    @ExcelProperty("责任分类代码")
    private String Classification;

    /** 保险期限类型 */
    @JsonProperty("TermType")
    @ExcelProperty("保险期限类型")
    private String TermType;

    /** 保单年度 */
    @JsonProperty("PolYear")
    @ExcelProperty("保单年度")
    private Integer PolYear;

    /** 管理机构代码 */
    @JsonProperty("ManageCom")
    @ExcelProperty("管理机构代码")
    private String ManageCom;

    /** 签单日期 */
    @JsonProperty("SignDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("签单日期")
    private Date SignDate;

    /** 保险责任生效日期 */
    @JsonProperty("EffDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("保险责任生效日期")
    private Date EffDate;

    /** 保险责任终止日期 */
    @JsonProperty("InvalidDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("保险责任终止日期")
    private Date InvalidDate;

    /** 核保结论代码 */
    @JsonProperty("UWConclusion")
    @ExcelProperty("核保结论代码")
    private String UWConclusion;

    /** 保单状态代码 */
    @JsonProperty("PolStatus")
    @ExcelProperty("保单状态代码")
    private String PolStatus;

    /** 保单险种状态代码 */
    @JsonProperty("Status")
    @ExcelProperty("保单险种状态代码")
    private String Status;

    /** 基本保额 */
    @JsonProperty("BasicSumInsured")
    @ExcelProperty("基本保额")
    private BigDecimal BasicSumInsured;

    /** 风险保额 */
    @JsonProperty("RiskAmnt")
    @ExcelProperty("风险保额")
    private BigDecimal RiskAmnt;

    /** 保费 */
    @JsonProperty("Premium")
    @ExcelProperty("保费")
    private BigDecimal Premium;

    /** 保险账户价值 */
    @JsonProperty("AccountValue")
    @ExcelProperty("保险账户价值")
    private BigDecimal AccountValue;

    /** 临分标记（0=否,1=是） */
    @JsonProperty("FacultativeFlag")
    @ExcelProperty("临分标记")
    private String FacultativeFlag;

    /** 无名单标志 */
    @JsonProperty("AnonymousFlag")
    @ExcelProperty("无名单标志")
    private String AnonymousFlag;

    /** 豁免险标志（0=否,1=是） */
    @JsonProperty("WaiverFlag")
    @ExcelProperty("豁免险标志")
    private String WaiverFlag;

    /** 所需豁免剩余保费 */
    @JsonProperty("WaiverPrem")
    @ExcelProperty("所需豁免剩余保费")
    private BigDecimal WaiverPrem;

    /** 期末现金价值 */
    @JsonProperty("FinalCashValue")
    @ExcelProperty("期末现金价值")
    private BigDecimal FinalCashValue;

    /** 期末责任准备金 */
    @JsonProperty("FinalLiabilityReserve")
    @ExcelProperty("期末责任准备金")
    private BigDecimal FinalLiabilityReserve;

    /** 被保人数 */
    @JsonProperty("InsurePeoples")
    @ExcelProperty("被保人数")
    private Integer InsurePeoples;

    /** 被保人客户号 */
    @JsonProperty("InsuredNo")
    @ExcelProperty("被保人客户号")
    private String InsuredNo;

    /** 被保人姓名 */
    @JsonProperty("InsuredName")
    @ExcelProperty("被保人姓名")
    private String InsuredName;

    /** 被保人性别 */
    @JsonProperty("InsuredSex")
    @ExcelProperty("被保人性别")
    private String InsuredSex;

    /** 被保人证件类型 */
    @JsonProperty("InsuredCertType")
    @ExcelProperty("被保人证件类型")
    private String InsuredCertType;

    /** 被保人证件编码 */
    @JsonProperty("InsuredCertNo")
    @ExcelProperty("被保人证件编码")
    private String InsuredCertNo;

    /** 职业代码 */
    @JsonProperty("OccupationType")
    @ExcelProperty("职业代码")
    private String OccupationType;

    /** 投保年龄 */
    @JsonProperty("AppntAge")
    @ExcelProperty("投保年龄")
    private Integer AppntAge;

    /** 当前年龄 */
    @JsonProperty("PreAge")
    @ExcelProperty("当前年龄")
    private Integer PreAge;

    /** 职业加费金额 */
    @JsonProperty("ProfessionalFee")
    @ExcelProperty("职业加费金额")
    private BigDecimal ProfessionalFee;

    /** 次标准体加费金额 */
    @JsonProperty("SubStandardFee")
    @ExcelProperty("次标准体加费金额")
    private BigDecimal SubStandardFee;

    /** EM加点 */
    @JsonProperty("EMRate")
    @ExcelProperty("EM加点")
    private BigDecimal EMRate;

    /** 建工险标志 */
    @JsonProperty("ProjectFlag")
    @ExcelProperty("建工险标志")
    private String ProjectFlag;

    /** 保全受理号码 */
    @JsonProperty("EndorAcceptNo")
    @ExcelProperty("保全受理号码")
    private String EndorAcceptNo;

    /** 保全批单号码 */
    @JsonProperty("EndorsementNo")
    @ExcelProperty("保全批单号码")
    private String EndorsementNo;

    /** 保全项目类型 */
    @JsonProperty("EdorType")
    @ExcelProperty("保全项目类型")
    private String EdorType;

    /** 保全生效日期 */
    @JsonProperty("EdorValiDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("保全生效日期")
    private Date EdorValiDate;

    /** 保全确认日期 */
    @JsonProperty("EdorConfDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("保全确认日期")
    private Date EdorConfDate;

    /** 保全发生费用 */
    @JsonProperty("EdorMoney")
    @ExcelProperty("保全发生费用")
    private BigDecimal EdorMoney;

    /** 变更前被保人投保年龄 */
    @JsonProperty("PreInsuredAge")
    @ExcelProperty("变更前被保人投保年龄")
    private Integer PreInsuredAge;

    /** 变更前基本保额 */
    @JsonProperty("PreBasicSumInsured")
    @ExcelProperty("变更前基本保额")
    private BigDecimal PreBasicSumInsured;

    /** 变更前风险保额 */
    @JsonProperty("PreRiskAmnt")
    @ExcelProperty("变更前风险保额")
    private BigDecimal PreRiskAmnt;

    /** 变更前分保保额 */
    @JsonProperty("PreReinsuranceAmnt")
    @ExcelProperty("变更前分保保额")
    private BigDecimal PreReinsuranceAmnt;

    /** 变更前自留额 */
    @JsonProperty("PreRetentionAmount")
    @ExcelProperty("变更前自留额")
    private BigDecimal PreRetentionAmount;

    /** 变更前保费 */
    @JsonProperty("PrePremium")
    @ExcelProperty("变更前保费")
    private BigDecimal PrePremium;

    /** 变更前账户价值 */
    @JsonProperty("PreAccountValue")
    @ExcelProperty("变更前账户价值")
    private BigDecimal PreAccountValue;

    /** 变更前所需豁免剩余保费 */
    @JsonProperty("PreWaiverPrem")
    @ExcelProperty("变更前所需豁免剩余保费")
    private BigDecimal PreWaiverPrem;

    /** 建筑面积变化量 */
    @JsonProperty("ProjectAcreageChange")
    @ExcelProperty("建筑面积变化量")
    private BigDecimal ProjectAcreageChange;

    /** 工程造价变化量 */
    @JsonProperty("ProjectCostChange")
    @ExcelProperty("工程造价变化量")
    private BigDecimal ProjectCostChange;

    /** 分出标记（0=未达到溢额线保单,1=分出保单） */
    @JsonProperty("SaparateFlag")
    @ExcelProperty("分出标记")
    private String SaparateFlag;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @ExcelProperty("再保险合同号码")
    private String ReInsuranceContNo;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @ExcelProperty("再保险公司代码")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @ExcelProperty("再保险公司名称")
    private String ReinsurerName;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    @ExcelProperty("分保方式")
    private String ReinsurMode;

    /** 分保比例 */
    @JsonProperty("QuotaSharePercentage")
    @ExcelProperty("分保比例")
    private String QuotaSharePercentage;

    /** 分保保额变化量 */
    @JsonProperty("ReinsuranceAmntChange")
    @ExcelProperty("分保保额变化量")
    private BigDecimal ReinsuranceAmntChange;

    /** 自留额 */
    @JsonProperty("RetentionAmount")
    @ExcelProperty("自留额")
    private BigDecimal RetentionAmount;

    /** 变更分保费 */
    @JsonProperty("ReinsurancePremiumChange")
    @ExcelProperty("变更分保费")
    private BigDecimal ReinsurancePremiumChange;

    /** 变更分保佣金 */
    @JsonProperty("ReinsuranceCommssionChange")
    @ExcelProperty("变更分保佣金")
    private BigDecimal ReinsuranceCommssionChange;

    /** 货币代码 */
    @JsonProperty("Currency")
    @ExcelProperty("货币代码")
    private String Currency;

    /** 分保计算日期 */
    @JsonProperty("ReComputationsDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("分保计算日期")
    private Date ReComputationsDate;

    /** 账单归属日期 */
    @JsonProperty("AccountGetDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("账单归属日期")
    private Date AccountGetDate;

    /** 所属账单流水号 */
    @JsonProperty("AccTransNo")
    @ExcelProperty("所属账单流水号")
    private String AccTransNo;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource = BasicDataEnums.ReportDataSource.人工.getCode();

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus = BasicDataEnums.ReportPushStatus.未推送.getCode();

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel = CedeoutEnums.未删除.getValue();
}
