package com.reinsurance.dto.excel;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.alibaba.excel.annotation.ExcelProperty;
import com.reinsurance.core.convert.PrpEventTypeConvert;
import com.reinsurance.core.convert.PrpGPFlagConvert;
import com.reinsurance.core.convert.PrpMainProductFlagConvert;
import com.reinsurance.dto.PrpBaseDTO;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.enums.CedeoutEnums;
import lombok.Data;
import lombok.EqualsAndHashCode;


import java.math.BigDecimal;
import java.util.Date;

/**
 * 再保生存金险种明细对象
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpBenefitImportXlsDTO extends PrpBaseDTO {
    
    private static final long serialVersionUID = 1L;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    @ExcelProperty("保险机构代码")
    private String CompanyCode;

    /** 团体保单号 */
    @JsonProperty("GrpPolicyNo")
    @ExcelProperty("团体保单号")
    private String GrpPolicyNo;

    /** 个人保单号 */
    @JsonProperty("PolicyNo")
    @ExcelProperty("个人保单号")
    private String PolicyNo;

    /** 个单保险险种号码 */
    @JsonProperty("ProductNo")
    @ExcelProperty("个单保险险种号码")
    private String ProductNo;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    @ExcelProperty(value = "团个性质", converter = PrpGPFlagConvert.class)
    private String GPFlag;

    /** 保单年度 */
    @JsonProperty("PolYear")
    @ExcelProperty("保单年度")
    private Integer PolYear;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    @ExcelProperty("产品编码")
    private String ProductCode;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    @ExcelProperty("责任代码")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    @ExcelProperty("责任名称")
    private String LiabilityName;

    /** 给付责任代码 */
    @JsonProperty("GetLiabilityCode")
    @ExcelProperty("给付责任代码")
    private String GetLiabilityCode;

    /** 给付责任名称 */
    @JsonProperty("GetLiabilityName")
    @ExcelProperty("给付责任名称")
    private String GetLiabilityName;

    /** 保险期限类型 */
    @JsonProperty("TermType")
    @ExcelProperty("保险期限类型")
    private String TermType;

    /** 领取序号 */
    @JsonProperty("WDNo")
    @ExcelProperty("领取序号")
    private String WDNo;

    /** 被保人客户号 */
    @JsonProperty("InsuredNo")
    @ExcelProperty("被保人客户号")
    private String InsuredNo;

    /** 被保人姓名 */
    @JsonProperty("InsuredName")
    @ExcelProperty("被保人姓名")
    private String InsuredName;

    /** 被保人性别 */
    @JsonProperty("InsuredSex")
    @ExcelProperty("被保人性别")
    private String InsuredSex;

    /** 被保人证件类型 */
    @JsonProperty("InsuredCertType")
    @ExcelProperty("被保人证件类型")
    private String InsuredCertType;

    /** 被保人证件编码 */
    @JsonProperty("InsuredCertNo")
    @ExcelProperty("被保人证件编码")
    private String InsuredCertNo;

    /** 职业代码 */
    @JsonProperty("OccupationType")
    @ExcelProperty("职业代码")
    private String OccupationType;

    /** 投保年龄 */
    @JsonProperty("AppntAge")
    @ExcelProperty("投保年龄")
    private Integer AppntAge;

    /** 当前年龄 */
    @JsonProperty("PreAge")
    @ExcelProperty("当前年龄")
    private Integer PreAge;

    /** 给付日期 */
    @JsonProperty("BenefitDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("给付日期")
    private Date BenefitDate;

    /** 分出标记（0=未达到溢额线保单,1=分出保单） */
    @JsonProperty("SaparateFlag")
    @ExcelProperty("分出标记")
    private String SaparateFlag;

    /** 生存金类型 */
    @JsonProperty("BenefitClass")
    @ExcelProperty("生存金类型")
    private String BenefitClass;

    /** 生存金领取金额 */
    @JsonProperty("BenefitAmount")
    @ExcelProperty("生存金领取金额")
    private BigDecimal BenefitAmount;

    /** 到账日期 */
    @JsonProperty("EnterAccDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("到账日期")
    private Date EnterAccDate;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @ExcelProperty("再保险合同号码")
    private String ReInsuranceContNo;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @ExcelProperty("再保险公司代码")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @ExcelProperty("再保险公司名称")
    private String ReinsurerName;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    @ExcelProperty("分保方式")
    private String ReinsurMode;

    /** 摊回金额 */
    @JsonProperty("BackBenefitAmount")
    @ExcelProperty("摊回金额")
    private BigDecimal BackBenefitAmount;

    /** 摊回日期 */
    @JsonProperty("BackDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("摊回日期")
    private Date BackDate;

    /** 货币代码 */
    @JsonProperty("Currency")
    @ExcelProperty("货币代码")
    private String Currency;

    /** 分保计算日期 */
    @JsonProperty("ReComputationsDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("分保计算日期")
    private Date ReComputationsDate;

    /** 账单归属日期 */
    @JsonProperty("AccountGetDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ExcelProperty("账单归属日期")
    private Date AccountGetDate;

    /** 所属账单流水号 */
    @JsonProperty("AccTransNo")
    private String AccTransNo;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource = BasicDataEnums.ReportDataSource.人工.getCode();

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus = BasicDataEnums.ReportPushStatus.未推送.getCode();

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel = CedeoutEnums.未删除.getValue();
}
