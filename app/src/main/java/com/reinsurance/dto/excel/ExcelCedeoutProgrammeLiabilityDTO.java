package com.reinsurance.dto.excel;

import cn.hutool.core.date.DatePattern;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 再保方案维护责任信息
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class ExcelCedeoutProgrammeLiabilityDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;

    @Excel(name = "版本号")
    private Long version;

    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    @Excel(name = "方案ID")
    private Long programmeId;

    @Excel(name = "方案编码")
    private String programmeCode;

    @Excel(name = "险种编码")
    private String riskCode;

    @Excel(name = "险种名称")
    private String riskName;

    @Excel(name = "责任编码")
    private String liabilityCode;

    @Excel(name = "责任名称")
    private String liabilityName;

    @Excel(name = "匹配开始日期", width = 40, dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date startDate;

    @Excel(name = "匹配结束日期", width = 40, dateFormat = DatePattern.NORM_DATETIME_PATTERN)
    private Date endDate;

    @Excel(name = "日期匹配保单属性")
    private String mateDateColumn;

    @Excel(name = "再保合同编码(逗号分隔)")
    private String contractCode;

    @Excel(name = "状态", dictType = "re_programme_status")
    private Integer status;

}
