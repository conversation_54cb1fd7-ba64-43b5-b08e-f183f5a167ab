package com.reinsurance.dto.excel;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 公式配置
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class ExcelFormulaDTO extends BaseDTO {

    private static final long serialVersionUID = 1L;

    @Excel(name = "版本号")
    private Long version;

    @Excel(name = "算法大类", readConverterExp = "1=风险保额计算, 2=理赔摊回计算, 3=其他")
    private Integer formulaBigType;

    @Excel(name = "算法类型", dictType = "formula_type")
    private String formulaType;

    @Excel(name = "分保方式", dictType = "re_cedeout_way")
    private Integer cedeoutWay;

    @Excel(name = "分出模式", dictType = "re_cedeout_mode")
    private Integer cedeoutMode;

    @Excel(name = "交费方式", dictType = "re_pay_intv")
    private String payIntv;

    @Excel(name = "再保项目", dictType = "formula_reinsurance_project")
    private Integer reinsuranceProject;

    @Excel(name = "业务类型", dictType = "risk_liability_business_type")
    private Integer busiType;

    @Excel(name = "公式名称")
    private String formulaName;

    @Excel(name = "公式编码")
    private String formulaCode;

    @Excel(name = "公式")
    private String formulaValue;

    @Excel(name = "公式描述")
    private String formulaDesc;

    @Excel(name = "公式")
    private String formulaText;

    @Excel(name = "公式富文本")
    private String formulaRichText;

    @Excel(name = "状态", dictType = "formula_status")
    private Integer status;

}
