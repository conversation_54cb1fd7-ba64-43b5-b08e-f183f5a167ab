package com.reinsurance.dto;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.reinsurance.core.config.customDictHandler.FormulaTypeConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 计算公式对象 t_formula
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@ApiModel("公式配置--实体")
@Data
@EqualsAndHashCode(callSuper=true)
public class FormulaDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("版本号")
    private Long version;

    @ApiModelProperty("算法大类，1=风险保额计算, 2=理赔摊回计算, 3=其他")
    @Excel(name = "算法大类", readConverterExp = "1=风险保额计算, 2=理赔摊回计算, 3=其他")
    private Integer formulaBigType;

    @ApiModelProperty("算法类型，字典：formula_type")
    @Excel(name = "算法类型", handler = FormulaTypeConvert.class)
    private String formulaType;

    @ApiModelProperty("分出模式，字典：re_cedeout_mode")
    @Excel(name = "分出模式", dictType = "re_cedeout_mode")
    private Integer cedeoutMode;

    @ApiModelProperty("交费方式，字典：cont_pay_intv")
    @Excel(name = "交费方式", dictType = "cont_pay_intv")
    private String payIntv;

    @ApiModelProperty("再保项目，字典：formula_reinsurance_project")
    @Excel(name = "再保项目", dictType = "formula_reinsurance_project")
    private Integer reinsuranceProject;

    @ApiModelProperty("分保方式，字典：re_cedeout_way")
    @Excel(name = "分保方式", dictType = "re_cedeout_way")
    private Integer cedeoutWay;

    @ApiModelProperty("业务类型，字典：risk_liability_business_type")
    @Excel(name = "业务类型", dictType = "risk_liability_business_type")
    private Integer busiType;

    @ApiModelProperty("公式名称")
    @Excel(name = "公式名称")
    private String formulaName;

    @ApiModelProperty("公式编码")
    @Excel(name = "公式编码")
    private String formulaCode;

    @ApiModelProperty("公式")
    private String formulaValue;

    @ApiModelProperty("公式描述")
    @Excel(name = "公式描述")
    private String formulaDesc;

    @ApiModelProperty("公式中文")
    @Excel(name = "公式")
    private String formulaText;

    @ApiModelProperty("公式富文本")
    private String formulaRichText;

    @ApiModelProperty("状态，字典：formula_status")
    @Excel(name = "状态", dictType = "formula_status")
    private Integer status;

    /** 是否删除 */
    private Integer isDel;

}
