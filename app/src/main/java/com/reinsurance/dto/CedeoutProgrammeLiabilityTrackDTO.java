package com.reinsurance.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.BaseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 再保分出方案责任轨迹对象 t_cedeout_programme_liability_track
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutProgrammeLiabilityTrackDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    /** 轨迹Id */
    private Long trackId;

    /** 业务表Id */
    private Long id;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNo;

    /** 版本号 */
    @Excel(name = "版本号")
    private Long version;

    /** 虚拟合同编码 */
    @Excel(name = "虚拟合同编码")
    private String virtualCode;

    /** 方案编码 */
    @Excel(name = "方案编码")
    private String programmeCode;

    /** 险种编码 */
    @Excel(name = "险种编码")
    private String riskCode;

    /** 险种名称 */
    @Excel(name = "险种名称")
    private String riskName;

    /** 责任编码 */
    @Excel(name = "责任编码")
    private String liabilityCode;

    /** 责任名称 */
    @Excel(name = "责任名称")
    private String liabilityName;

    /** 匹配开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 匹配结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "匹配结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 日期匹配保单属性 */
    @Excel(name = "日期匹配保单属性")
    private String mateDateColumn;

    /** 再保合同编码;多个以逗号分隔 */
    @Excel(name = "再保合同编码;多个以逗号分隔")
    private String contractCode;

    /** 状态（0=有效,1=无效） */
    @Excel(name = "状态", readConverterExp = "0=有效,1=无效")
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    @Excel(name = "是否删除", readConverterExp = "0=未删除,1=已删除")
    private Integer isDel;

}
