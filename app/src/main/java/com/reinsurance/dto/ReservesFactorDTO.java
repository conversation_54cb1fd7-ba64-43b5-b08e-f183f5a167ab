package com.reinsurance.dto;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.jd.lightning.common.core.domain.BaseDTO;
import com.reinsurance.constant.converter.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 准备金因子对象 t_reserves_factor
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@ApiModel("准备金因子--实体")
@Data
@EqualsAndHashCode(callSuper=true)
@ExcelIgnoreUnannotated
public class ReservesFactorDTO extends BaseDTO
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("批次号")
    private String batchNo;

    @ApiModelProperty("版本号")
    private Long version;

    @ApiModelProperty("优选体标识，字典：reserves_factor_inusred_mrtclst")
    //@ExcelProperty(value = "优选体标识", converter = InusredMrtclstConverter.class)
    private Integer inusredMrtclst;

    @ApiModelProperty("交费频率，字典：cont_pay_intv")
    //@ExcelProperty(value = "交费频率", converter = PayIntvConverter.class)
    private Integer payIntv;

    @ApiModelProperty("险种编码，字典：core_insurance_type")
    @ExcelProperty("险种编码")
    private String riskCode;

    @ApiModelProperty("因子类型，字典：reserves_factor_type")
    @ExcelProperty(value = "因子类型", converter = FactorTypeConverter.class)
    private String factorType;

    @ApiModelProperty("风险类型")
    @ExcelProperty(value = "风险类型")
    private String riskType;

    @ApiModelProperty("保障计划")
    @ExcelProperty(value = "保障计划")
    private String planCode;

    @ApiModelProperty("性别，字典：sys_user_sex")
    @ExcelProperty(value = "性别", converter = SexConverter.class)
    private Integer insuredSex;

    @ApiModelProperty("投保年龄")
    @ExcelProperty("投保年龄")
    private Integer insuredAge;

    @ApiModelProperty("保险期间")
    @ExcelProperty("保险期间")
    private Integer insuYear;

    @ApiModelProperty("保险期间单位，字典：insu_payend_year_flag")
    @ExcelProperty(value = "保险期间单位", converter = PayUnitConverter.class)
    private String insuYearFlag;

    @ApiModelProperty("交费期间")
    @ExcelProperty("交费期间")
    private Integer payendYear;

    @ApiModelProperty("交费期间单位，字典：insu_payend_year_flag")
    @ExcelProperty(value = "交费期间单位", converter = PayUnitConverter.class)
    private String payendYearFlag;

    @ApiModelProperty("保单年度")
    @ExcelProperty("保单年度")
    private Integer policyYear;

    @ApiModelProperty("年缴保费(元)")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelProperty("年缴保费(元)")
    private BigDecimal premium;

    @ApiModelProperty("保险金额(元)")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelProperty("保险金额(元)")
    private BigDecimal amount;

    @ApiModelProperty("期初准备金(元)")
    @JsonSerialize(using = ToStringSerializer.class)
    @ExcelProperty("期初准备金(元)")
    private BigDecimal initReserve;

    @ApiModelProperty("状态，字典：reserves_factor_status")
    @ExcelProperty(value = "状态", converter = StatusConverter.class)
    private Integer status;

    /** 是否删除 */
    private Integer isDel;

}
