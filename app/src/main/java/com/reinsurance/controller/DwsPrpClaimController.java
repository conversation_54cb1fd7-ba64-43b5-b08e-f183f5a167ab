package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.query.DwsPrpClaimQuery;
import com.reinsurance.service.IDwsPrpClaimService;
import com.reinsurance.utils.ReinsuObjectUtil;

/**
 * 再保理赔险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/dws/prp/claim")
public class DwsPrpClaimController extends BaseController {
    
    @Autowired
    private IDwsPrpClaimService dwsPrpClaimService;

    /**
     * 查询再保理赔险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpClaimQuery dwsPrpClaimQuery) {
        startPage();
        List<DwsPrpClaimEntity> list = dwsPrpClaimService.selectDwsPrpClaimList(dwsPrpClaimQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保理赔险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:export')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DwsPrpClaimQuery dwsPrpClaimQuery) {
        List<DwsPrpClaimEntity> list = dwsPrpClaimService.selectDwsPrpClaimList(dwsPrpClaimQuery);
        List<DwsPrpClaimDTO> dtoList = ReinsuObjectUtil.convertList(list, DwsPrpClaimDTO.class);
        ExcelUtil<DwsPrpClaimDTO> util = new ExcelUtil<DwsPrpClaimDTO>(DwsPrpClaimDTO.class);
        util.exportExcel(response, dtoList, "再保理赔险种明细数据");
    }

    /**
     * 获取再保理赔险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        DwsPrpClaimEntity entity = dwsPrpClaimService.selectDwsPrpClaimById(Id);
        DwsPrpClaimDTO dto = ReinsuObjectUtil.convertModel(entity, DwsPrpClaimDTO.class);
        return Result.success(dto);
    }

    /**
     * 新增再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:add')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpClaimDTO dwsPrpClaimDTO) {
        return toAjax(dwsPrpClaimService.insertDwsPrpClaim(dwsPrpClaimDTO));
    }

    /**
     * 修改再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:edit')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpClaimDTO dwsPrpClaimDTO) {
        return toAjax(dwsPrpClaimService.updateDwsPrpClaim(dwsPrpClaimDTO));
    }

    /**
     * 删除再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:remove')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpClaimService.deleteDwsPrpClaimByIds(Ids));
    }

    /**
     * 推送再保理赔险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:push')")
    @Log(title = "再保理赔险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody List<String> accTransNos) {
        String pushBy = getUsername();
        Integer pushStatus = 1; // 已推送
        return toAjax(dwsPrpClaimService.updateDwsPrpClaimPushStatus(pushStatus, pushBy, accTransNos));
    }

    /**
     * 检查再保理赔险种明细是否存在
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:claim:query')")
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpClaimQuery dwsPrpClaimQuery) {
        List<DwsPrpClaimEntity> list = dwsPrpClaimService.selectDwsPrpClaimList(dwsPrpClaimQuery);
        return Result.success(list.size() > 0);
    }
}
