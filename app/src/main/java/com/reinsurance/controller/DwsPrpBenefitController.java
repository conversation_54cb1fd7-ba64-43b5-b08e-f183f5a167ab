package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.domain.DwsPrpBenefitEntity;
import com.reinsurance.dto.DwsPrpBenefitDTO;
import com.reinsurance.query.DwsPrpBenefitQuery;
import com.reinsurance.service.IDwsPrpBenefitService;
import com.reinsurance.utils.ReinsuObjectUtil;

/**
 * 再保生存金险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/dws/prp/benefit")
public class DwsPrpBenefitController extends BaseController {
    
    @Autowired
    private IDwsPrpBenefitService dwsPrpBenefitService;

    /**
     * 查询再保生存金险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        startPage();
        List<DwsPrpBenefitEntity> list = dwsPrpBenefitService.selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保生存金险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:export')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        List<DwsPrpBenefitEntity> list = dwsPrpBenefitService.selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        List<DwsPrpBenefitDTO> dtoList = ReinsuObjectUtil.convertList(list, DwsPrpBenefitDTO.class);
        ExcelUtil<DwsPrpBenefitDTO> util = new ExcelUtil<DwsPrpBenefitDTO>(DwsPrpBenefitDTO.class);
        util.exportExcel(response, dtoList, "再保生存金险种明细数据");
    }

    /**
     * 获取再保生存金险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        DwsPrpBenefitEntity entity = dwsPrpBenefitService.selectDwsPrpBenefitById(Id);
        DwsPrpBenefitDTO dto = ReinsuObjectUtil.convertModel(entity, DwsPrpBenefitDTO.class);
        return Result.success(dto);
    }

    /**
     * 新增再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:add')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        return toAjax(dwsPrpBenefitService.insertDwsPrpBenefit(dwsPrpBenefitDTO));
    }

    /**
     * 修改再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:edit')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpBenefitDTO dwsPrpBenefitDTO) {
        return toAjax(dwsPrpBenefitService.updateDwsPrpBenefit(dwsPrpBenefitDTO));
    }

    /**
     * 删除再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:remove')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpBenefitService.deleteDwsPrpBenefitByIds(Ids));
    }

    /**
     * 推送再保生存金险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:push')")
    @Log(title = "再保生存金险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody List<String> accTransNos) {
        String pushBy = getUsername();
        Integer pushStatus = 1; // 已推送
        return toAjax(dwsPrpBenefitService.updateDwsPrpBenefitPushStatus(pushStatus, pushBy, accTransNos));
    }

    /**
     * 检查再保生存金险种明细是否存在
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:benefit:query')")
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpBenefitQuery dwsPrpBenefitQuery) {
        List<DwsPrpBenefitEntity> list = dwsPrpBenefitService.selectDwsPrpBenefitList(dwsPrpBenefitQuery);
        return Result.success(list.size() > 0);
    }
}
