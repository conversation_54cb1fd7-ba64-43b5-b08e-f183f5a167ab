package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.domain.DwsPrpEdorEntity;
import com.reinsurance.dto.DwsPrpEdorDTO;
import com.reinsurance.query.DwsPrpEdorQuery;
import com.reinsurance.service.IDwsPrpEdorService;
import com.reinsurance.utils.ReinsuObjectUtil;

/**
 * 再保保全险种明细Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/dws/prp/edor")
public class DwsPrpEdorController extends BaseController {
    
    @Autowired
    private IDwsPrpEdorService dwsPrpEdorService;

    /**
     * 查询再保保全险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:list')")
    @GetMapping("/list")
    public TableDataInfo list(DwsPrpEdorQuery dwsPrpEdorQuery) {
        startPage();
        List<DwsPrpEdorEntity> list = dwsPrpEdorService.selectDwsPrpEdorList(dwsPrpEdorQuery);
        return getDataTable(list);
    }

    /**
     * 导出再保保全险种明细列表
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:export')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DwsPrpEdorQuery dwsPrpEdorQuery) {
        List<DwsPrpEdorEntity> list = dwsPrpEdorService.selectDwsPrpEdorList(dwsPrpEdorQuery);
        List<DwsPrpEdorDTO> dtoList = ReinsuObjectUtil.convertList(list, DwsPrpEdorDTO.class);
        ExcelUtil<DwsPrpEdorDTO> util = new ExcelUtil<DwsPrpEdorDTO>(DwsPrpEdorDTO.class);
        util.exportExcel(response, dtoList, "再保保全险种明细数据");
    }

    /**
     * 获取再保保全险种明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:query')")
    @GetMapping(value = "/{Id}")
    public Result getInfo(@PathVariable("Id") Long Id) {
        DwsPrpEdorEntity entity = dwsPrpEdorService.selectDwsPrpEdorById(Id);
        DwsPrpEdorDTO dto = ReinsuObjectUtil.convertModel(entity, DwsPrpEdorDTO.class);
        return Result.success(dto);
    }

    /**
     * 新增再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:add')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsPrpEdorDTO dwsPrpEdorDTO) {
        return toAjax(dwsPrpEdorService.insertDwsPrpEdor(dwsPrpEdorDTO));
    }

    /**
     * 修改再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:edit')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody DwsPrpEdorDTO dwsPrpEdorDTO) {
        return toAjax(dwsPrpEdorService.updateDwsPrpEdor(dwsPrpEdorDTO));
    }

    /**
     * 删除再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:remove')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{Ids}")
    public Result remove(@PathVariable Long[] Ids) {
        return toAjax(dwsPrpEdorService.deleteDwsPrpEdorByIds(Ids));
    }

    /**
     * 推送再保保全险种明细
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:push')")
    @Log(title = "再保保全险种明细", businessType = BusinessType.UPDATE)
    @PostMapping("/push")
    public Result push(@RequestBody List<String> accTransNos) {
        String pushBy = getUsername();
        Integer pushStatus = 1; // 已推送
        return toAjax(dwsPrpEdorService.updateDwsPrpEdorPushStatus(pushStatus, pushBy, accTransNos));
    }

    /**
     * 检查再保保全险种明细是否存在
     */
    @PreAuthorize("@ss.hasPermi('dws:prp:edor:query')")
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsPrpEdorQuery dwsPrpEdorQuery) {
        List<DwsPrpEdorEntity> list = dwsPrpEdorService.selectDwsPrpEdorList(dwsPrpEdorQuery);
        return Result.success(list.size() > 0);
    }
}
