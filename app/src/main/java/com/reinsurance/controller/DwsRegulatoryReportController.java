package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.reinsurance.dto.*;
import com.reinsurance.query.*;
import com.reinsurance.service.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.enums.BasicDataEnums.EastReport;
import com.reinsurance.enums.BasicDataEnums.PrpReport;
import com.reinsurance.utils.ReinsuObjectUtil;

import lombok.extern.slf4j.Slf4j;

import com.jd.lightning.common.utils.StringUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.system.service.ISysConfigService;
import com.jd.lightning.common.core.page.TableDataInfo;

/**
 * 监管报表信息Controller
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@RestController
@RequestMapping("/regulatory/report")
public class DwsRegulatoryReportController extends BaseController {
	
	@Autowired
    private ISysConfigService sysConfigService;
	
	@Autowired
	private IDwsEastZbhtxxbService dwsEastZbhtxxbService;
	
	@Autowired
	private IDwsEastZbcpxxbService dwsEastZbcpxxbService;
	
	@Autowired
	private IDwsEastZbzdxxbService dwsEastZbzdxxbService;
	
	@Autowired
	private IDwsEastBlzbbdmxbService dwsEastBlzbbdmxbService;
	 
    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

	@Autowired
	private IDwsPrpProductService dwsPrpProductService;

	@Autowired
	private IDwsPrpInsureContService dwsPrpInsureContService;

	@Autowired
	private IDwsPrpAccountService dwsPrpAccountService;

	@Autowired
	private IDwsPrpContService dwsPrpContService;

	@Autowired
	private IDwsPrpEdorService dwsPrpEdorService;

	@Autowired
	private IDwsPrpClaimService dwsPrpClaimService;

	@Autowired
	private IDwsPrpBenefitService dwsPrpBenefitService;


	/**
	 * 查询监管报表列表
	 * @param dwsRegulatoryReportQuery
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/list")
    public TableDataInfo list(DwsRegulatoryReportQuery dwsRegulatoryReportQuery) {
        startPage();
        List<DwsRegulatoryReportDTO> list = dwsRegulatoryReportService.selectDwsRegulatoryReportList(dwsRegulatoryReportQuery);
        return getDataTable(list);
    }

    /**
     * 导出监管报表
     * @param response
     * @param dwsRegulatoryReportQuery
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:export')")
    @Log(title = "监管报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DwsRegulatoryReportQuery dwsRegulatoryReportQuery) {
        List<DwsRegulatoryReportDTO> list = dwsRegulatoryReportService.selectDwsRegulatoryReportList(dwsRegulatoryReportQuery);
        ExcelUtil<DwsRegulatoryReportDTO> util = new ExcelUtil<DwsRegulatoryReportDTO>(DwsRegulatoryReportDTO.class);
        util.exportExcel(response, list, "监管报表信息数据");
    }
    
    /**
     * 生成监管报表数据检查是不是已存在
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:add')")
    @PostMapping("/exists")
    public Result exists(@RequestBody DwsRegulatoryReportDTO dwsRegulatoryReportDTO) {
        return dwsRegulatoryReportService.checkReportExists(dwsRegulatoryReportDTO);
    }
    
    /**
     * 生成监管报表数据
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:add')")
    @Log(title = "监管报表", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody DwsRegulatoryReportDTO dwsRegulatoryReportDTO) {
        return dwsRegulatoryReportService.insertRegulatoryReportData(dwsRegulatoryReportDTO);
    }
    
    /**
     * 查询East合同信息列表
     * @param dwsEastZbhtxxbQuery
     * @return
     */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/contract/east/list")
    public TableDataInfo contractList(DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery) {
		startPage();
		List<DwsEastZbhtxxbDTO> list = dwsEastZbhtxxbService.selectDwsEastZbhtxxbList(dwsEastZbhtxxbQuery);
        return getDataTable(list);
    }
    
    /**
     * 查询East产品信息列表
     * @param dwsEastZbcpxxbQuery
     * @return
     */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/product/east/list")
    public TableDataInfo productList(DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery) {
		startPage();
		List<DwsEastZbcpxxbDTO> list = dwsEastZbcpxxbService.selectDwsEastZbcpxxbList(dwsEastZbcpxxbQuery);
        return getDataTable(list);
    }

    /**
     * 查询East账单信息列表
     * @param dwsEastZbzdxxbQuery
     * @return
     */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/bill/east/list")
    public TableDataInfo billList(DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery) {
		startPage();
		List<DwsEastZbzdxxbDTO> list = dwsEastZbzdxxbService.selectDwsEastZbzdxxbList(dwsEastZbzdxxbQuery);
        return getDataTable(list);
    }
	
	/**
	 * 导出East报表数据
	 * @param response
	 * @param reportCode
	 * @param dwsEastZbzdxxbQuery
	 */
    @PreAuthorize("@ss.hasPermi('regulatory:report:export')")
    @Log(title = "导出East报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export/east/{reportCode}")
    public void export(HttpServletResponse response, @PathVariable Integer reportCode, DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery) {
		String reportName = EastReport.check(reportCode);
		if (StringUtils.isBlank(reportName)) {
			log.info("再保导出监管报表数据未传报表编码");
			return;
		}
		if (EastReport.再保合同信息表.getCode() == reportCode) {
			DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery = ReinsuObjectUtil.convertModel(dwsEastZbzdxxbQuery, DwsEastZbhtxxbQuery.class);
			dwsEastZbhtxxbService.exportDwsEastZbhtxxb(response, dwsEastZbhtxxbQuery);
		} else if (EastReport.再保产品信息表.getCode() == reportCode) {
			DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery = ReinsuObjectUtil.convertModel(dwsEastZbzdxxbQuery, DwsEastZbcpxxbQuery.class);
			dwsEastZbcpxxbService.exportDwsEastZbcpxxb(response, dwsEastZbcpxxbQuery);
		} else if (EastReport.再保账单信息表.getCode() == reportCode) {
			dwsEastZbzdxxbService.exportDwsEastZbzdxxb(response, dwsEastZbzdxxbQuery);
		}
    }
    
    /**
     * 导入监管报表数据
     * @param reportCode
     * @param file
     * @return
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:import')")
    @Log(title = "导入East报表", businessType = BusinessType.IMPORT)
    @PostMapping("/import/east/{reportCode}")
    public Result importData(@PathVariable Integer reportCode, @RequestParam("file") MultipartFile file) {
		if(StringUtils.isBlank(EastReport.check(reportCode))) {
			return Result.error("请选择要导入的报表名称（500）");
		}
		Result result = null;
		String companyCode = sysConfigService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		String companyName = sysConfigService.selectConfigByKey(RsConstant.eastReportInsCompanyName);
		String manageCom = sysConfigService.selectConfigByKey(RsConstant.eastReportInsManageCom);
		if(EastReport.再保合同信息表.getCode() == reportCode) {
			result = dwsEastZbhtxxbService.importDwsEastZbhtxxb(companyCode, companyName, manageCom, file);
		} else if(EastReport.再保产品信息表.getCode() == reportCode) {
			result = dwsEastZbcpxxbService.importDwsEastZbcpxxb(companyCode, companyName, manageCom, file);
		}else if(EastReport.再保账单信息表.getCode() == reportCode) {
			result = dwsEastZbzdxxbService.importDwsEastZbzdxxb(companyCode, companyName, manageCom, file);
		}
        return result;
    }
    
    /**
     * 删除East报表数据
     * @param reportCode
     * @param id
     * @return
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:remove')")
    @Log(title = "删除East报表", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove/east/{reportCode}/{id}")
    public Result remove(@PathVariable Integer reportCode, @PathVariable Long id) {
		int result = 0;

		String reportName = EastReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return Result.error("请选择要删除的报表名称（500）");
		}
		if(id == null || id <= 0) {
			return Result.error("请选择要删除的" + StringUtils.trimToEmpty(reportName) + "（500）");
		}
		if(EastReport.再保合同信息表.getCode() == reportCode) {
			result = dwsEastZbhtxxbService.deleteDwsEastZbhtxxbById(id);
		} else if(EastReport.再保产品信息表.getCode() == reportCode) {
			result = dwsEastZbcpxxbService.deleteDwsEastZbcpxxbById(id);
		}else if(EastReport.再保账单信息表.getCode() == reportCode) {
			result = dwsEastZbzdxxbService.deleteDwsEastZbzdxxbById(id);
		}
		if(result == -1) {
			return Result.error("系统产生的" + reportName + "不允许删除");
		}
		if(result == -2) {
			return Result.error(reportName + "已推送不允许删除");
		}
        return Result.success(result);
    }
    
    /**
     * 推送East报表数据
     * @param reportCode
     * @param ids
     * @return
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:update')")
    @Log(title = "推送East报表", businessType = BusinessType.UPDATE)
	@GetMapping("/push/east/{reportCode}/{ids}")
    public Result push(@PathVariable Integer reportCode, @PathVariable Long [] ids) {
		String reportName = PrpReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return Result.error("请选择要推送的报表名称（500）");
		}
		if(ids == null || ids.length <= 0) {
			return Result.error("请选择要推送的" + StringUtils.trimToEmpty(reportName) + "（500）");
		}
		Result result = null;
		if(EastReport.再保合同信息表.getCode() == reportCode) {
			result = dwsEastZbhtxxbService.updateDwsEastZbhtxxbPushStatus(ids);
		} else if(EastReport.再保产品信息表.getCode() == reportCode) {
			result = dwsEastZbcpxxbService.updateDwsEastZbcpxxbPushStatus(ids);
		}else if(EastReport.再保账单信息表.getCode() == reportCode) {
			result = dwsEastZbzdxxbService.updateDwsEastZbzdxxbPushStatus(ids);
		}
		return result;
    }
    
    /***
     * 导出East比例再保保单明细
     * @param response
     * @param ids
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:export')")
    @Log(title = "East比例再保保单明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export/east/blzbbdmxb/{ids}")
    public void exportBlzbbdmxb(HttpServletResponse response, @PathVariable Long [] ids) {
    	dwsEastBlzbbdmxbService.exportDwsEastBlzbbdmxb(response, ids);
    }
    
    /**
     * 导入East比例再保保单明细
     * @param id
     * @param file
     * @return
     */
    @PreAuthorize("@ss.hasPermi('regulatory:report:import')")
    @Log(title = "East比例再保保单明细", businessType = BusinessType.IMPORT)
    @PostMapping("/import/east/blzbbdmxb/{id}")
    public Result importBlzbbdmxb(@PathVariable Long id, @RequestParam("file") MultipartFile file) {
    	if(id == null) {
    		return Result.error("请选择要导入比例再保保单明细的账单");
    	}
		return dwsEastBlzbbdmxbService.importDwsEastBlzbbdmxb(id, file);
    }

	/**
	 * 查询保单登记再保产品信息列表
	 * @param dwsPrpProductQuery
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/product/prp/list")
	public TableDataInfo prpProductList(DwsPrpProductQuery dwsPrpProductQuery) {
		startPage();
		List<DwsPrpProductDTO> list = dwsPrpProductService.selectDwsPrpProductList(dwsPrpProductQuery);
		return getDataTable(list);
	}

	/**
	 * 查询保单登记再保合同信息列表
	 * @param dwsPrpInsureContQuery
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/contract/prp/list")
	public TableDataInfo prpContractList(DwsPrpInsureContQuery dwsPrpInsureContQuery) {
		startPage();
		List<DwsPrpInsureContDTO> list = dwsPrpInsureContService.selectDwsPrpInsureContList(dwsPrpInsureContQuery);
		return getDataTable(list);
	}


	/**
	 * 查询保单登记再保账单信息列表
	 * @param dwsPrpAccountQuery
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:list')")
	@GetMapping("/account/prp/list")
	public TableDataInfo prpAccountList(DwsPrpAccountQuery dwsPrpAccountQuery) {
		startPage();
		List<DwsPrpAccountDTO> list = dwsPrpAccountService.selectDwsPrpAccountList(dwsPrpAccountQuery);
		return getDataTable(list);
	}

	/**
	 * 导出保单登记报表数据
	 * @param response
	 * @param reportCode
	 * @param dwsPrpAccountQuery
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:export')")
	@Log(title = "导出保单登记报表", businessType = BusinessType.EXPORT)
	@PostMapping("/export/prp/{reportCode}")
	public void exportPrpData(HttpServletResponse response, @PathVariable Integer reportCode, DwsPrpAccountQuery dwsPrpAccountQuery) {
		String reportName = EastReport.check(reportCode);
		if (StringUtils.isBlank(reportName)) {
			log.info("再保导出监管报表数据未传报表编码");
			return;
		}
		if(PrpReport.LRInsureCont.getCode() ==reportCode) {
			DwsPrpInsureContQuery dwsPrpInsureContQuery = ReinsuObjectUtil.convertModel(dwsPrpAccountQuery, DwsPrpInsureContQuery.class);
			dwsPrpInsureContService.exportDwsPrpInsureCont(response, dwsPrpInsureContQuery);
		}else if(PrpReport.LRProduct.getCode() == reportCode) {
			DwsPrpProductQuery dwsPrpProductQuery = ReinsuObjectUtil.convertModel(dwsPrpAccountQuery, DwsPrpProductQuery.class);
			dwsPrpProductService.exportDwsPrpProduct(response, dwsPrpProductQuery);
		}else if(PrpReport.LRAccount.getCode() == reportCode) {
			dwsPrpAccountService.exportDwsPrpAccount(response, dwsPrpAccountQuery);
		}
	}

	/**
	 * 导入保单登记报表数据
	 * @param reportCode
	 * @param file
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:import')")
	@Log(title = "导入保单登记报表", businessType = BusinessType.IMPORT)
	@PostMapping("/import/prp/{reportCode}")
	public Result importPrpData(@PathVariable Integer reportCode, @RequestParam("file") MultipartFile file) {
		if(StringUtils.isBlank(PrpReport.check(reportCode))) {
			return Result.error("请选择要导入的报表名称（500）");
		}
		Result result = null;
		String companyCode = sysConfigService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		if(PrpReport.LRInsureCont.getCode() ==reportCode) {
			result = dwsPrpInsureContService.importDwsPrpInsureCont(companyCode, file);
		}else if(PrpReport.LRProduct.getCode() == reportCode) {
			result = dwsPrpProductService.importDwsPrpProduct(companyCode, file);
		}else if(PrpReport.LRAccount.getCode() == reportCode) {
			result = dwsPrpAccountService.importDwsPrpAccount(companyCode, file);
		}
		return result;
	}

	/**
	 * 删除保单登记报表数据
	 * @param reportCode
	 * @param id
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:remove')")
	@Log(title = "删除保单登记报表", businessType = BusinessType.DELETE)
	@DeleteMapping("/remove/prp/{reportCode}/{id}")
	public Result removePrp(@PathVariable Integer reportCode, @PathVariable Long id) {
		String reportName = EastReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return Result.error("请选择要删除的报表名称（500）");
		}
		if(id == null || id <= 0) {
			return Result.error("请选择要删除的" + StringUtils.trimToEmpty(reportName) + "（500）");
		}

		int result = 0;
		if(PrpReport.LRInsureCont.getCode() == reportCode) {
			result = dwsPrpInsureContService.deleteDwsPrpInsureContById(id);
		}else if(PrpReport.LRProduct.getCode() == reportCode) {
			result = dwsPrpProductService.deleteDwsPrpProductById(id);
		}else if(PrpReport.LRAccount.getCode() == reportCode) {
			result = dwsPrpAccountService.deleteDwsPrpAccountById(id);
		}

		if(result == -1) {
			return Result.error("系统产生的" + reportName + "不允许删除");
		}
		if(result == -2) {
			return Result.error(reportName + "已推送不允许删除");
		}
		return Result.success(result);
	}

	/**
	 * 推送保单登记报表数据
	 * @param reportCode
	 * @param ids
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:update')")
	@Log(title = "推送保单登记报表", businessType = BusinessType.UPDATE)
	@GetMapping("/push/prp/{reportCode}/{ids}")
	public Result pushPrp(@PathVariable Integer reportCode, @PathVariable Long [] ids) {
		String reportName = PrpReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return Result.error("请选择要推送的报表名称（500）");
		}
		if(ids == null || ids.length <= 0) {
			return Result.error("请选择要推送的" + StringUtils.trimToEmpty(reportName) + "（500）");
		}
		Result result = null;
		if(PrpReport.LRInsureCont.getCode() == reportCode) {
			result = dwsPrpInsureContService.updateDwsPrpInsureContPushStatus(ids);
		}else if(PrpReport.LRProduct.getCode() == reportCode) {
			result = dwsPrpProductService.updateDwsPrpProductPushStatus(ids);
		}else if(PrpReport.LRAccount.getCode() == reportCode) {
			result = dwsPrpAccountService.updateDwsPrpAccountPushStatus(ids);
		}
		return result;
	}

	/***
	 * 导出保单登记明细
	 * @param response
	 * @param reportCode
	 * @param ids
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:export')")
	@Log(title = "保单登记明细报表", businessType = BusinessType.EXPORT)
	@PostMapping("/export/prp/details/{reportCode}/{ids}")
	public void exportPrpDetails(HttpServletResponse response, @PathVariable Integer reportCode, @PathVariable Long [] ids) {
		String reportName = PrpReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return;
		}
		if(PrpReport.LRCont.getCode() == reportCode){
			dwsPrpContService.exportDwsPrpCont(response, ids);
		}else if (PrpReport.LREdor.getCode() == reportCode){
			dwsPrpEdorService.exportDwsPrpEdor(response, ids);
		}else if (PrpReport.LRClaim.getCode() == reportCode){
			dwsPrpClaimService.exportDwsPrpClaim(response, ids);
		}else if (PrpReport.LRBenefit.getCode() == reportCode){
			dwsPrpBenefitService.exportDwsPrpBenefit(response, ids);
		}
	}

	/**
	 * 导入保单登记明细
	 * @param reportCode
	 * @param id
	 * @param file
	 * @return
	 */
	@PreAuthorize("@ss.hasPermi('regulatory:report:import')")
	@Log(title = "保单登记明细报表", businessType = BusinessType.IMPORT)
	@PostMapping("/import/prp/details/{reportCode}/{id}")
	public Result importPrpDetails(@PathVariable Integer reportCode, @PathVariable Long id, @RequestParam("file") MultipartFile file) {
		String reportName = PrpReport.check(reportCode);
		if(StringUtils.isBlank(reportName)) {
			return Result.error("请选择要导入的明细类型");
		}
		if(id == null) {
			return Result.error("请选择要导入明细的账单");
		}
		if(PrpReport.LRCont.getCode() == reportCode){
			return dwsPrpContService.importDwsPrpCont(id, file);
		}else if (PrpReport.LREdor.getCode() == reportCode){
			return dwsPrpEdorService.importDwsPrpEdor(id, file);
		}else if (PrpReport.LRClaim.getCode() == reportCode){
			return dwsPrpClaimService.importDwsPrpClaim(id, file);
		}else if (PrpReport.LRBenefit.getCode() == reportCode){
			return dwsPrpBenefitService.importDwsPrpBenefit(id, file);
		}
		return Result.error("要导入的明细类型有误，无法导入");
	}
}
