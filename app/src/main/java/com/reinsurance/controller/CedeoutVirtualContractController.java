package com.reinsurance.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.reinsurance.dto.CedeoutVirtualCompanyDTO;
import com.reinsurance.dto.CedeoutVirtualContractDTO;
import com.reinsurance.service.ICedeoutVirtualContractService;
import com.reinsurance.query.CedeoutVirtualCompanyQuery;
import com.reinsurance.service.ICedeoutCompanyService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.poi.ExcelUtil;

import com.reinsurance.query.CedeoutVirtualContractQuery;

/**
 * 再保虚拟合同Controller
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
@RestController
@RequestMapping("/reinsurance/virtual/contract")
public class CedeoutVirtualContractController extends BaseController {
    @Autowired
    private ICedeoutVirtualContractService cedeoutVirtualContractService;
    @Autowired
    private ICedeoutCompanyService cedeoutCompanyService;

    /**
     * 查询虚拟合同列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:list')")
    @GetMapping("/list")
    public TableDataInfo list(CedeoutVirtualContractQuery cedeoutVirtualContractQuery)
    {
        startPage();
        List<CedeoutVirtualContractDTO> list = cedeoutVirtualContractService.selectCedeoutVirtualContractList(cedeoutVirtualContractQuery);
        return getDataTable(list);
    }

    /**
     * 查询虚拟合同列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:list')")
    @GetMapping("/getVirtualCodeList")
    public Result getVirtualCodeList() {
        CedeoutVirtualContractQuery cedeoutVirtualContractQuery = new CedeoutVirtualContractQuery();
        List<CedeoutVirtualContractDTO> list = cedeoutVirtualContractService.selectCedeoutVirtualContractList(cedeoutVirtualContractQuery);
        return Result.success(list);
    }

    /**
     * 查询虚拟合同公司列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:list')")
    @GetMapping("/getVirtualCompanyList/{virtualCode}")
    public Result getVirtualCompanyList(@PathVariable("virtualCode") String virtualCode) {
        CedeoutVirtualCompanyQuery query = new CedeoutVirtualCompanyQuery();
        query.setVirtualCode(virtualCode);
        List<CedeoutVirtualCompanyDTO> list = cedeoutVirtualContractService.selectCedeoutVirtualCompanyList(query);
        if(!CollectionUtils.isEmpty(list)){
           list.forEach(f -> {
               f.setCompanyName(cedeoutCompanyService.selectCedeoutCompanyByCode(f.getCompanyCode()).getCompanyName());
           });
        }
        return Result.success(list);
    }


    /**
     * 导出虚拟合同列表
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:export')")
    @Log(title = "虚拟合同", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CedeoutVirtualContractQuery cedeoutVirtualContractQuery)
    {
        List<CedeoutVirtualContractDTO> list = cedeoutVirtualContractService.selectCedeoutVirtualContractList(cedeoutVirtualContractQuery);
        ExcelUtil<CedeoutVirtualContractDTO> util = new ExcelUtil<CedeoutVirtualContractDTO>(CedeoutVirtualContractDTO.class);
        util.exportExcel(response, list, "虚拟合同数据");
    }

    /**
     * 获取虚拟合同详细信息
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id)
    {
        return Result.success(cedeoutVirtualContractService.selectCedeoutVirtualContractById(id));
    }

    /**
     * 新增虚拟合同
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:add')")
    @Log(title = "虚拟合同", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public Result add(@RequestBody @Validated(CedeoutVirtualContractDTO.Add.class) CedeoutVirtualContractDTO cedeoutVirtualContractDTO) {
        int n = cedeoutVirtualContractService.insertCedeoutVirtualContract(cedeoutVirtualContractDTO);
        if(n > 0){
            return Result.success(n);
        }
        if(n == -1){
            return Result.error("新增失败! 再保公司不可为空!");
        }
        if(n == -2){
            return Result.error("新增失败! 虚拟合同编码已存在!");
        }
        if(n == -3){
            return Result.error("新增失败! 分保比例之和必须为100 !");
        }
        if(n == -4){
            return Result.error("新增失败! 再保公司不可重复!");
        }
        return Result.error("系统错误,请联系管理员!");
    }

    /**
     * 修改虚拟合同
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:edit')")
    @Log(title = "虚拟合同", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/edit")
    public Result edit(@RequestBody @Validated(CedeoutVirtualContractDTO.Update.class) CedeoutVirtualContractDTO cedeoutVirtualContractDTO) {
        int n = cedeoutVirtualContractService.updateCedeoutVirtualContract(cedeoutVirtualContractDTO);
        if(n > 0){
            return Result.success(n);
        }
        if(n == -1){
            return Result.error("新增失败! 再保公司不可为空!");
        }
        if(n == -2){
            return Result.error("新增失败! 虚拟合同编码已存在!");
        }
        if(n == -3){
            return Result.error("新增失败! 分保比例之和必须为100 !");
        }
        if(n == -4){
            return Result.error("新增失败! 再保公司不可重复!");
        }
        if(n == -5){
            return Result.error("已被再保方案绑定,不可删除!");
        }
        return Result.error("系统错误,请联系管理员!");
    }

    /**
     * 删除虚拟合同
     */
    @PreAuthorize("@ss.hasPermi('reinsurance:virtualContract:remove')")
    @Log(title = "虚拟合同", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}")
    public Result remove(@PathVariable Long id) {
        int n = cedeoutVirtualContractService.deleteCedeoutVirtualContractById(id);
        if(n == -1){
            return Result.error("已被再保方案绑定,不可删除!");
        }
        return Result.success();
    }
    
}
