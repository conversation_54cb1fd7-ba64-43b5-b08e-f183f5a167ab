package com.reinsurance.core.config.customDictHandler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.poi.ExcelHandlerAdapter;
import org.springframework.stereotype.Component;
import java.util.List;
import java.util.Map;

/**
 * 导出excel的自定义字典数据转换处理
 */
@Component
public class SysDictExportConvert implements ExcelHandlerAdapter {

    /** 字典缓存计时器，1分钟之内，不允许重复查询 */
    private static final Map<String, Long> exportDictTimeMap = MapUtil.newConcurrentHashMap();

    /**
     * 缓存导出时的字典数据
     * key: dictType
     * value: 字典数据：key：编码，value：名称
     */
    private static final Map<String, Map<Object, Object>> exportDictMap = MapUtil.newConcurrentHashMap();

    @Override
    public Object format(Object val, String[] strings) {
        if (ArrayUtil.isEmpty(strings)) {
            return val;
        }
        if (ObjectUtil.isNull(val)) {
            return null;
        }
        Map<Object, Object> loadDictData = loadDictData(strings[0], val.toString());
        return loadDictData.getOrDefault(val.toString(), val);
    }

    /**
     * 加载字典数据
     * @param dictType 字典类型
     * @return 字典数据
     */
    private Map<Object, Object> loadDictData(String dictType, String dictKey) {
        Map<Object, Object> dictDataCacheMap = exportDictMap.get(dictType);
        //有缓存，返回缓存
        if (MapUtil.isNotEmpty(dictDataCacheMap) && dictDataCacheMap.containsKey(dictKey)) {
            return dictDataCacheMap;
        }
        //无缓存，检查是否允许查询数据库，当前时间 - 上次查询时间 < 1分钟，不进行查询返回空数据
        Map<Object, Object> dictDataMap = MapUtil.newHashMap();
        Long timeStamp = exportDictTimeMap.get(dictType);
        if (ObjectUtil.isNotNull(timeStamp) && System.currentTimeMillis() - timeStamp < 60 * 1000) {
            return dictDataMap;
        }
        SysDict.BeanMethod beanMethod = SysDict.BeanMethod.dictMethodMap.get(dictType);
        List<SysDictData> dictDataList = ReflectUtil.invoke(beanMethod.getBean(), beanMethod.getMethod(), dictType);
        if (CollectionUtil.isNotEmpty(dictDataList)) {
            for (SysDictData dictData : dictDataList) {
                dictDataMap.put(dictData.getDictValue(), dictData.getDictLabel());
            }
            exportDictMap.put(dictType, dictDataMap);
        }
        //查询后，记录本次查询时间
        exportDictTimeMap.put(dictType, System.currentTimeMillis());
        return dictDataMap;
    }

}
