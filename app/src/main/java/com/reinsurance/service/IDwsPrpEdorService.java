package com.reinsurance.service;

import java.util.List;
import java.util.Map;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.domain.DwsPrpEdorEntity;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.dto.DwsPrpEdorDTO;
import com.reinsurance.query.DwsPrpEdorQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 再保保全变更信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IDwsPrpEdorService {
    
    /**
     * 查询再保保全变更信息
     *
     * @param Id 再保保全变更信息主键
     * @return 再保保全变更信息
     */
    public DwsPrpEdorEntity selectDwsPrpEdorById(Long Id);

    /**
     * 查询再保保全变更信息列表
     *
     * @param dwsPrpEdorQuery 再保保全变更信息
     * @return 再保保全变更信息集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorList(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 根据主键数组查询再保保全变更信息列表
     *
     * @param Ids 主键数组
     * @return 再保保全变更信息集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorByIds(Long[] Ids);

    /**
     * 新增再保保全变更信息
     *
     * @param dwsPrpEdorDTO 再保保全变更信息
     * @return 结果
     */
    public int insertDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO);

    /**
     * 批量新增再保保全变更信息
     *
     * @param dwsPrpEdorList 再保保全变更信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpEdor(List<DwsPrpEdorEntity> dwsPrpEdorList);

    /**
     * 修改再保保全变更信息
     *
     * @param dwsPrpEdorDTO 再保保全变更信息
     * @return 结果
     */
    public int updateDwsPrpEdor(DwsPrpEdorDTO dwsPrpEdorDTO);

    /**
     * 批量删除再保保全变更信息
     *
     * @param Ids 需要删除的再保保全变更信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpEdorByIds(Long[] Ids);

    /**
     * 删除再保保全变更信息信息
     *
     * @param Id 再保保全变更信息主键
     * @return 结果
     */
    public int deleteDwsPrpEdorById(Long Id);

    /**
     * 查询再保保全变更信息总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpEdorCountByAccTransNo(String accTransNo);

    /**
     * 查询再保保全变更信息列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保保全变更信息列表
     */
    List<DwsPrpEdorEntity> selectDwsPrpEdorListByAccTransNo(String accTransNo, int pageSize, int startRows);

    /**
     * 删除再保保全变更信息
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpEdorByAccTransNo(String accTransNo);

    /**
     * 批量插入再保保全变更信息数据（已结算）
     * @param account
     * @return
     */
    public int insertDwsPrpEdorFormTrade(DwsPrpAccountDTO account);

    /**
     * 批量插入再保保全变更信息数据（未结算）
     * @param account
     * @return
     */
    public int insertPreDwsPrpEdorFormTrade(DwsPrpAccountDTO account);

    /**
     * 查询再保保全变更信息交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return 再保保全变更信息ID集合
     */
    public List<Long> selectWaitSetTransNoDwsPrpEdorListByAccTransNo(String accTransNo, int limit);

    /**
     * 批量更新再保保全变更信息交易编码（streamload方式）
     * @param contList
     * @return
     */
    public int updateDwsPrpEdorTransactionNo(List<Map<String, Object>> contList);

    /**
     * 导入再保保全变更信息
     *
     * @param accountId 所属账单Id
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpEdor(Long accountId, MultipartFile file);

    /**
     * 导出再保保全变更信息
     *
     * @param response 响应对象
     * @param accountIds 所属账单Id集合
     */
    public void exportDwsPrpEdor(HttpServletResponse response, Long[] accountIds);
}
