package com.reinsurance.service;

import java.util.List;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.domain.DwsPrpBenefitEntity;
import com.reinsurance.dto.DwsPrpBenefitDTO;
import com.reinsurance.query.DwsPrpBenefitQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 再保生存金信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IDwsPrpBenefitService {
    
    /**
     * 查询再保生存金信息
     *
     * @param Id 再保生存金信息主键
     * @return 再保生存金信息
     */
    public DwsPrpBenefitEntity selectDwsPrpBenefitById(Long Id);

    /**
     * 查询再保生存金信息列表
     *
     * @param dwsPrpBenefitQuery 再保生存金信息
     * @return 再保生存金信息集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitList(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 根据主键数组查询再保生存金信息列表
     *
     * @param Ids 主键数组
     * @return 再保生存金信息集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 新增再保生存金信息
     *
     * @param dwsPrpBenefitDTO 再保生存金信息
     * @return 结果
     */
    public int insertDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO);

    /**
     * 批量新增再保生存金信息
     *
     * @param dwsPrpBenefitList 再保生存金信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpBenefit(List<DwsPrpBenefitEntity> dwsPrpBenefitList);

    /**
     * 修改再保生存金信息
     *
     * @param dwsPrpBenefitDTO 再保生存金信息
     * @return 结果
     */
    public int updateDwsPrpBenefit(DwsPrpBenefitDTO dwsPrpBenefitDTO);

    /**
     * 批量删除再保生存金信息
     *
     * @param Ids 需要删除的再保生存金信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 删除再保生存金信息信息
     *
     * @param Id 再保生存金信息主键
     * @return 结果
     */
    public int deleteDwsPrpBenefitById(Long Id);

    /**
     * 查询再保生存金信息总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpBenefitCountByAccTransNo(String accTransNo);

    /**
     * 查询再保生存金信息列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保生存金信息列表
     */
    List<DwsPrpBenefitEntity> selectDwsPrpBenefitListByAccTransNo(String accTransNo, int pageSize, int startRows);

    /**
     * 删除再保生存金信息
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpBenefitByAccTransNo(String accTransNo);

    /**
     * 导入再保生存金信息
     *
     * @param accountId 所属账单Id
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpBenefit(Long accountId, MultipartFile file);

    /**
     * 导出再保生存金信息
     *
     * @param response 响应对象
     * @param accountIds 所属账单Id集合
     */
    public void exportDwsPrpBenefit(HttpServletResponse response, Long[] accountIds);
}
