package com.reinsurance.service;

import java.util.List;

import com.reinsurance.dto.CedeoutFacultativeCedeoutTypeDTO;
import com.reinsurance.dto.CedeoutFacultativeDTO;
import com.reinsurance.query.CedeoutFacultativeQuery;

/**
 * 临分数据Service接口
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface ICedeoutFacultativeService {
    /**
     * 查询临分数据
     *
     * @param id 临分数据主键
     * @return 临分数据DTO
     */
    public CedeoutFacultativeDTO selectCedeoutFacultativeById(Long id);

    /**
     * 查询临分数据列表
     *
     * @param cedeoutFacultativeQuery 临分数据Query
     * @return 临分数据DTO 集合
     */
    List<CedeoutFacultativeDTO> selectCedeoutFacultativeList(CedeoutFacultativeQuery cedeoutFacultativeQuery);

    /**
     * 新增临分数据
     *
     * @param cedeoutFacultativeDTO 临分数据DTO
     * @return 结果
     */
    int insertCedeoutFacultative(CedeoutFacultativeDTO cedeoutFacultativeDTO);

    /**
     * 修改临分数据
     *
     * @param cedeoutFacultativeDTO 临分数据DTO
     * @return 结果
     */
    int updateCedeoutFacultative(CedeoutFacultativeDTO cedeoutFacultativeDTO);

    /**
     * 批量删除临分数据
     *
     * @param ids 需要删除的临分数据主键集合
     * @return 结果
     */
    int deleteCedeoutFacultativeByIds(Long[] ids);

    /**
     * 删除临分数据信息
     *
     * @param id 临分数据主键
     * @return 结果
     */
    int deleteCedeoutFacultativeById(Long id);

    /**
     * 自留分保/合同分保/自定义方案
     */
    void cedeoutType(CedeoutFacultativeCedeoutTypeDTO cedeoutTypeDTO);

    /**
     * 录入完毕
     */
    void inputCompleted(Long[] ids);

    /**
     * 调整临分数据
     *
     * @param cedeoutFacultativeDTO 临分数据DTO
     * @return 结果
     */
    int adjustment(CedeoutFacultativeDTO cedeoutFacultativeDTO);

}
