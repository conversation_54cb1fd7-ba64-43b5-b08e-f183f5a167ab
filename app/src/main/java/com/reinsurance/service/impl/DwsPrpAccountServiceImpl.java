package com.reinsurance.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpStatus;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.*;
import com.reinsurance.service.IRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.poi.ExcelUtil;
import com.jd.lightning.common.utils.StringUtils;

import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.query.DwsPrpAccountQuery;
import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpAccountService;
import com.reinsurance.service.IDwsRegulatoryReportService;
import com.reinsurance.enums.BasicDataEnums.ReportDataSource;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;
import com.reinsurance.enums.BasicDataEnums.RegulatorReport;
import com.reinsurance.enums.BasicDataEnums.PrpReport;
import com.reinsurance.enums.BasicDataEnums.PrpBillType;

import lombok.extern.slf4j.Slf4j;

/**
 * 保单登记再保账单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpAccountServiceImpl implements IDwsPrpAccountService {

    @Autowired
    private IRedisService redisService;

    @Autowired
    private DwsPrpContMapper dwsPrpContMapper;

    @Autowired
    private DwsPrpEdorMapper dwsPrpEdorMapper;

    @Autowired
    private DwsPrpClaimMapper dwsPrpClaimMapper;

    @Autowired
    private DwsPrpBenefitMapper dwsPrpBenefitMapper;

    @Autowired
    private DwsPrpAccountMapper dwsPrpAccountMapper;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询保单登记再保账单信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 保单登记再保账单信息
     */
    @Override
    public DwsPrpAccountDTO selectDwsPrpAccountById(Long Id) {
        DwsPrpAccountEntity entity = dwsPrpAccountMapper.selectDwsPrpAccountById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpAccountDTO.class);
    }

    /**
     * 查询保单登记再保账单信息列表
     *
     * @param dwsPrpAccountQuery 保单登记再保账单信息
     * @return 保单登记再保账单信息
     */
    @Override
    public List<DwsPrpAccountDTO> selectDwsPrpAccountList(DwsPrpAccountQuery dwsPrpAccountQuery) {
        List<DwsPrpAccountEntity> entityList = dwsPrpAccountMapper.selectDwsPrpAccountList(dwsPrpAccountQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpAccountDTO.class);
    }

    /**
     * 新增保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO) {
        DwsPrpAccountEntity dwsPrpAccountEntity = ReinsuObjectUtil.convertModel(dwsPrpAccountDTO, DwsPrpAccountEntity.class);
        dwsPrpAccountEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpAccountMapper.insertDwsPrpAccount(dwsPrpAccountEntity);
    }

    /**
     * 批量新增保单登记再保账单信息
     *
     * @param dwsPrpAccountList 保单登记再保账单信息列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpAccount(List<DwsPrpAccountDTO> dwsPrpAccountList) {
        List<DwsPrpAccountEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpAccountList, DwsPrpAccountEntity.class);
        return dwsPrpAccountMapper.insertBatchDwsPrpAccount(entitys);
    }

    /**
     * 修改保单登记再保账单信息
     *
     * @param dwsPrpAccountDTO 保单登记再保账单信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpAccount(DwsPrpAccountDTO dwsPrpAccountDTO) {
        DwsPrpAccountEntity dwsPrpAccountEntity = ReinsuObjectUtil.convertModel(dwsPrpAccountDTO, DwsPrpAccountEntity.class);
        dwsPrpAccountEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpAccountMapper.updateDwsPrpAccount(dwsPrpAccountEntity);
    }

    /**
     * 批量删除保单登记再保账单信息
     *
     * @param Ids 需要删除的保单登记再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpAccountByIds(Long[] Ids) {
        List<DwsPrpAccountEntity> accountList = dwsPrpAccountMapper.selectDwsPrpAccountByIds(Ids);
        if(CollUtil.isEmpty(accountList)){
            return 0;
        }
        int deleteRows = dwsPrpAccountMapper.deleteDwsPrpAccountByIds(Ids);
        if(deleteRows > 0){
            for(DwsPrpAccountEntity account : accountList){
                dwsPrpContMapper.deleteDwsPrpContByAccTransNo(account.getTransactionNo());
                dwsPrpEdorMapper.deleteDwsPrpEdorByAccTransNo(account.getTransactionNo());
                dwsPrpClaimMapper.deleteDwsPrpClaimByAccTransNo(account.getTransactionNo());
                dwsPrpBenefitMapper.deleteDwsPrpBenefitByAccTransNo(account.getTransactionNo());
            }
            List<Integer> reportYears = accountList.stream().map(DwsPrpAccountEntity::getReportYear).distinct().collect(Collectors.toList());
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), reportYears);
        }
        return deleteRows;
    }

    /**
     * 删除保单登记再保账单信息信息
     *
     * @param Id 保单登记再保账单信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpAccountById(Long Id) {
        DwsPrpAccountEntity entity = dwsPrpAccountMapper.selectDwsPrpAccountById(Id);
        if(entity == null) {
            return 0;
        }
        if(ReportDataSource.系统.getCode() == entity.getDataSource()) {
            return -1;
        }
        if(ReportPushStatus.已推送.getCode() == entity.getPushStatus()) {
            return -2;
        }
        int deleteRows = dwsPrpAccountMapper.deleteDwsPrpAccountById(Id);
        if(deleteRows > 0) {
            dwsPrpContMapper.deleteDwsPrpContByAccTransNo(entity.getTransactionNo());
            dwsPrpEdorMapper.deleteDwsPrpEdorByAccTransNo(entity.getTransactionNo());
            dwsPrpClaimMapper.deleteDwsPrpClaimByAccTransNo(entity.getTransactionNo());
            dwsPrpBenefitMapper.deleteDwsPrpBenefitByAccTransNo(entity.getTransactionNo());
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.DELETE, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), Arrays.asList(entity.getReportYear()));
        }
        return deleteRows;
    }

    /**
     * 导入保单登记再保账单信息
     *
     * @param companyCode 保险机构代码
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpAccount(String companyCode, MultipartFile file) {
        try {
            // 1. 解析Excel文件
            ExcelUtil<DwsPrpAccountDTO> util = new ExcelUtil<>(DwsPrpAccountDTO.class);
            List<DwsPrpAccountDTO> accountList = util.importExcel(file.getInputStream());
            if(CollUtil.isEmpty(accountList)) {
                return Result.error("导入数据不能为空！");
            }
            // 2. 信息校验
            String error = this.checkImportData(companyCode, accountList);
            if(StringUtils.isNotBlank(error)) {
                return Result.error(error);
            }
            // 3. 生成流水号
            String keySuffix = accountList.get(0).getCompanyCode();
            List<String> serialNos = redisService.getUniqueCodes(BasicDataEnums.RedisKeyModule.PRP, keySuffix, accountList.size());
            if(CollUtil.isEmpty(serialNos) || accountList.size() != serialNos.size()) {
                return Result.error("生成流水号失败，请联系管理员。");
            }
            // 4.设置系统字段
            int index = 0;
            for (DwsPrpAccountDTO account : accountList) {
                account.setTransactionNo(serialNos.get(index));
                account.setCreateTime(DateUtils.getNowDate());
                account.setUpdateTime(DateUtils.getNowDate());
                account.setCreateBy(SecurityUtils.getUsername());
                account.setUpdateBy(SecurityUtils.getUsername());
                account.setDataSource(ReportDataSource.系统.getCode());
                account.setPushStatus(ReportPushStatus.未推送.getCode());
                account.setAccountPeriod(account.getReportYear() + String.format("%02d", account.getReportMonth()));
                index++;
            }
            // 5. 批量入库
            List<DwsPrpAccountEntity> dwsPrpAccountList = ReinsuObjectUtil.convertList(accountList, DwsPrpAccountEntity.class);
            int insertRows = dwsPrpAccountMapper.insertBatchDwsPrpAccount(dwsPrpAccountList);
            if(insertRows > 0) {
                // 6. 更新监管报表推送状态
                List<Integer> reportYears = accountList.stream().map(DwsPrpAccountDTO::getReportYear).distinct().collect(Collectors.toList());
                dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.IMPORT, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), reportYears);
            }
            return Result.success("成功导入" + insertRows + "行", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记账单信息出错, 错误原因:", e);
            return Result.error("导入" + PrpReport.LRAccount.getCode() + "表出错，请联系管理员。");
        }
    }

    /**
     * 导出保单登记再保账单信息
     *
     * @param response 响应对象
     * @param dwsPrpAccountQuery 查询条件
     */
    @Override
    public void exportDwsPrpAccount(HttpServletResponse response, DwsPrpAccountQuery dwsPrpAccountQuery) {
        List<DwsPrpAccountDTO> list = selectDwsPrpAccountList(dwsPrpAccountQuery);
        ExcelUtil<DwsPrpAccountDTO> util = new ExcelUtil<>(DwsPrpAccountDTO.class);
        util.exportExcel(response, list, "保单登记再保账单信息数据");
    }

    /**
     * 检查保单登记再保账单信息是否存在
     *
     * @param dwsPrpAccountQuery 查询条件
     * @return 结果
     */
    @Override
    public int selectDwsPrpAccountExists(DwsPrpAccountQuery dwsPrpAccountQuery) {
        return dwsPrpAccountMapper.selectDwsPrpAccountExists(dwsPrpAccountQuery);
    }

    /**
     * 更新推送状态
     *
     * @param Ids 主键数组
     * @return 结果
     */
    @Override
    public Result updateDwsPrpAccountPushStatus(Long[] Ids) {
        List<DwsPrpAccountEntity> accountList = dwsPrpAccountMapper.selectDwsPrpAccountByIds(Ids);
        if(CollUtil.isEmpty(accountList)) {
            return Result.error("要推送的" + PrpReport.LRAccount.getDesc() + "不存在。");
        }
        long incompleteCount = accountList.stream().filter(zd -> this.subDataImportFlag(zd)).count();
        if(incompleteCount > 0) {
            return Result.error("要推送的" + PrpReport.LRAccount.getDesc() + "包含未导入明细数据的账单，不允许推送。");
        }
        long alreadyPushedCount = accountList.stream().filter(cp -> ReportPushStatus.已推送.getCode() == cp.getPushStatus()).count();
        if(alreadyPushedCount > 0) {//包含已推送的数据
            return Result.error("要推送的" + PrpReport.LRAccount.getDesc() + "包含已推送的数据，不允许重复推送。");
        }
        List<String> accTransNos = accountList.stream().map(DwsPrpAccountEntity::getTransactionNo).collect(Collectors.toList());
        dwsPrpContMapper.updateDwsPrpContPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), accTransNos);
        dwsPrpEdorMapper.updateDwsPrpEdorPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), accTransNos);
        dwsPrpClaimMapper.updateDwsPrpClaimPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), accTransNos);
        dwsPrpBenefitMapper.updateDwsPrpBenefitPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), accTransNos);
        int updateRows = dwsPrpAccountMapper.updateDwsPrpAccountPushStatus(ReportPushStatus.已推送.getCode(), SecurityUtils.getUsername(), Ids);
        if(updateRows > 0) {
            List<Integer> reportYears = accountList.stream().map(DwsPrpAccountEntity::getReportYear).distinct().collect(Collectors.toList());
            dwsRegulatoryReportService.insertOrUpdateRegulatoryReport(BusinessType.UPDATE, RegulatorReport.保单登记数据报送, PrpReport.LRAccount.getCode(), reportYears);
        }
        return Result.success("推送成功", updateRows);
    }

    /**
     * 查询年度报表推送状态
     *
     * @param reportYear 报表年份
     * @return 结果
     */
    @Override
    public Integer selectAnnualReportShouldPushStatus(int reportYear) {
        return dwsPrpAccountMapper.selectAnnualReportShouldPushStatus(reportYear);
    }

    @Override
    public int updateAccountImportStatusByTransactionNo(DwsPrpAccountDTO dwsPrpAccountDTO) {
        DwsPrpAccountEntity dwsPrpAccountEntity = ReinsuObjectUtil.convertModel(dwsPrpAccountDTO, DwsPrpAccountEntity.class);
        return dwsPrpAccountMapper.updateAccountImportStatusByTransactionNo(dwsPrpAccountEntity);
    }

    private String checkImportData(String companyCode, List<DwsPrpAccountDTO> accountList) {
        int index = 0;
        StringBuffer result = new StringBuffer();
        for(DwsPrpAccountDTO acount : accountList) {
            List<String> emptys = new ArrayList<>();
            List<String> errors = new ArrayList<>();
            if(StringUtils.isBlank(acount.getCompanyCode())) {
                emptys.add("保险机构代码");
            }else {
                if(!acount.getCompanyCode().equals(companyCode)) {
                    errors.add("保险机构代码");
                }
            }
            if(StringUtils.isBlank(acount.getAccountID())) {
                emptys.add("账单编号");
            }
            if(acount.getAccountingPeriodfrom() == null) {
                emptys.add("账单起期");
            }
            if(acount.getAccountingPeriodto() == null) {
                emptys.add("账单止期");
            }
            if(StringUtils.isBlank(acount.getReinsurerCode())) {
                emptys.add("再保险公司代码");
            }
            if(StringUtils.isBlank(acount.getReinsurerName())) {
                emptys.add("再保险公司名称");
            }
            if(StringUtils.isBlank(acount.getReInsuranceContNo())) {
                emptys.add("再保险合同号码");
            }
            if(StringUtils.isBlank(acount.getReInsuranceContName())) {
                emptys.add("再保险合同名称");
            }
            if(StringUtils.isBlank(acount.getCurrency())) {
                emptys.add("货币代码");
            }
            if(StringUtils.isBlank(acount.getAccountStatus())) {
                emptys.add("账单状态");
            }
            if(StringUtils.isBlank(acount.getPairingStatus())) {
                emptys.add("结算状态");
            }else {
                if(PrpBillType.getName(acount.getPairingStatus()) == null){
                    errors.add("结算状态");
                }
                if(PrpBillType.已结算.getCode().equals(acount.getPairingStatus())) {
                    if(acount.getPairingDate() == null) {
                        emptys.add("结算日期");
                    }
                    if(acount.getCurrentRate() == null) {
                        emptys.add("结算汇率");
                    }
                }
            }
            if(acount.getReportYear() == null) {
                emptys.add("所属年份");
            }
            if(acount.getReportMonth() == null) {
                emptys.add("所属月份");
            }
            index++;
            if(emptys.size() > 0) {
                result.append("第" + index + "行：" + String.join("、", emptys) + "为空。<br/>");
            }
            if(errors.size() > 0) {
                result.append("第" + index + "行：" + String.join("、", errors) + "有误。<br/>");
            }
        }
        return result.toString();
    }

    private boolean subDataImportFlag(DwsPrpAccountEntity acount){
        if(CedeoutEnums.导入状态_未导入.getValue() == acount.getContImportStatus() ||
                CedeoutEnums.导入状态_未导入.getValue() == acount.getEdorImportStatus() ||
                CedeoutEnums.导入状态_未导入.getValue() == acount.getClaimImportStatus() ||
                CedeoutEnums.导入状态_未导入.getValue() == acount.getBenefitImportStatus()){
            return false;
        }
        return true;
    }
}
