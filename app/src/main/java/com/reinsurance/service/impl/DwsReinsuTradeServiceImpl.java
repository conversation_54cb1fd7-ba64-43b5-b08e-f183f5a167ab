package com.reinsurance.service.impl;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.reinsurance.dto.*;
import com.reinsurance.mapper.DwsContHistoryMapper;
import com.reinsurance.mapper.DwsEdorContLiabilityMapper;
import com.reinsurance.mapper.DwsPolicyMapper;
import com.reinsurance.mapper.DwsReinsuPolicyLiabilityMapper;
import com.reinsurance.mapper.DwsReinsuSettleBillMapper;
import com.reinsurance.service.ICedeoutBatchLogService;
import com.reinsurance.service.IDwsReinsuTradeService;
import com.reinsurance.service.IFileService;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.reinsurance.domain.DwsContHistoryEntity;
import com.reinsurance.domain.DwsEdorContLiabilityEntity;
import com.reinsurance.domain.DwsPolicyEntity;
import com.reinsurance.domain.DwsReinsuPolicyLiabilityEntity;
import com.reinsurance.domain.DwsReinsuSettleBillEntity;
import com.reinsurance.enums.BasicDataEnums.ConfirmStatus;
import com.reinsurance.enums.BasicDataEnums.DataTrackType;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.mapper.DwsReinsuTradeMapper;
import com.reinsurance.query.CedeoutBatchLogQuery;
import com.reinsurance.query.DwsReinsuTradeBillDetailQuery;
import com.reinsurance.query.DwsReinsuTradeQuery;
import com.reinsurance.query.ReInsuranceReportDataQuery;
import com.reinsurance.domain.DwsReinsuTradeEntity;
import com.reinsurance.utils.CedeoutUtils;
import com.reinsurance.utils.FileUploadUtils;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.excel.EasyExcel;
import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.StarRocksConnector;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 再保分出摊回明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-18
 */

@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsReinsuTradeServiceImpl implements IDwsReinsuTradeService
{
	@Autowired
	private IFileService fileService;
	
	@Autowired
    private DwsPolicyMapper dwsPolicyMapper;
	
	@Autowired
    private StarRocksConnector starRocksConnector;
	
	@Autowired
    private DwsContHistoryMapper dwsContHistoryMapper;
	
	@Autowired
    private DwsReinsuTradeMapper dwsReinsuTradeMapper;
    
    @Autowired
    private ICedeoutBatchLogService cedeoutBatchLogService;
    
    
    @Autowired
    private DwsReinsuSettleBillMapper dwsReinsuSettleBillMapper;
    
    @Autowired
    private DwsEdorContLiabilityMapper dwsEdorContLiabilityMapper;
    
    @Autowired
    private DwsReinsuPolicyLiabilityMapper dwsReinsuPolicyLiabilityMapper;
    
    @Override
    public DwsReinsuTradeDTO selectDwsReinsuTradeById(Long id)
    {
        DwsReinsuTradeEntity dwsReinsuTradeEntity = dwsReinsuTradeMapper.selectDwsReinsuTradeById(id);
        return ReinsuObjectUtil.convertModel(dwsReinsuTradeEntity, DwsReinsuTradeDTO.class);
    }

    @Override
    public List<DwsReinsuTradeDTO> selectDwsReinsuTradeList(DwsReinsuTradeQuery dwsReinsuTradeQuery)
    {
        List<DwsReinsuTradeEntity> list = dwsReinsuTradeMapper.selectDwsReinsuTradeList(dwsReinsuTradeQuery);
        return ReinsuObjectUtil.convertList(list, DwsReinsuTradeDTO.class);
    }

    @Override
    public int insertDwsReinsuTrade(DwsReinsuTradeDTO dwsReinsuTradeDTO)
    {
    	DwsReinsuTradeEntity dwsReinsuTradeEntity = ReinsuObjectUtil.convertModel(dwsReinsuTradeDTO, DwsReinsuTradeEntity.class);
    	dwsReinsuTradeEntity.setCreateTime(DateUtils.getNowDate());
		return dwsReinsuTradeMapper.insertDwsReinsuTrade(dwsReinsuTradeEntity);
    }
    
    @Override
    public int updateDwsReinsuTrade(DwsReinsuTradeDTO dwsReinsuTradeDTO)
    {
    	DwsReinsuTradeEntity dwsReinsuTradeEntity = ReinsuObjectUtil.convertModel(dwsReinsuTradeDTO, DwsReinsuTradeEntity.class);
    	dwsReinsuTradeEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsReinsuTradeMapper.updateDwsReinsuTrade(dwsReinsuTradeEntity);
    }

	@Override
	public int updateReleasePreviousDwsReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		return dwsReinsuTradeMapper.updateReleasePreviousDwsReinsuTrade(dwsReinsuTradeQuery);
	}

	@Override
    public int deleteDwsReinsuTradeByIds(Long[] ids)
    {
        return dwsReinsuTradeMapper.deleteDwsReinsuTradeByIds(ids);
    }

    @Override
    public int deleteDwsReinsuTradeById(Long id) {
        return dwsReinsuTradeMapper.deleteDwsReinsuTradeById(id);
    }

	@Override
	public int updateCopyDwsReinsuTradeBySrcDwsReinsuTrade() {
		return dwsReinsuTradeMapper.updateCopyDwsReinsuTradeBySrcDwsReinsuTrade();
	}

	@Override
	public int insertBatchDwsReinsuTrade(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs) {
		int result = 0;
		try {
			List<DwsReinsuTradeEntity> dwsReinsuTradeEntitys = ReinsuObjectUtil.convertList(dwsReinsuTradeDTOs, DwsReinsuTradeEntity.class);
			StarRocksOperationResult starRocksResult = starRocksConnector.insert("t_dws_reinsu_trade", ReinsuJsonUtil.toSnakeCaseJsonString(dwsReinsuTradeEntitys));
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.error("再保批量新增分出摊回记录失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
			}else {
				result = dwsReinsuTradeEntitys.size();
			}
			return result;
		} catch (Exception e) {
			log.error("再保批量新增分出摊回记录出错, 错误原因", e);
			return 0;
		}
	}
	
	@Override
	public int updateBatchDwsReinsuTrade(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs) {
		int result = 0;
		try {
			String columnNames = "id,calc_status,calc_fail_code,init_risk_amount,occupy_risk_amount,release_risk_amount,cedeout_amount,self_amount,cedeout_scale,self_scale,added_tax,cedeout_premium," +
								 "cedeout_add_premium,cedeout_total_premium,cedeout_commission,rate_data_id,rate_data_value,com_rate_data_id,com_rate_data_value,dis_rate_data_id,dis_rate_data_value," + 
								 "reserves_id,reserves,return_status,return_date,return_reason,return_premium,return_cb_premium,return_total_premium,return_claim_amount,return_expired_gold,return_commission," + 
								 "adjust_status,adjust_date,adjuster,adjust_reason,adjust_batch_no,bill_confirm_status,bill_confirm_date,bill_confirmer,remark,update_by,update_time";
			StarRocksOperationResult starRocksResult = starRocksConnector.update("t_dws_reinsu_trade", ReinsuJsonUtil.toSnakeCaseJsonString(dwsReinsuTradeDTOs), columnNames);
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.error("再保批量新增分出摊回记录失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
			}else {
				result = dwsReinsuTradeDTOs.size();
			}
			return result;
		} catch (Exception e) {
			log.error("再保批量更新分出摊回记录出错, 错误原因", e);
			return 0;
		}
	}

	@Override
	public List<DwsReinsuTradeDTO> selectWaitCedeoutDwsReinsuTrade(JobParamDTO jobParam, Integer dataCopy, Integer limit) {
		DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
		dwsReinsuTradeQuery.setDataCopy(dataCopy);
		dwsReinsuTradeQuery.getParams().put("limit", limit);
		dwsReinsuTradeQuery.setRsPayIntv(jobParam.getRsPayIntv());
		dwsReinsuTradeQuery.setDataType(CedeoutEnums.数据类型_分出.getValue());
		dwsReinsuTradeQuery.setCalcStatus(CedeoutEnums.计算状态_未计算.getValue());
		List<DwsReinsuTradeEntity> list = dwsReinsuTradeMapper.selectWaitCedeoutDwsReinsuTrade(dwsReinsuTradeQuery);
		return ReinsuObjectUtil.convertList(list, DwsReinsuTradeDTO.class);
	}

    @Override
    public List<AnomalousDataDTO> selectInsuredList(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
        List<AnomalousDataDTO> entityList = new ArrayList<>();
        if(dwsReinsuTradeQuery.getInsuredCedeoutType() == CedeoutEnums.再保保单查询_分出列表.getValue()){
        	dwsReinsuTradeQuery.setCalcStatus(CedeoutEnums.计算状态_成功.getValue());
            entityList = dwsReinsuTradeMapper.selectInsuredList(dwsReinsuTradeQuery);
        }else if(dwsReinsuTradeQuery.getInsuredCedeoutType() == CedeoutEnums.再保保单查询_未达溢额线列表.getValue()){
            // 未到达溢额线（自留额可能有数） 导致未分出保单
        	dwsReinsuTradeQuery.setCalcStatus(CedeoutEnums.计算状态_忽略.getValue());
            entityList = dwsReinsuTradeMapper.selectInsuredList(dwsReinsuTradeQuery);
        }
        return entityList;
    }

    @Override
    public List<DwsPolicyEntity> insuredUnCedeoutList(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
        // 关联业务表 将分出的数据过滤出去
        List<DwsPolicyEntity> dwsPolicyList = dwsPolicyMapper.selectDwsPolicyNotCedeoutList(dwsReinsuTradeQuery);
        return dwsPolicyList;
    }

    @Override
	public BigDecimal selectOccupyRiskAmountByInsuredNo(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		return dwsReinsuTradeMapper.selectOccupyRiskAmountByInsuredNo(dwsReinsuTradeQuery);
	}

    @Override
    public List<AnomalousDataDTO> selectAnomalousDataCalcList(CedeoutBatchLogQuery cedeoutBatchLogQuery) {
        List<AnomalousDataDTO> list = dwsReinsuTradeMapper.selectAnomalousDataCalcList(cedeoutBatchLogQuery);
        return list;
    }

	@Override
	public List<DwsReinsuTradeBillDetailDTO> selectReinsuTradeBillList(DwsReinsuTradeBillDetailQuery cedeoutBillQuery) {
		return dwsReinsuTradeMapper.selectReinsuTradeBillList(cedeoutBillQuery);
	}

	@Override
	public Result updateAdjustReinsuTradeBill(DwsReinsuTradeBillDetailDTO cedeoutBillDTO) {
		if(cedeoutBillDTO.getCedeoutPremium() != null) {
			cedeoutBillDTO.setCedeoutTotalPremium(cedeoutBillDTO.getCedeoutPremium().add(cedeoutBillDTO.getCedeoutAddPremium() == null ? BigDecimal.ZERO : cedeoutBillDTO.getCedeoutAddPremium()));
		}
		if(cedeoutBillDTO.getReturnPremium() != null) {
			cedeoutBillDTO.setReturnTotalPremium(cedeoutBillDTO.getReturnPremium().add(cedeoutBillDTO.getReturnCbPremium() == null ? BigDecimal.ZERO : cedeoutBillDTO.getReturnCbPremium()));
		}
		DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillByTradeId(cedeoutBillDTO.getId());
		if(dwsReinsuSettleBillEntity != null && ConfirmStatus.已确认.getCode() == dwsReinsuSettleBillEntity.getConfirmStatus()) {
			return Result.error("账单已确认不允许对明细账进行调整");
		}
		int updateRow = dwsReinsuTradeMapper.updateAdjustReinsuTradeBill(cedeoutBillDTO);
		if(updateRow <= 0) {
			return Result.error("调整失败");
		}
		return Result.success("调整成功");
	}
	
	@Override
	public Result updateBatchAdjustReinsuTradeBill(List<DwsReinsuTradeBillDetailDTO> cedeoutBillDTOs) {
		int result = 0;
		try {
			List<String> billNos = new ArrayList<String>();
			List<List<DwsReinsuTradeBillDetailDTO>> dwsReinsuTradeList = ListUtil.split(cedeoutBillDTOs, 10000);
			for(List<DwsReinsuTradeBillDetailDTO> list : dwsReinsuTradeList) {
				List<Long> tradeIds = list.stream().map(DwsReinsuTradeBillDetailDTO::getId).collect(Collectors.toList());
				List<DwsReinsuSettleBillEntity> dwsReinsuSettleBillList = dwsReinsuSettleBillMapper.selectDwsReinsuSettleBillByTradeIds(tradeIds);
				if(CollUtil.isNotEmpty(dwsReinsuSettleBillList)) {
					billNos.addAll(dwsReinsuSettleBillList.stream().filter(b -> ConfirmStatus.已确认.getCode() == b.getConfirmStatus()).map(DwsReinsuSettleBillEntity::getBillNo).collect(Collectors.toList()));
				}
			}
			if(CollUtil.isNotEmpty(billNos)) {
				return Result.error("账单号" + billNos.toString() + "已确认不允许对明细账进行调整");
			}
			
			Date adjustDate = DateUtils.getNowDate();
			String adjustBatchNo = CedeoutUtils.getBatchNo(SecurityUtils.getUserId());
			String columnNames = "id,cedeout_premium,cedeout_add_premium,cedeout_total_premium,cedeout_commission,return_premium,return_cb_premium,return_total_premium," + 
								 "return_claim_amount,return_expired_gold,return_commission,adjust_status,adjust_date,adjuster,adjust_reason,adjust_batch_no,update_by,update_time";
			cedeoutBillDTOs.forEach(cedeoutBill -> {
				if(cedeoutBill.getCedeoutPremium() != null) {
					cedeoutBill.setCedeoutTotalPremium(cedeoutBill.getCedeoutPremium().add(cedeoutBill.getCedeoutAddPremium() == null ? BigDecimal.ZERO : cedeoutBill.getCedeoutAddPremium()));
				}
				if(cedeoutBill.getReturnPremium() != null) {
					cedeoutBill.setReturnTotalPremium(cedeoutBill.getReturnPremium().add(cedeoutBill.getReturnCbPremium() == null ? BigDecimal.ZERO : cedeoutBill.getReturnCbPremium()));
				}
				cedeoutBill.setAdjustDate(adjustDate);
				cedeoutBill.setUpdateTime(adjustDate);
				cedeoutBill.setAdjustBatchNo(adjustBatchNo);
				cedeoutBill.setAdjuster(SecurityUtils.getUsername());
				cedeoutBill.setAdjustStatus(CedeoutEnums.是.getValue());
				cedeoutBill.setUpdateBy(cedeoutBill.getAdjuster());
			});
			StarRocksOperationResult starRocksResult = starRocksConnector.update("t_dws_reinsu_trade", ReinsuJsonUtil.toSnakeCaseJsonString(cedeoutBillDTOs), columnNames);
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.error("再保批量调整分出摊回账单失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
			}else {
				result = cedeoutBillDTOs.size();
			}
			return result>0 ? Result.success("调整成功") : Result.error("调整失败");
		} catch (Exception e) {
			log.error("再保批量调整分出摊回账单出错, 错误原因", e);
			return Result.error("调整失败");
		}
	}

	private ReInsuranceReportDataQuery setReInsuranceReportDataQuery(ReInsuranceReportDataQuery reInsuranceReportDataQuery){
		List<Integer> busiTypes = null;
		if(reInsuranceReportDataQuery.getReportDataType() == CedeoutEnums.报表数据类型_分出.getValue()){
			reInsuranceReportDataQuery.setDataType(CedeoutEnums.数据类型_分出.getValue());
		}else if(reInsuranceReportDataQuery.getReportDataType() == CedeoutEnums.报表数据类型_保全.getValue()){
			reInsuranceReportDataQuery.setDataType(CedeoutEnums.数据类型_摊回.getValue());
			busiTypes = Arrays.asList(CedeoutEnums.业务类型_失效.getValue(), CedeoutEnums.业务类型_保全.getValue());
		}else{
			reInsuranceReportDataQuery.setDataType(CedeoutEnums.数据类型_摊回.getValue());
			reInsuranceReportDataQuery.setBusiType(CedeoutEnums.业务类型_理赔.getValue());
		}
		reInsuranceReportDataQuery.getParams().put("busiTypes", busiTypes);
		return reInsuranceReportDataQuery;
	}

	@Override
	public Integer selectReportDataCount(ReInsuranceReportDataQuery reInsuranceReportDataQuery) {
		return dwsReinsuTradeMapper.selectReportDataCount(setReInsuranceReportDataQuery(reInsuranceReportDataQuery));
	}
	
	@Override
	public List<Map<String, Object>> selectReportDataList(ReInsuranceReportDataQuery reInsuranceReportDataQuery) {
		return dwsReinsuTradeMapper.selectReportDataList(setReInsuranceReportDataQuery(reInsuranceReportDataQuery));
	}

	@Override
	public List<Map<String, Object>> selectSummaryReportData(ReInsuranceReportDataQuery reInsuranceReportDataQuery) {
		return dwsReinsuTradeMapper.selectSummaryReportData(setReInsuranceReportDataQuery(reInsuranceReportDataQuery));
	}

	@Override
	public Result importHistoryData(String executor, MultipartFile[] files) {
		String batchNo = CedeoutUtils.getBatchNo(CedeoutEnums.执行方式_手动.getValue());
		log.info("导入历史分保数据处理中, batchNo:{}", batchNo);
        try {
        	List<String> dataFiles = new ArrayList<String>();
        	String dstPath = fileService.getStoragePath() + "history/trade/";
        	for (MultipartFile file : files) {
        		dataFiles.add(FileUploadUtils.upload(dstPath, file));
            }
        	this.saveCedeoutBatchLog(batchNo, executor, String.join(",", dataFiles));
        	log.info("导入历史分保数据处理中, 文件保存完成, batchNo:{}", batchNo);
        	CompletableFuture.runAsync(() -> { this.xlsFile2Table(batchNo, executor, dataFiles); });
        	return Result.success("文件上传成功，数据异步处理中。");
        } catch (Exception e) {
        	log.error("导入历史分保数据出错, batchNo:{}, 错误原因:", batchNo, e);
        	return Result.error("导入失败。");
        }
	}
	
	@Override
	public int insertImportDwsReinsuTrade(List<ImportDwsReinsuTradeDTO> importDwsReinsuTradeList) {
		int result = 0;
		try {
			StarRocksOperationResult starRocksResult = starRocksConnector.insert("t_dws_reinsu_trade", ReinsuJsonUtil.toSnakeCaseJsonString(importDwsReinsuTradeList));
			String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
			if(!("success").equalsIgnoreCase(status)) {//操作失败
				log.error("再保批量导入分出摊回记录失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
			}else {
				result = importDwsReinsuTradeList.size();
			}
			return result;
		} catch (Exception e) {
			log.error("再保批量导入分出摊回记录出错, 错误原因", e);
			return 0;
		}
	}
	
	@Override
	public List<DwsReinsuTradeDTO> selectWaitReturnOnCedeoutReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		List<DwsReinsuTradeEntity> dwsReinsuTradeList = dwsReinsuTradeMapper.selectWaitReturnOnCedeoutReinsuTrade(dwsReinsuTradeQuery);
		return ReinsuObjectUtil.convertList(dwsReinsuTradeList, DwsReinsuTradeDTO.class);
	}

	@Override
	public int insertBatchDwsReinsuTradeByPool(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs) {
		List<DwsReinsuTradeEntity> dwsReinsuTradeList = ReinsuObjectUtil.convertList(dwsReinsuTradeDTOs, DwsReinsuTradeEntity.class);
		return dwsReinsuTradeMapper.insertBatchDwsReinsuTrade(dwsReinsuTradeList);
	}
	
	@Override
	public int updateBatchCedeoutTradeReturnStatus(List<DwsReinsuTradeDTO> dwsReinsuTradeDTOs) {
		int totalUpdateRows = 0;
		List<DwsReinsuTradeEntity> dwsReinsuTradeList = ReinsuObjectUtil.convertList(dwsReinsuTradeDTOs, DwsReinsuTradeEntity.class);
		for(DwsReinsuTradeEntity cedeoutReinsuTrade : dwsReinsuTradeList) {
			int updateRows = dwsReinsuTradeMapper.updateCedeoutTradeReturnStatus(cedeoutReinsuTrade);
			totalUpdateRows += updateRows;
		}
		return totalUpdateRows;
	}
	
	@Override
	public int selectDwsReinsuTradeCountByBillNo(String billNo) {
		return dwsReinsuTradeMapper.selectDwsReinsuTradeCountByBillNo(billNo);
	}

	@Override
	public List<Map<String, Object>> selectDwsReinsuTradeListByBillNo(List<String> outColumns, String billNo, int pageSize, int startRows) {
		return dwsReinsuTradeMapper.selectDwsReinsuTradeListByBillNo(outColumns, billNo, pageSize, startRows);
	}

	@Override
	public Map<String, DwsEastZbzdxxbDTO> selectDwsReinsuTradeMoneyByBillNos(List<String> billNos) {
		Map<String, DwsEastZbzdxxbDTO> tradeMoneyMap = MapUtil.newHashMap();
		List<DwsEastZbzdxxbDTO> tradeMoneyList = dwsReinsuTradeMapper.selectDwsReinsuTradeMoneyByBillNos(billNos);
		if(CollUtil.isNotEmpty(tradeMoneyList)) {
			tradeMoneyMap = tradeMoneyList.stream().collect(Collectors.toMap(b -> b.getSettleBillNo(), b->b));
		}
		return tradeMoneyMap;
	}

	@Override
	public List<DwsEastZbzdxxbDTO> selectDwsReinsuTradeGroupSummaryAsZbzdxxb(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		return dwsReinsuTradeMapper.selectDwsReinsuTradeGroupSummaryAsZbzdxxb(dwsReinsuTradeQuery);
	}

	@Override
	public DwsReinsuTradeDTO selectOneVirtualReinsuTrade(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		DwsReinsuTradeDTO dwsReinsuTrade = null;
		if(CedeoutEnums.业务类型_新单.getValue() == dwsReinsuTradeQuery.getBusiType() || CedeoutEnums.业务类型_续期.getValue() == dwsReinsuTradeQuery.getBusiType()) {
			DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityQuery = ReinsuObjectUtil.convertModel(dwsReinsuTradeQuery, DwsReinsuPolicyLiabilityEntity.class);
	    	DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiability = dwsReinsuPolicyLiabilityMapper.selectOneReinsuPolicyLiability(dwsReinsuPolicyLiabilityQuery);
	    	if(dwsReinsuPolicyLiability != null) {
	    		dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsReinsuPolicyLiability, DwsReinsuTradeDTO.class);
	    	}else {
	    		if(DataTrackType.回溯数据.getCode() == dwsReinsuTradeQuery.getBackTrackData()) {//回溯
	    			DwsContHistoryEntity dwsContHistoryQuery = ReinsuObjectUtil.convertModel(dwsReinsuTradeQuery, DwsContHistoryEntity.class);
	    			DwsContHistoryEntity dwsContHistoryEntity = dwsContHistoryMapper.selectOneDwsContHistoryByPol(dwsContHistoryQuery);
	    			if(dwsContHistoryEntity != null) {
	    	    		dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsContHistoryEntity, DwsReinsuTradeDTO.class);
	    	    	}
	    		}
	    	}
		}else if(CedeoutEnums.业务类型_保全.getValue() == dwsReinsuTradeQuery.getBusiType()) {
			DwsEdorContLiabilityEntity dwsEdorContLiabilityQuery = new DwsEdorContLiabilityEntity();
			dwsEdorContLiabilityQuery.setPolNo(dwsReinsuTradeQuery.getPolNo());
			dwsEdorContLiabilityQuery.setContYear(dwsReinsuTradeQuery.getContYear());
			dwsEdorContLiabilityQuery.setUniqueKey((String)dwsReinsuTradeQuery.getParams().get("uniqueKey"));
			DwsEdorContLiabilityEntity dwsEdorContLiability = dwsEdorContLiabilityMapper.selectOneDwsEdorContLiability(dwsEdorContLiabilityQuery);
			if(dwsEdorContLiability != null) {
	    		dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsEdorContLiability, DwsReinsuTradeDTO.class);
	    	}
		}
		
		if(dwsReinsuTrade == null && DataTrackType.常规数据.getCode() == dwsReinsuTradeQuery.getBackTrackData()) {
			DwsPolicyEntity dwsPolicyQuery = new DwsPolicyEntity();
			dwsPolicyQuery.setPolNo(dwsReinsuTradeQuery.getPolNo());
			dwsPolicyQuery.setContYear(dwsReinsuTradeQuery.getContYear());
			dwsPolicyQuery.setBusiType(dwsReinsuTradeQuery.getBusiType());
			DwsPolicyEntity dwsPolicyEntity = dwsPolicyMapper.selectOneDwsPolicy(dwsPolicyQuery);
			if(dwsPolicyEntity != null) {
				dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsPolicyEntity, DwsReinsuTradeDTO.class);
			}else {
				dwsPolicyEntity = dwsPolicyMapper.selectOneDwsPolicyFormDwsContLiability(dwsPolicyQuery);
				if(dwsPolicyEntity != null) {
					dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsPolicyEntity, DwsReinsuTradeDTO.class);
				}
			}
		}
    	return dwsReinsuTrade;
	}

	@Override
	public Map<String, DwsPrpAccountDTO> selectDwsReinsuTradeSummaryMoneyAsPrpAccount(List<String> billNos) {
		Map<String, DwsPrpAccountDTO> tradeMoneyMap = MapUtil.newHashMap();
		List<DwsPrpAccountDTO> tradeMoneyList = dwsReinsuTradeMapper.selectDwsReinsuTradeSummaryMoneyAsPrpAccount(billNos);
		if(CollUtil.isNotEmpty(tradeMoneyList)) {
			tradeMoneyMap = tradeMoneyList.stream().collect(Collectors.toMap(b -> b.getSettleBillNo(), b->b));
		}
		return tradeMoneyMap;
	}

	@Override
	public List<DwsPrpAccountDTO> selectDwsReinsuTradeGroupSummaryAsPrpAccount(DwsReinsuTradeQuery dwsReinsuTradeQuery) {
		return dwsReinsuTradeMapper.selectDwsReinsuTradeGroupSummaryAsPrpAccount(dwsReinsuTradeQuery);
	}

	private void saveCedeoutBatchLog(String batchNo, String executor, String fileNames){
		CedeoutBatchLogDTO cedeoutBatchLog = new CedeoutBatchLogDTO();
		cedeoutBatchLog.setPassCount(0);
        cedeoutBatchLog.setFailCount(0);
        cedeoutBatchLog.setBatchNo(batchNo);
        cedeoutBatchLog.setExecutor(executor);
        cedeoutBatchLog.setCreateBy(executor);
        cedeoutBatchLog.setInputParams(fileNames);
        cedeoutBatchLog.setRemark("手工导入历史数据");
        cedeoutBatchLog.setCreateTime(DateUtils.getNowDate());
        cedeoutBatchLog.setExecuteDate(DateUtils.getNowDate());
		cedeoutBatchLog.setStatus(CedeoutEnums.状态_有效.getValue());
		cedeoutBatchLog.setCalcType(CedeoutEnums.执行方式_手动.getValue());
        cedeoutBatchLog.setProgress(CedeoutEnums.执行状态_执行中.getValue());
        cedeoutBatchLogService.insertCedeoutBatchLog(cedeoutBatchLog);
	}
	
	private void xlsFile2Table(String batchNo, String executor, List<String> dataFiles){
		try {
        	for(String file : dataFiles) {
        		log.info("导入历史分保数据处理中, 开始解析xls文件, batchNo:{}, file:{}", batchNo, file);
        		ImportDwsReinsuTradeService importDwsReinsuTradeService = new ImportDwsReinsuTradeService(batchNo, executor, DateUtils.getNowDate());
            	EasyExcel.read(FileUtil.getInputStream(file), ImportDwsReinsuTradeDTO.class, importDwsReinsuTradeService).sheet().doRead();
        	}
    	}catch(Exception e) {
    		log.error("导入历史分保数据解析入库出错, batchNo:{}, 错误原因:", batchNo, e);
    	}
	}
	
}
