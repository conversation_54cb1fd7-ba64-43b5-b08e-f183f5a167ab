package com.reinsurance.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.StarRocksConnector;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.DictUtils;
import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.dto.excel.DwsPrpContImportXlsDTO;
import com.reinsurance.enums.BasicDataEnums.ReportPushStatus;
import com.reinsurance.enums.BasicDataEnums.FileSuffix;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.listener.DwsPrpContReadListener;
import com.reinsurance.mapper.DwsPrpAccountMapper;
import com.reinsurance.service.IFileService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.FileUploadUtils;
import com.reinsurance.utils.ReinsuJsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.StringUtils;

import com.reinsurance.mapper.DwsPrpContMapper;
import com.reinsurance.dto.DwsPrpContDTO;
import com.reinsurance.query.DwsPrpContQuery;
import com.reinsurance.domain.DwsPrpContEntity;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.reinsurance.service.IDwsPrpContService;
import com.reinsurance.service.IDwsRegulatoryReportService;

import lombok.extern.slf4j.Slf4j;

/**
 * 再保首续期险种明细Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-30
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpContServiceImpl implements IDwsPrpContService {

    @Autowired
    private IFileService fileService;

    @Autowired
    private IRedisService redisService;

    @Autowired
    private DwsPrpContMapper dwsPrpContMapper;

    @Autowired
    private DwsPrpAccountMapper dwsPrpAccountMapper;

    @Autowired
    private StarRocksConnector starRocksConnector;

    @Autowired
    private IDwsRegulatoryReportService dwsRegulatoryReportService;

    /**
     * 查询再保首续期险种明细
     *
     * @param Id 再保首续期险种明细主键
     * @return 再保首续期险种明细
     */
    @Override
    public DwsPrpContDTO selectDwsPrpContById(Long Id) {
        DwsPrpContEntity entity = dwsPrpContMapper.selectDwsPrpContById(Id);
        return ReinsuObjectUtil.convertModel(entity, DwsPrpContDTO.class);
    }

    /**
     * 查询再保首续期险种明细列表
     *
     * @param dwsPrpContQuery 再保首续期险种明细
     * @return 再保首续期险种明细
     */
    @Override
    public List<DwsPrpContDTO> selectDwsPrpContList(DwsPrpContQuery dwsPrpContQuery) {
        List<DwsPrpContEntity> entityList = dwsPrpContMapper.selectDwsPrpContList(dwsPrpContQuery);
        return ReinsuObjectUtil.convertList(entityList, DwsPrpContDTO.class);
    }

    /**
     * 新增再保首续期险种明细
     *
     * @param dwsPrpContDTO 再保首续期险种明细
     * @return 结果
     */
    @Override
    public int insertDwsPrpCont(DwsPrpContDTO dwsPrpContDTO) {
        DwsPrpContEntity dwsPrpContEntity = ReinsuObjectUtil.convertModel(dwsPrpContDTO, DwsPrpContEntity.class);
        dwsPrpContEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpContMapper.insertDwsPrpCont(dwsPrpContEntity);
    }

    /**
     * 批量新增再保首续期险种明细
     *
     * @param dwsPrpContList 再保首续期险种明细列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpCont(List<DwsPrpContDTO> dwsPrpContList) {
        List<DwsPrpContEntity> entitys = ReinsuObjectUtil.convertList(dwsPrpContList, DwsPrpContEntity.class);
        return dwsPrpContMapper.insertBatchDwsPrpCont(entitys);
    }

    /**
     * 修改再保首续期险种明细
     *
     * @param dwsPrpContDTO 再保首续期险种明细
     * @return 结果
     */
    @Override
    public int updateDwsPrpCont(DwsPrpContDTO dwsPrpContDTO) {
        DwsPrpContEntity dwsPrpContEntity = ReinsuObjectUtil.convertModel(dwsPrpContDTO, DwsPrpContEntity.class);
        dwsPrpContEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpContMapper.updateDwsPrpCont(dwsPrpContEntity);
    }

    /**
     * 批量删除再保首续期险种明细
     *
     * @param Ids 需要删除的再保首续期险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpContByIds(Long[] Ids) {
        return dwsPrpContMapper.deleteDwsPrpContByIds(Ids);
    }

    /**
     * 删除再保首续期险种明细信息
     *
     * @param Id 再保首续期险种明细主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpContById(Long Id) {
        return dwsPrpContMapper.deleteDwsPrpContById(Id);
    }

    @Override
    public int insertDwsPrpContFormTrade(DwsPrpAccountDTO account) {
        return dwsPrpContMapper.insertDwsPrpContFormTrade(account);
    }

    @Override
    public int insertPreDwsPrpContFormTrade(DwsPrpAccountDTO account) {
        return dwsPrpContMapper.insertPreDwsPrpContFormTrade(account);
    }

    @Override
    public List<Long> selectWaitSetTransNoDwsPrpContListByAccTransNo(String accTransNo, int limit) {
        return dwsPrpContMapper.selectWaitSetTransNoDwsPrpContListByAccTransNo(accTransNo, limit);
    }

    @Override
    public int updateDwsPrpContTransactionNo(List<Map<String, Object>> contList) {
        int result = 0;
        try {
            String columnNames = "Id,TransactionNo,UpdateBy,UpdateTime";
            StarRocksOperationResult starRocksResult = starRocksConnector.update("t_dws_prp_cont", ReinsuJsonUtil.toJsonString(contList), columnNames);
            String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
            if(!("success").equalsIgnoreCase(status)) {//操作失败
                log.error("再保批量更新保单登记再保首续期险种明细交易编码失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
            }else {
                result = contList.size();
            }
            return result;
        } catch (Exception e) {
            log.error("再保批量更新保单登记再保首续期险种明细交易编码出错, 错误原因", e);
            return 0;
        }
    }

    /**
     * 导入再保首续期险种明细
     * @param accountId 所属账单Id
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpCont(Long accountId, MultipartFile file) {
        try {
            DwsPrpAccountEntity account = dwsPrpAccountMapper.selectDwsPrpAccountById(accountId);
            if(account == null) {
                return Result.error("账单信息不存在");
            }
            if(CedeoutEnums.导入状态_已导入.getValue() == account.getContImportStatus()) {
                return Result.error("账单已导入首续期险种明细，不允许重复导入。");
            }
            if(ReportPushStatus.已推送.getCode() == account.getPushStatus()) {
                return Result.error("账单已推送至报送平台，不允许导入首续期险种明细。");
            }
            String fileName = FileUploadUtils.upload(fileService.getStoragePath() + "prp/cont/", file);
            CompletableFuture.runAsync(() -> {
                DwsPrpContReadListener readListener = new DwsPrpContReadListener(account, redisService, starRocksConnector);
                EasyExcel.read(FileUtil.getInputStream(fileName), DwsPrpContImportXlsDTO.class, readListener).sheet().doRead();
            });
            return Result.success("文件上传成功, 数据后台导入中, 请稍后查询导入结果。", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记首续期险种明细出错, accountId:{}, 错误原因:", accountId, e);
            return Result.error("导入首续期险种明细失败（系统异常），请联系管理员");
        }
    }

    /**
     * 导出再保首续期险种明细
     *
     * @param response 响应对象
     * @param accountIds 所属账单Id集合
     */
    @Override
    public void exportDwsPrpCont(HttpServletResponse response, Long[] accountIds) {
        OutputStream os = null;
        final int pageSize = 10000;
        ExcelWriter excelWriter = null;
        try {
            if(accountIds == null || accountIds.length <= 0) {
                return;
            }
            List<DwsPrpAccountEntity> accountList = dwsPrpAccountMapper.selectDwsPrpAccountByIds(accountIds);
            if(CollUtil.isEmpty(accountList)) {
                log.info("再保导出保单登记首续期险种明细结束, 要导出的账单不存在, ids:{}", accountIds.toString());
                return;
            }
            Map<String, Object> headers = this.getHeaders();
            List<String> fieldList = (List<String>)headers.get("fieldList");
            List<List<String>> headerList = (List<List<String>>)headers.get("titleList");
            Map<String, Map<String, String>> fieldMapping = (Map<String, Map<String, String>>)headers.get("fieldMapping");
            String fileName = URLEncoder.encode("保单登记首续期险种明细" + FileSuffix.EXCEL.getSuffix(), "utf-8");

            os = response.getOutputStream();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            excelWriter = EasyExcel.write(os).registerWriteHandler(this.getCellStyle()).head(headerList).build();
            for(DwsPrpAccountEntity account : accountList) {
                int totalRows = dwsPrpContMapper.selectDwsPrpContCountByAccTransNo(account.getTransactionNo());
                if(totalRows <= 0) {
                    continue;
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(account.getTransactionNo()).build();

                int totalPage = (totalRows+pageSize-1)/pageSize;
                for(int pageNo=1; pageNo<=totalPage; pageNo++) {
                    int startRows = (pageNo - 1) * pageSize;
                    List<DwsPrpContEntity> contList = dwsPrpContMapper.selectDwsPrpContListByAccTransNo(account.getTransactionNo(), pageSize, startRows);
                    if(CollUtil.isNotEmpty(contList)) {
                        excelWriter.write(this.getDatas(fieldList, fieldMapping, contList), writeSheet);
                    }
                }
            }
        }catch(Exception e) {
            log.error("再保导出保单登记首续期险种明细出错, accountIds:{}, 错误原因:", accountIds.toString(), e);
        }finally {
            if(excelWriter != null) {
                excelWriter.finish();
            }
            if(os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {}
            }
        }
    }


    private HorizontalCellStyleStrategy getCellStyle(){
        HorizontalCellStyleStrategy handlerFontStyleStrategy = new HorizontalCellStyleStrategy();
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont writeFont = new WriteFont();
        writeFont.setFontHeightInPoints((short)12);
        headWriteCellStyle.setWriteFont(writeFont);
        handlerFontStyleStrategy.setHeadWriteCellStyle(headWriteCellStyle);
        return handlerFontStyleStrategy;
    }

    private Map<String, Object> getHeaders(){
        List<String> fieldList = new ArrayList<String>();
        List<List<String>> titleList = new ArrayList<List<String>>();
        Map<String, Map<String, String>> fieldMapping = MapUtil.newHashMap();
        Field[] fields = ReflectUtil.getFields(DwsPrpContDTO.class);
        for(Field field : fields) {
            Excel excel = field.getAnnotation(Excel.class);
            if(excel != null && (Excel.Type.ALL == excel.type() || Excel.Type.EXPORT == excel.type())) {
                fieldList.add(field.getName());
                titleList.add(Arrays.asList(excel.name()));
                if(StringUtils.isNotBlank(excel.readConverterExp())) {
                    fieldMapping.put(field.getName(),
                            Stream.of(excel.readConverterExp().split(",")).map(pair -> pair.split("="))
                                    .collect(Collectors.toMap(data -> StringUtils.trim(data[0]),
                                            data -> StringUtils.trim(data[1]))));
                }
                if(StringUtils.isNotBlank(excel.dictType())) {
                    List<SysDictData> dictList = DictUtils.getDictCache(excel.dictType());
                    if(CollUtil.isNotEmpty(dictList)) {
                        fieldMapping.put(field.getName(), dictList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel)));
                    }
                }
            }
        }
        return ImmutableMap.of("titleList", titleList, "fieldList", fieldList, "fieldMapping", fieldMapping);
    }

    private List<List<Object>> getDatas(List<String> fieldList, Map<String, Map<String, String>> fieldMapping, List<DwsPrpContEntity> contList) {
        List<List<Object>> datas = new ArrayList<List<Object>>();
        for(DwsPrpContEntity cont : contList) {
            List<Object> rowDatas = Lists.newArrayList();
            for(String field : fieldList) {
                Object dataValue = BeanUtil.getFieldValue(cont, field);
                if(dataValue != null && dataValue instanceof Date) {
                    rowDatas.add(DateUtil.formatDate((Date)dataValue));
                }else {
                    if(fieldMapping.containsKey(field)) {
                        String dataLabel = fieldMapping.get(field).get(String.valueOf(dataValue));
                        rowDatas.add(StringUtils.isNotEmpty(dataLabel) ? dataLabel : dataValue);
                    }else {
                        rowDatas.add(dataValue);
                    }
                }
            }
            datas.add(rowDatas);
        }
        return datas;
    }
}
