package com.reinsurance.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.constant.HttpStatus;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.enums.BusinessType;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.common.utils.SecurityUtils;
import com.jd.lightning.common.utils.StringUtils;

import com.reinsurance.dto.*;
import com.reinsurance.query.*;
import com.reinsurance.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.reinsurance.mapper.DwsRegulatoryReportMapper;

import com.reinsurance.enums.BasicDataEnums.*;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsRegulatoryReportEntity;
import com.reinsurance.utils.ReDateUtil;
import com.reinsurance.utils.ReinsuJsonUtil;
import com.reinsurance.utils.ReinsuObjectUtil;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 监管报表信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsRegulatoryReportServiceImpl implements IDwsRegulatoryReportService {
	
	@Autowired
	private IRedisService redisService;
	
	@Autowired
	private ISysDictExtService sysDictExtService;
	
	@Autowired
    private ISysConfigExtService sysConfigExtService;
	
	@Autowired
    private ICedeoutContractService cedeoutContractService;
	
	@Autowired
    private IDwsEastZbhtxxbService dwsEastZbhtxxbService;
	
	@Autowired
    private IDwsEastZbcpxxbService dwsEastZbcpxxbService;
	
	@Autowired
    private IDwsEastZbzdxxbService dwsEastZbzdxxbService;
	
	@Autowired
    private IDwsEastBlzbbdmxbService dwsEastBlzbbdmxbService;
	
    @Autowired
    private DwsRegulatoryReportMapper dwsRegulatoryReportMapper;
    
    @Autowired
    private IDwsReinsuSettleBillService dwsReinsuSettleBillService;
    
    @Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;

	@Autowired
	private IDwsPrpProductService dwsPrpProductService;

	@Autowired
	private IDwsPrpInsureContService dwsPrpInsureContService;

	@Autowired
	private IDwsPrpAccountService dwsPrpAccountService;

	@Autowired
	private IDwsPrpContService dwsPrpContService;

	@Autowired
	private IDwsPrpEdorService dwsPrpEdorService;

	@Autowired
	private IDwsPrpClaimService dwsPrpClaimService;

	@Autowired
	private IDwsPrpBenefitService dwsPrpBenefitService;

    
    /**
     * 查询监管报表信息列表
     *
     * @param dwsRegulatoryReportQuery 监管报表信息
     * @return 监管报表信息
     */
    @Override
    public List<DwsRegulatoryReportDTO> selectDwsRegulatoryReportList(DwsRegulatoryReportQuery dwsRegulatoryReportQuery) {
        List<DwsRegulatoryReportEntity> list = dwsRegulatoryReportMapper.selectDwsRegulatoryReportList(dwsRegulatoryReportQuery);
        return ReinsuObjectUtil.convertList(list, DwsRegulatoryReportDTO.class);
    }

	@Override
	public Result checkReportExists(DwsRegulatoryReportDTO dwsRegulatoryReportDTO) {
		String errorMsg = this.checkRegulatoryReport(dwsRegulatoryReportDTO);
		if(StringUtils.isNotBlank(errorMsg)) {
			return Result.error(errorMsg);
		}
		int result = 0;
		if(RegulatorReport.保单登记数据报送.getCode() == dwsRegulatoryReportDTO.getTypeCode()){
			if(PrpReport.LRInsureCont.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsPrpInsureContQuery dwsPrpInsureContQuery = new DwsPrpInsureContQuery();
				dwsPrpInsureContQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsPrpInsureContQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				result = dwsPrpInsureContService.selectDwsPrpInsureContExists(dwsPrpInsureContQuery);
			}else if(PrpReport.LRProduct.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsPrpProductQuery dwsPrpProductQuery = new DwsPrpProductQuery();
				dwsPrpProductQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsPrpProductQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				result = dwsPrpProductService.selectDwsPrpProductExists(dwsPrpProductQuery);
			}else if(PrpReport.LRAccount.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsPrpAccountQuery dwsPrpAccountQuery = new DwsPrpAccountQuery();
				dwsPrpAccountQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsPrpAccountQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				result = dwsPrpAccountService.selectDwsPrpAccountExists(dwsPrpAccountQuery);
			}
		}else if(RegulatorReport.East数据报送.getCode() == dwsRegulatoryReportDTO.getTypeCode()){
			if(EastReport.再保合同信息表.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsEastZbhtxxbQuery dwsEastZbhtxxbQuery = new DwsEastZbhtxxbQuery();
				dwsEastZbhtxxbQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsEastZbhtxxbQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				result = dwsEastZbhtxxbService.selectDwsEastZbhtxxbExists(dwsEastZbhtxxbQuery);
			}else if(EastReport.再保产品信息表.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsEastZbcpxxbQuery dwsEastZbcpxxbQuery = new DwsEastZbcpxxbQuery();
				dwsEastZbcpxxbQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsEastZbcpxxbQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				result = dwsEastZbcpxxbService.selectDwsEastZbcpxxbExists(dwsEastZbcpxxbQuery);
			}else if(EastReport.再保账单信息表.getCode() == dwsRegulatoryReportDTO.getReportCode()) {
				DwsEastZbzdxxbQuery dwsEastZbzdxxbQuery = new DwsEastZbzdxxbQuery();
				dwsEastZbzdxxbQuery.setReportYear(dwsRegulatoryReportDTO.getReportYear());
				dwsEastZbzdxxbQuery.setReportMonth(dwsRegulatoryReportDTO.getReportMonth());
				dwsEastZbzdxxbQuery.setYtzdbz(String.valueOf(dwsRegulatoryReportDTO.getBillType()));
				result = dwsEastZbzdxxbService.selectDwsEastZbzdxxbExists(dwsEastZbzdxxbQuery);
			}
		}
		return Result.success(result);
	}

	@Override
	public Result insertRegulatoryReportData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		Result result = null;
		try {
			String errorMsg = this.checkRegulatoryReport(regulatoryReportDTO);
			if(StringUtils.isNotBlank(errorMsg)) {
				return Result.error(errorMsg);
			}
			boolean isInsertFlag = false;
			DwsRegulatoryReportQuery regulatoryReportQuery = ReinsuObjectUtil.convertModel(regulatoryReportDTO, DwsRegulatoryReportQuery.class);
			DwsRegulatoryReportEntity regulatoryReportEntity = dwsRegulatoryReportMapper.selectDwsRegulatoryReport(regulatoryReportQuery);
			if(regulatoryReportEntity == null) {
				isInsertFlag = true;
				regulatoryReportEntity = ReinsuObjectUtil.convertModel(regulatoryReportDTO, DwsRegulatoryReportEntity.class);
				regulatoryReportEntity.setCreateBy(SecurityUtils.getUsername());
				regulatoryReportEntity.setUpdateBy(SecurityUtils.getUsername());
				dwsRegulatoryReportMapper.insertDwsRegulatoryReport(regulatoryReportEntity);
			}
			if(RegulatorReport.保单登记数据报送.getCode() == regulatoryReportDTO.getTypeCode()){
				if(PrpReport.LRInsureCont.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertPrpInsureContData(regulatoryReportDTO);
				}else if(PrpReport.LRProduct.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertPrpProductData(regulatoryReportDTO);
				}else if(PrpReport.LRAccount.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertPrpAccountData(regulatoryReportDTO);
				}
			}else if(RegulatorReport.East数据报送.getCode() == regulatoryReportDTO.getTypeCode()){
				if(EastReport.再保合同信息表.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertEastContractData(regulatoryReportDTO);
				}else if(EastReport.再保产品信息表.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertEastProductData(regulatoryReportDTO);
				}else if(EastReport.再保账单信息表.getCode() == regulatoryReportDTO.getReportCode()) {
					result = this.insertEastBillData(regulatoryReportDTO);
				}
			}

			boolean updatePushStatus = ReportPushStatus.全部推送.getCode() == regulatoryReportEntity.getPushStatus();
			boolean isSuccess = String.valueOf(HttpStatus.SUCCESS).equals(String.valueOf(result.get(Result.CODE_TAG)));
			if(!isInsertFlag && isSuccess && updatePushStatus) {//新增数据、报表数据成功、且推送状态为全部
				regulatoryReportEntity.setUpdateTime(DateUtil.date());
				regulatoryReportEntity.setUpdateBy(SecurityUtils.getUsername());
				regulatoryReportEntity.setPushStatus(ReportPushStatus.部分推送.getCode());
				dwsRegulatoryReportMapper.updateDwsRegulatoryReport(regulatoryReportEntity);
			}
			return result;
		}catch(Exception e) {
			log.error("再保生成监管报表数据异常, regulatoryReport:{}, 原因:", ReinsuJsonUtil.toJsonString(regulatoryReportDTO), e);
			return Result.error("生成报表数据异常，请联系管理员。");
		}
	}
	
	@Override
	public int insertOrUpdateRegulatoryReport(BusinessType businessType, RegulatorReport regulatorReport, int reportCode, List<Integer> reportYears) {
		int result = 0;
		String reportName = "";
		try {
			if(RegulatorReport.保单登记数据报送.getCode() == regulatorReport.getCode()){
				reportName = PrpReport.check(reportCode);
			}else if(RegulatorReport.East数据报送.getCode() == regulatorReport.getCode()){
				reportName = EastReport.check(reportCode);
			}
			if(StringUtils.isEmpty(reportName)){
				return  result;
			}
			DwsRegulatoryReportQuery regulatoryReportQuery = new DwsRegulatoryReportQuery();
			regulatoryReportQuery.setReportCode(reportCode);
	    	regulatoryReportQuery.setTypeCode(regulatorReport.getCode());
	    	for(int reportYear : reportYears) {
	    		regulatoryReportQuery.setReportYear(reportYear);
	    		DwsRegulatoryReportEntity regulatoryReportEntity = dwsRegulatoryReportMapper.selectDwsRegulatoryReport(regulatoryReportQuery);
	    		if(regulatoryReportEntity == null) {
	    			regulatoryReportEntity = ReinsuObjectUtil.convertModel(regulatoryReportQuery, DwsRegulatoryReportEntity.class);
	    			regulatoryReportEntity.setReportName(reportName);
	    			regulatoryReportEntity.setCreateBy(SecurityUtils.getUsername());
					regulatoryReportEntity.setUpdateBy(SecurityUtils.getUsername());
	    			regulatoryReportEntity.setPushStatus(ReportPushStatus.未推送.getCode());
	    			regulatoryReportEntity.setTypeName(RegulatorReport.East数据报送.getDesc());
					int insertRows = dwsRegulatoryReportMapper.insertDwsRegulatoryReport(regulatoryReportEntity);
					result += insertRows;
	    		}else {
	    			Integer pushStatus = null;
	    			if(BusinessType.IMPORT.equals(businessType)) {//导入
	    				if(ReportPushStatus.全部推送.getCode() == regulatoryReportEntity.getPushStatus()) {//原来有全部推送
	    					pushStatus = ReportPushStatus.部分推送.getCode();
		    			}
	    			}else if(BusinessType.DELETE.equals(businessType) || BusinessType.UPDATE.equals(businessType)) {//删除、推送
	    				pushStatus = regulatoryReportEntity.getPushStatus();
						if(RegulatorReport.保单登记数据报送.getCode() == regulatorReport.getCode()){
							if(PrpReport.LRInsureCont.getCode() == reportCode) {
								pushStatus = dwsPrpInsureContService.selectAnnualReportShouldPushStatus(reportYear);
							}else if(PrpReport.LRProduct.getCode() == reportCode) {
								pushStatus = dwsPrpProductService.selectAnnualReportShouldPushStatus(reportYear);
							}else if(PrpReport.LRAccount.getCode() == reportCode) {
								pushStatus = dwsPrpAccountService.selectAnnualReportShouldPushStatus(reportYear);
							}
						}else if(RegulatorReport.East数据报送.getCode() == regulatorReport.getCode()){
							if(EastReport.再保合同信息表.getCode() == reportCode) {
								pushStatus = dwsEastZbhtxxbService.selectAnnualReportShouldPushStatus(reportYear);
							}else if(EastReport.再保产品信息表.getCode() == reportCode) {
								pushStatus = dwsEastZbcpxxbService.selectAnnualReportShouldPushStatus(reportYear);
							}else if(EastReport.再保账单信息表.getCode() == reportCode) {
								pushStatus = dwsEastZbzdxxbService.selectAnnualReportShouldPushStatus(reportYear);
							}
						}
	    			}
	    			if(pushStatus != null) {
	    				regulatoryReportEntity.setPushStatus(pushStatus);
	    				regulatoryReportEntity.setUpdateTime(DateUtil.date());
	    				regulatoryReportEntity.setUpdateBy(SecurityUtils.getUsername());
	    				int updateRows = dwsRegulatoryReportMapper.updateDwsRegulatoryReport(regulatoryReportEntity);
	    				result += updateRows;
	    			}
	    		}
	    	}
		}catch(Exception e) {
			log.error("再保操作监管报表成功，修改监管年度信息出错, reportName:{}, 错误原因", reportName, e);
		}
		return result;
	}

	private String checkRegulatoryReport(DwsRegulatoryReportDTO regulatoryReportDTO) {
		if(regulatoryReportDTO == null) {
			return "生成报表数据参数为空";
		}
		if(regulatoryReportDTO.getReportYear() == null) {
			return "请选择报表年份";
		}
		if(regulatoryReportDTO.getReportMonth() == null) {
			return "请选择报表月份";
		}
		String typeName = RegulatorReport.check(regulatoryReportDTO.getTypeCode());
		if(StringUtils.isBlank(typeName)) {
			return "请选择监管报表类型";
		}
		if(RegulatorReport.保单登记数据报送.getCode() != regulatoryReportDTO.getTypeCode() && RegulatorReport.East数据报送.getCode() != regulatoryReportDTO.getTypeCode()) {
			return StringUtils.trimToEmpty(typeName) + "功能待开发";
		}
		String reportName = "";
		if(RegulatorReport.保单登记数据报送.getCode() == regulatoryReportDTO.getTypeCode()){
			reportName = PrpReport.check(regulatoryReportDTO.getReportCode());
			if(StringUtils.isBlank(reportName)) {
				return "请选择报表名称";
			}
			if(PrpReport.LRAccount.getCode() == regulatoryReportDTO.getReportCode()) {
				if(StringUtils.isBlank(PrpBillType.getName(String.valueOf(regulatoryReportDTO.getBillType())))) {
					return "请选择账单类型（预提/实际）";
				}
			}
		}else if(RegulatorReport.East数据报送.getCode() == regulatoryReportDTO.getTypeCode()){
			reportName = EastReport.check(regulatoryReportDTO.getReportCode());
			if(StringUtils.isBlank(reportName)) {
				return "请选择报表名称";
			}
			if(EastReport.再保账单信息表.getCode() == regulatoryReportDTO.getReportCode()) {
				if(StringUtils.isBlank(EastBillType.getName(regulatoryReportDTO.getBillType()))) {
					return "请选择账单类型（预提/实际）";
				}
			}
		}
		regulatoryReportDTO.setTypeName(typeName);
		regulatoryReportDTO.setReportName(reportName);
		return null;
	}
	
	/**
	 * 生成East再保合同信息
	 * @param regulatoryReportDTO
	 * @return
	 */
	private Result insertEastContractData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		int reportYear = regulatoryReportDTO.getReportYear();
		int reportMonth = regulatoryReportDTO.getReportMonth();
		
		Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
		Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();
		CedeoutContractQuery cedeoutContractQuery = new CedeoutContractQuery();
		cedeoutContractQuery.setStatus(CedeoutEnums.状态_有效.getValue());
		cedeoutContractQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
		cedeoutContractQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
		List<DwsEastZbhtxxbDTO> zbhtxxbList = cedeoutContractService.selectOriginalContractAsEastZbhtxxb(cedeoutContractQuery);
		if(CollUtil.isEmpty(zbhtxxbList)) {
			return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无合同数据。", 0);
		}
		String manageCom = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsManageCom);
		String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		String companyName = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyName);
		if(StringUtils.isBlank(companyCode) || StringUtils.isBlank(companyName)) {
			return Result.error("系统参数中未配置保险机构代码，请联系管理员");
		}
		Map<String, String> contractTypeMap = sysDictExtService.selectDictDataMap(RsConstant.reContractType);
		if(MapUtil.isEmpty(contractTypeMap)) {
			return Result.error("系统中未配置“合同附约类型”字典，请联系管理员");
		}
		
		Map<String, String> contractStatusMap = sysDictExtService.selectDictDataMap(RsConstant.reContractStatus);
		if(MapUtil.isEmpty(contractTypeMap)) {
			return Result.error("系统中未配置“合同状态”字典，请联系管理员");
		}
		
		List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, companyCode, zbhtxxbList.size());
		if(CollUtil.isEmpty(serialNos) || zbhtxxbList.size() != serialNos.size()) {
			return Result.error("生成流水号失败，请联系管理员");
		}
		
		int index = 0;
		String sjbspch = ReDateUtil.format(lastDayOfMonth, DatePattern.PURE_DATE_PATTERN);
		String gatherDate = ReDateUtil.format(ReDateUtil.endOfMonth(ReDateUtil.lastMonth()), DatePattern.PURE_DATE_PATTERN);
		for(DwsEastZbhtxxbDTO zbhtxxb : zbhtxxbList) {
			zbhtxxb.setCjrq(gatherDate);
			zbhtxxb.setBxjgdm(companyCode);
			zbhtxxb.setBxjgmc(companyName);
			zbhtxxb.setYbxgsdm(companyCode);
			zbhtxxb.setYbxgsmc(companyName);
			zbhtxxb.setLsh(serialNos.get(index));
			zbhtxxb.setHtlx(EastContractType.比例合同.getDesc());
			zbhtxxb.setHtfl(EastContractType.分出合同.getDesc());
			zbhtxxb.setJzzbhtbz(String.valueOf(EastReportEitherFlag.否.getCode()));
			zbhtxxb.setHtzt(contractStatusMap.get(zbhtxxb.getHtzt()));
			zbhtxxb.setHtfylx(contractTypeMap.get(zbhtxxb.getHtfylx()));
			
			zbhtxxb.setSjbspch(sjbspch);
			zbhtxxb.setManagecom(manageCom);
			
			zbhtxxb.setReportYear(reportYear);
			zbhtxxb.setReportMonth(reportMonth);
			zbhtxxb.setCreateTime(DateUtils.getNowDate());
        	zbhtxxb.setUpdateTime(DateUtils.getNowDate());
			zbhtxxb.setCreateBy(SecurityUtils.getUsername());
			zbhtxxb.setUpdateBy(SecurityUtils.getUsername());
			zbhtxxb.setDataSource(ReportDataSource.系统.getCode());
			zbhtxxb.setPushStatus(ReportPushStatus.未推送.getCode());
			zbhtxxb.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
			index++;
		}
		int insertRows = dwsEastZbhtxxbService.insertBatchDwsEastZbhtxxb(zbhtxxbList);
		return Result.success("生成报表数据成功", insertRows);
	}
	
	/**
	 * 生成East再保产品信息
	 * @param regulatoryReportDTO
	 * @return
	 */
	private Result insertEastProductData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		int reportYear = regulatoryReportDTO.getReportYear();
		int reportMonth = regulatoryReportDTO.getReportMonth();
		
		Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
		Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();
		
		CedeoutContractLiabilityQuery contractLiabilityQuery = new CedeoutContractLiabilityQuery();
		contractLiabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
		contractLiabilityQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
		contractLiabilityQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
		List<DwsEastZbcpxxbDTO> zbcpxxbList = cedeoutContractService.selectContractLiabilityAsEastZbcpxxb(contractLiabilityQuery);
		if(CollUtil.isEmpty(zbcpxxbList)) {
			return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无产品数据。", 0);
		}
		String manageCom = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsManageCom);
		String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		String companyName = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyName);
		if(StringUtils.isBlank(companyCode) || StringUtils.isBlank(companyName)) {
			return Result.error("系统参数中未配置保险机构代码，请联系管理员");
		}
		Map<String, String> contractTypeMap = sysDictExtService.selectDictDataMap(RsConstant.reContractType);
		if(MapUtil.isEmpty(contractTypeMap)) {
			return Result.error("系统中未配置“合同附约类型”字典，请联系管理员");
		}
		
		List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, companyCode, zbcpxxbList.size());
		if(CollUtil.isEmpty(serialNos) || zbcpxxbList.size() != serialNos.size()) {
			return Result.error("生成流水号失败，请联系管理员");
		}
		
		int index = 0;
		String sjbspch = ReDateUtil.format(lastDayOfMonth, DatePattern.PURE_DATE_PATTERN);
		String gatherDate = ReDateUtil.format(ReDateUtil.endOfMonth(ReDateUtil.lastMonth()), DatePattern.PURE_DATE_PATTERN);
		for(DwsEastZbcpxxbDTO zbcpxxb : zbcpxxbList) {
			zbcpxxb.setCjrq(gatherDate);
			zbcpxxb.setBxjgdm(companyCode);
			zbcpxxb.setBxjgmc(companyName);
			zbcpxxb.setLsh(serialNos.get(index));
			zbcpxxb.setHtfylx(contractTypeMap.get(zbcpxxb.getHtfylx()));
			zbcpxxb.setTgxz(EastContType.getDescByCode(Integer.valueOf(zbcpxxb.getTgxz())));
			if(String.valueOf(EastCedeoutWay.成数.getCode()).equals(zbcpxxb.getFbfs()) || 
				String.valueOf(EastCedeoutWay.共保.getCode()).equals(zbcpxxb.getFbfs())) {
				zbcpxxb.setZle(String.valueOf(BigDecimal.ONE.negate().intValue()));
			}
			if(String.valueOf(EastCedeoutWay.溢额.getCode()).equals(zbcpxxb.getFbfs())) {
				zbcpxxb.setZlbl(String.valueOf(BigDecimal.ONE.negate().intValue()));
				zbcpxxb.setFbbl(String.valueOf(BigDecimal.ONE.negate().intValue()));
			}
			zbcpxxb.setFbxh(String.valueOf(BigDecimal.ONE.intValue()));
			zbcpxxb.setFbfs(EastCedeoutWay.getDescByCode(Integer.valueOf(zbcpxxb.getFbfs())));
			
			zbcpxxb.setSjbspch(sjbspch);
			zbcpxxb.setManagecom(manageCom);
			
			zbcpxxb.setReportYear(reportYear);
			zbcpxxb.setReportMonth(reportMonth);
			zbcpxxb.setCreateTime(DateUtils.getNowDate());
			zbcpxxb.setUpdateTime(DateUtils.getNowDate());
			zbcpxxb.setCreateBy(SecurityUtils.getUsername());
			zbcpxxb.setUpdateBy(SecurityUtils.getUsername());
			zbcpxxb.setDataSource(ReportDataSource.系统.getCode());
			zbcpxxb.setPushStatus(ReportPushStatus.未推送.getCode());
			zbcpxxb.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
			index++;
		}
		int insertRows = dwsEastZbcpxxbService.insertBatchDwsEastZbcpxxb(zbcpxxbList);
		return Result.success("生成报表数据成功", insertRows);
	}
	
	/**
	 * 生成East再保账单信息
	 * @param regulatoryReportDTO
	 * @return
	 */
	public Result insertEastBillData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		String billTypeName = EastBillType.getName(regulatoryReportDTO.getBillType());
		if(StringUtils.isBlank(billTypeName)) {
			return Result.error("账单类型只允许为预提/实际。");
		}
		int reportYear = regulatoryReportDTO.getReportYear();
		int reportMonth = regulatoryReportDTO.getReportMonth();
		Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
		Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();
		
		String manageCom = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsManageCom);
		String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		String companyName = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyName);
		if(StringUtils.isBlank(companyCode) || StringUtils.isBlank(companyName)) {
			return Result.error("系统参数中未配置保险机构代码，请联系管理员");
		}
		String mysqlBaseName = sysConfigExtService.selectConfigByKey(RsConstant.mysqlBaseName);
		String dataWarehouseName = sysConfigExtService.selectConfigByKey(RsConstant.dataWarehouseName);
		int batchLimit = Integer.valueOf(sysConfigExtService.selectConfigByKey(RsConstant.jobLimitRows));
		
		List<DwsEastZbzdxxbDTO> zbzdxxbList = null;
		String sjbspch = ReDateUtil.format(lastDayOfMonth, DatePattern.PURE_DATE_PATTERN);
		String gatherDate = ReDateUtil.format(ReDateUtil.endOfMonth(ReDateUtil.lastMonth()), DatePattern.PURE_DATE_PATTERN);
		if(EastBillType.预提.getCode() == regulatoryReportDTO.getBillType()) {//预提
			Date startDate = ReDateUtil.offsetDay(firstDayOfMonth, 1).toJdkDate();
			Date endDate = ReDateUtil.offsetDay(lastDayOfMonth, 1).toJdkDate();
			DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
			dwsReinsuTradeQuery.getParams().put("mysqlBaseName", mysqlBaseName);
			dwsReinsuTradeQuery.getParams().put(RsConstant.paramsStartDate, startDate);
			dwsReinsuTradeQuery.getParams().put(RsConstant.paramsEndDate, endDate);
			zbzdxxbList = dwsReinsuTradeService.selectDwsReinsuTradeGroupSummaryAsZbzdxxb(dwsReinsuTradeQuery);
			if(CollUtil.isEmpty(zbzdxxbList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无预提账单数据。", 0);
			}
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, companyCode, zbzdxxbList.size());
			if(CollUtil.isEmpty(serialNos) || zbzdxxbList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			int index = 0;
			String zdqq = ReDateUtil.format(firstDayOfMonth, DatePattern.PURE_DATE_PATTERN);
			String zdzq = ReDateUtil.format(lastDayOfMonth, DatePattern.PURE_DATE_PATTERN);
			for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {
				zbzdxxb.setZdqq(zdqq);
				zbzdxxb.setZdzq(zdzq);
				zbzdxxb.setCjrq(gatherDate);
				zbzdxxb.setBxjgdm(companyCode);
				zbzdxxb.setBxjgmc(companyName);
				zbzdxxb.setYbxgsdm(companyCode);
				zbzdxxb.setYbxgsmc(companyName);
				zbzdxxb.setLsh(serialNos.get(index));
				zbzdxxb.setZdfl(EastZdfl.分出账单.getDesc());
				zbzdxxb.setThtbj(BigDecimal.ZERO);//摊回退保金
				zbzdxxb.setThscj(BigDecimal.ZERO);//摊回生存金
				zbzdxxb.setJszt(ConfirmStatus.未确认.getSettle());
				zbzdxxb.setYtzdbz(String.valueOf(regulatoryReportDTO.getBillType()));
				zbzdxxb.setZdbh(zbzdxxb.getZbxhthm() + reportYear + "Q" + DateUtil.quarter(firstDayOfMonth)); //再保合同号+年度+季度
				
				zbzdxxb.setSjbspch(sjbspch);
				zbzdxxb.setManagecom(manageCom);
				
				zbzdxxb.setReportYear(reportYear);
				zbzdxxb.setReportMonth(reportMonth);
				zbzdxxb.setCreateTime(DateUtils.getNowDate());
				zbzdxxb.setUpdateTime(DateUtils.getNowDate());
				zbzdxxb.setCreateBy(SecurityUtils.getUsername());
				zbzdxxb.setUpdateBy(SecurityUtils.getUsername());
				zbzdxxb.setDataSource(ReportDataSource.系统.getCode());
				zbzdxxb.setPushStatus(ReportPushStatus.未推送.getCode());
				zbzdxxb.setImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				zbzdxxb.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
				
				zbzdxxb.getParams().put("mysqlBaseName", mysqlBaseName);
				zbzdxxb.getParams().put("dataWarehouseName", dataWarehouseName);
				zbzdxxb.getParams().put(RsConstant.paramsStartDate, startDate);
				zbzdxxb.getParams().put(RsConstant.paramsEndDate, endDate);
				index++;
			}
		}else {//实际
			DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery = new DwsReinsuSettleBillQuery();
			dwsReinsuSettleBillQuery.getParams().put("mysqlBaseName", mysqlBaseName);
			dwsReinsuSettleBillQuery.setConfirmStatus(ConfirmStatus.已确认.getCode());
			dwsReinsuSettleBillQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
			dwsReinsuSettleBillQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
			zbzdxxbList = dwsReinsuSettleBillService.selectSettleBillAsZbzdxxb(dwsReinsuSettleBillQuery);
			if(CollUtil.isEmpty(zbzdxxbList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无实际账单数据。", 0);
			}
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.EAST, companyCode, zbzdxxbList.size());
			if(CollUtil.isEmpty(serialNos) || zbzdxxbList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			int index = 0;
			List<String> billNos = zbzdxxbList.stream().map(DwsEastZbzdxxbDTO::getSettleBillNo).collect(Collectors.toList());
			Map<String, DwsEastZbzdxxbDTO> zbzdxxbMoneyMap = dwsReinsuTradeService.selectDwsReinsuTradeMoneyByBillNos(billNos);
			for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {
				DwsEastZbzdxxbDTO zbzdxxbMoney = zbzdxxbMoneyMap.get(zbzdxxb.getSettleBillNo());
				zbzdxxb.setCjrq(gatherDate);
				zbzdxxb.setBxjgdm(companyCode);
				zbzdxxb.setBxjgmc(companyName);
				zbzdxxb.setYbxgsdm(companyCode);
				zbzdxxb.setYbxgsmc(companyName);
				zbzdxxb.setLsh(serialNos.get(index));
				zbzdxxb.setZdfl(EastZdfl.分出账单.getDesc());
				zbzdxxb.setThtbj(BigDecimal.ZERO);//摊回退保金
				zbzdxxb.setThscj(BigDecimal.ZERO);//摊回生存金
				zbzdxxb.setJszt(ConfirmStatus.已确认.getSettle());
				zbzdxxb.setYtzdbz(String.valueOf(regulatoryReportDTO.getBillType()));
				zbzdxxb.setFbf(zbzdxxbMoney != null ? zbzdxxbMoney.getFbf() : BigDecimal.ZERO);//分保费
				zbzdxxb.setFbyj(zbzdxxbMoney != null ? zbzdxxbMoney.getFbyj() : BigDecimal.ZERO);//分保佣金
				zbzdxxb.setThfbf(zbzdxxbMoney != null ? zbzdxxbMoney.getThfbf() : BigDecimal.ZERO);//退回分保费
				zbzdxxb.setThfbyj(zbzdxxbMoney != null ? zbzdxxbMoney.getThfbyj() : BigDecimal.ZERO);//退回分保佣金
				zbzdxxb.setThlpk(zbzdxxbMoney != null ? zbzdxxbMoney.getThlpk() : BigDecimal.ZERO);//摊回理赔款
				zbzdxxb.setThmqj(zbzdxxbMoney != null ? zbzdxxbMoney.getThmqj() : BigDecimal.ZERO);//摊回满期金
				zbzdxxb.setZdbh(zbzdxxb.getZbxhthm() + reportYear + "Q" + DateUtil.quarter(firstDayOfMonth)); //再保合同号+年度+季度
				
				zbzdxxb.setSjbspch(sjbspch);
				zbzdxxb.setManagecom(manageCom);
				
				zbzdxxb.setReportYear(reportYear);
				zbzdxxb.setReportMonth(reportMonth);
				zbzdxxb.setCreateTime(DateUtils.getNowDate());
				zbzdxxb.setUpdateTime(DateUtils.getNowDate());
				zbzdxxb.setCreateBy(SecurityUtils.getUsername());
				zbzdxxb.setUpdateBy(SecurityUtils.getUsername());
				zbzdxxb.setDataSource(ReportDataSource.系统.getCode());
				zbzdxxb.setPushStatus(ReportPushStatus.未推送.getCode());
				zbzdxxb.setImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				zbzdxxb.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
				
				zbzdxxb.setTotalRows(zbzdxxbMoney.getTotalRows());
				zbzdxxb.getParams().put("mysqlBaseName", mysqlBaseName);
				zbzdxxb.getParams().put("dataWarehouseName", dataWarehouseName);
				index++;
			}
		}
		int insertRows = dwsEastZbzdxxbService.insertBatchDwsEastZbzdxxb(zbzdxxbList);
		final List<DwsEastZbzdxxbDTO> insertZbzdxxbList = ReinsuObjectUtil.convertList(zbzdxxbList, DwsEastZbzdxxbDTO.class);
		CompletableFuture.runAsync(() -> insertBlzbbdmxb(insertZbzdxxbList, batchLimit));//插入比例再保保单明细，更新流水号
		return Result.success("生成报表数据成功", insertRows);
	}
	
	/**
	 * 保存East比例再保保单明细，更新比例再保保单明细流水号
	 * @param zbzdxxbList
	 * @param batchLimit
	 */
	private void insertBlzbbdmxb(List<DwsEastZbzdxxbDTO> zbzdxxbList, int batchLimit) {
		for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {//插入
			try {
				int insertRows = 0;
				if(String.valueOf(EastBillType.实际.getCode()).equals(zbzdxxb.getYtzdbz())) {
					insertRows = dwsEastBlzbbdmxbService.insertDwsEastBlzbbdmxbFormTrade(zbzdxxb);
				}else {
					insertRows = dwsEastBlzbbdmxbService.insertDwsEastPreBlzbbdmxbFormTrade(zbzdxxb);
				}
				zbzdxxb.setRemark("totalRows:" + zbzdxxb.getTotalRows() + "; insertRows:" + insertRows);
				log.info("再保East比例再保保单明细完成, lsh:{}, totalRows:{}, insertRows:{}", zbzdxxb.getLsh(), zbzdxxb.getTotalRows(), insertRows);
			}catch(Exception e) {
				zbzdxxb.setRemark("插入比例再保保单明细异常");
				log.error("再保East比例再保保单明细插入出错, lsh:{}, 错误原因:", zbzdxxb.getLsh(), e);
			}
		}
		for(DwsEastZbzdxxbDTO zbzdxxb : zbzdxxbList) {//更新流水号
			Integer importStatus = zbzdxxb.getImportStatus();
			try {
				String remark = "";
				log.info("再保East比例再保保单明细更新流水号开始, lsh:{}", zbzdxxb.getLsh());
				while(true) {
					List<Long> ids = dwsEastBlzbbdmxbService.selectWaitSetLshBlzbbdmxbListByBillLsh(zbzdxxb.getLsh(), batchLimit);
					if(CollUtil.isEmpty(ids)) {
						remark = "未匹配到比例再保保单明细";
						break;
					}
					List<String> lshs = redisService.getUniqueCodes(RedisKeyModule.EAST, zbzdxxb.getBxjgdm(), ids.size());
					if(CollUtil.isEmpty(lshs) || ids.size() != lshs.size()) {
						remark = "生成比例再保保单明细流水号失败";
						break;
					}
					int index = 0;
					Date updateTime = DateUtils.getNowDate();
					List<Map<String, Object>> blzbbdmxbList = new ArrayList<Map<String, Object>>();
					for(Long id : ids) {
						Map<String, Object> blzbbdmxbMap = MapUtil.newHashMap(4);
						blzbbdmxbMap.put("ID", id);
						blzbbdmxbMap.put("LSH", lshs.get(index));
						blzbbdmxbMap.put("UPDATE_TIME", updateTime);
						blzbbdmxbMap.put("UPDATE_BY", zbzdxxb.getCreateBy());
						blzbbdmxbList.add(blzbbdmxbMap);
						index++;
					}
					int updateRows = dwsEastBlzbbdmxbService.updateDwsEastBlzbbdmxbLsh(blzbbdmxbList);
					log.info("再保East比例再保保单明细更新流水号进行中, lsh:{}, updateRows:{}", zbzdxxb.getLsh(), updateRows);
					if(ids.size() < batchLimit) {//已处理完成
						importStatus = CedeoutEnums.导入状态_已导入.getValue();
						break;
					}
				}
				if(StringUtils.isNotEmpty(remark)) {
					zbzdxxb.setRemark(StringUtils.joinWith(";", zbzdxxb.getRemark(), remark));
				}
				zbzdxxb.setImportStatus(importStatus);
				zbzdxxb.setUpdateTime(DateUtils.getNowDate());
				int updateResult = dwsEastZbzdxxbService.updateZbzdxxbImportStatusByLsh(zbzdxxb);
				log.info("再保East比例再保保单明细更新流水号完成, lsh:{}, updateResult:{}", zbzdxxb.getLsh(), updateResult);
			}catch(Exception e) {
				log.error("再保East比例再保保单明细更新流水号出错, lsh:{}, 错误原因:", zbzdxxb.getLsh(), e);
			}
		}
	}


	/**
	 * 生成保单登记再保产品信息对象
	 * @param regulatoryReportDTO 监管报表信息对象
	 * @return 结果
	 */
	private Result insertPrpProductData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		try {
			int reportYear = regulatoryReportDTO.getReportYear();
			int reportMonth = regulatoryReportDTO.getReportMonth();

			// 1. 查询系统中的产品配置数据
			Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
			Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();
			CedeoutContractLiabilityQuery contractLiabilityQuery = new CedeoutContractLiabilityQuery();
			contractLiabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
			contractLiabilityQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
			contractLiabilityQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
			List<DwsPrpProductDTO> productList = cedeoutContractService.selectContractLiabilityAsPrpProduct(contractLiabilityQuery);
			if(CollUtil.isEmpty(productList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无产品数据。", 0);
			}
			// 2. 读取系统参数表的报表报送数据保险公司代码
			String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
			if(StringUtils.isBlank(companyCode)) {
				return Result.error("系统参数中未配置保险机构代码，请联系管理员");
			}
			// 3. 生成流水号
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.PRP, companyCode, productList.size());
			if(CollUtil.isEmpty(serialNos) || productList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			// 4.设置系统字段
			int index = 0;
			for(DwsPrpProductDTO product : productList) {
				product.setReportYear(reportYear);
				product.setReportMonth(reportMonth);
				product.setCompanyCode(companyCode);
				product.setTransactionNo(serialNos.get(index));
				product.setContOrAmendmentType(PrpContractAmendmentType.getCodeByMapingCode(product.getContOrAmendmentType()));
				if(PrpContractAmendmentType.主合同.getCode() == product.getContOrAmendmentType()){
					product.setMainReInsuranceContNo(product.getReInsuranceContNo());
				}
				product.setCreateTime(DateUtils.getNowDate());
				product.setUpdateTime(DateUtils.getNowDate());
				product.setCreateBy(SecurityUtils.getUsername());
				product.setUpdateBy(SecurityUtils.getUsername());
				product.setDataSource(ReportDataSource.系统.getCode());
				product.setPushStatus(ReportPushStatus.未推送.getCode());
				product.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
				index++;
			}
			// 5. 批量入库
			int insertRows = dwsPrpProductService.insertBatchDwsPrpProduct(productList);
			return Result.success("成功生成" + insertRows + "条LRProduct", insertRows);
		} catch(Exception e) {
			log.error("生成LRProduct表数据出错, 错误原因:", e);
			return Result.error("生成LRProduct表数据出错，请联系管理员。");
		}
	}

	/**
	 * 生成保单登记再保合同信息对象表数据
	 * @param regulatoryReportDTO 监管报表信息对象
	 * @return 结果
	 */
	private Result insertPrpInsureContData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		try {
			int reportYear = regulatoryReportDTO.getReportYear();
			int reportMonth = regulatoryReportDTO.getReportMonth();

			// 1. 查询系统中的产品配置数据
			Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
			Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();
			CedeoutContractQuery cedeoutContractQuery = new CedeoutContractQuery();
			cedeoutContractQuery.setStatus(CedeoutEnums.状态_有效.getValue());
			cedeoutContractQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
			cedeoutContractQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
			List<DwsPrpInsureContDTO> contractList = cedeoutContractService.selectOriginalContractAsPrpInsureCont(cedeoutContractQuery);
			if(CollUtil.isEmpty(contractList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无产品数据。", 0);
			}
			// 2. 读取系统参数表的报表报送数据保险公司代码
			String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
			if(StringUtils.isBlank(companyCode)) {
				return Result.error("系统参数中未配置保险机构代码，请联系管理员");
			}
			// 3. 生成流水号
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.PRP, companyCode, contractList.size());
			if(CollUtil.isEmpty(serialNos) || contractList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			// 4.设置系统字段
			int index = 0;
			for(DwsPrpInsureContDTO contract : contractList) {
				contract.setReportYear(reportYear);
				contract.setReportMonth(reportMonth);
				contract.setCompanyCode(companyCode);
				contract.setTransactionNo(serialNos.get(index));
				contract.setContOrAmendmentType(PrpContractAmendmentType.getCodeByMapingCode(contract.getContOrAmendmentType()));
				if(PrpContractAmendmentType.主合同.getCode() == contract.getContOrAmendmentType()){
					contract.setMainReInsuranceContNo(contract.getReInsuranceContNo());
				}
				contract.setContType(PrpContType.比例合同.getCode());
				contract.setContStatus(PrpContStatus.有效.getCode());
				contract.setChargeType(PrpChargeType.业务年度.getCode());
				contract.setCreateTime(DateUtils.getNowDate());
				contract.setUpdateTime(DateUtils.getNowDate());
				contract.setCreateBy(SecurityUtils.getUsername());
				contract.setUpdateBy(SecurityUtils.getUsername());
				contract.setDataSource(ReportDataSource.系统.getCode());
				contract.setPushStatus(ReportPushStatus.未推送.getCode());
				contract.setAccountPeriod(reportYear + String.format("%02d", reportMonth));
				index++;
			}
			// 5. 批量入库
			int insertRows = dwsPrpInsureContService.insertBatchDwsPrpInsureCont(contractList);
			return Result.success("成功生成" + insertRows + "条LRInsureCont", insertRows);
		} catch(Exception e) {
			log.error("生成保单登记再保合同信息表数据出错, 错误原因:", e);
			return Result.error("生成保单登记再保合同信息表数据出错，请联系管理员。");
		}
	}


	/**
	 * 生成保单登记账单信息对象表数据
	 * @param regulatoryReportDTO
	 * @return
	 */
	public Result insertPrpAccountData(DwsRegulatoryReportDTO regulatoryReportDTO) {
		String billType = String.valueOf(regulatoryReportDTO.getBillType());
		String billTypeName = PrpBillType.getName(billType);
		if(StringUtils.isBlank(billTypeName)) {
			return Result.error("账单类型只允许为预提（未结算）/实际（已结算）。");
		}
		int reportYear = regulatoryReportDTO.getReportYear();
		int reportMonth = regulatoryReportDTO.getReportMonth();
		Date firstDayOfMonth = ReDateUtil.getFirstDayOfMonth(reportYear, reportMonth);
		Date lastDayOfMonth = ReDateUtil.endOfDay(ReDateUtil.endOfMonth(firstDayOfMonth)).toJdkDate();

		String companyCode = sysConfigExtService.selectConfigByKey(RsConstant.eastReportInsCompanyCode);
		if(StringUtils.isBlank(companyCode)) {
			return Result.error("系统参数中未配置保险机构代码，请联系管理员");
		}
		String mysqlBaseName = sysConfigExtService.selectConfigByKey(RsConstant.mysqlBaseName);
		String dataWarehouseName = sysConfigExtService.selectConfigByKey(RsConstant.dataWarehouseName);
		int batchLimit = Integer.valueOf(sysConfigExtService.selectConfigByKey(RsConstant.jobLimitRows));
		List<DwsPrpAccountDTO> accountList = null;
		if(PrpBillType.未结算.getCode().equals(billType)) {//未结算
			Date startDate = ReDateUtil.offsetDay(firstDayOfMonth, 1).toJdkDate();
			Date endDate = ReDateUtil.offsetDay(lastDayOfMonth, 1).toJdkDate();
			DwsReinsuTradeQuery dwsReinsuTradeQuery = new DwsReinsuTradeQuery();
			dwsReinsuTradeQuery.getParams().put("mysqlBaseName", mysqlBaseName);
			dwsReinsuTradeQuery.getParams().put(RsConstant.paramsStartDate, startDate);
			dwsReinsuTradeQuery.getParams().put(RsConstant.paramsEndDate, endDate);
			accountList = dwsReinsuTradeService.selectDwsReinsuTradeGroupSummaryAsPrpAccount(dwsReinsuTradeQuery);
			if(CollUtil.isEmpty(accountList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无预提（未结算）账单数据。", 0);
			}

			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.PRP, companyCode, accountList.size());
			if(CollUtil.isEmpty(serialNos) || accountList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			int index = 0;
			for(DwsPrpAccountDTO account : accountList) {
				account.setPairingStatus(billType);
				account.setCompanyCode(companyCode);
				account.setCurrency(RsConstant.prp_currency);
				account.setTransactionNo(serialNos.get(index));
				account.setAccountingPeriodfrom(firstDayOfMonth);
				account.setAccountingPeriodto(lastDayOfMonth);
				account.setAccountStatus(PrpContStatus.有效.getCode());
				account.setContImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setEdorImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setClaimImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setBenefitImportStatus(CedeoutEnums.导入状态_不需要.getValue());
				account.setAccountID(account.getReInsuranceContNo() + reportYear + "Q" + DateUtil.quarter(firstDayOfMonth));//再保合同号+年度+季度

				account.setReturnAnnuity(BigDecimal.ZERO);//摊回年金
				account.setReturnLivBene(BigDecimal.ZERO);//摊回生存金
				account.setReturnSurrenderPay(BigDecimal.ZERO);//摊回退保金

				account.setReportYear(reportYear);
				account.setReportMonth(reportMonth);
				account.setCreateTime(DateUtils.getNowDate());
				account.setUpdateTime(DateUtils.getNowDate());
				account.setCreateBy(SecurityUtils.getUsername());
				account.setUpdateBy(SecurityUtils.getUsername());
				account.setDataSource(ReportDataSource.系统.getCode());
				account.setPushStatus(ReportPushStatus.未推送.getCode());
				account.setAccountPeriod(reportYear + String.format("%02d", reportMonth));

				account.getParams().put("mysqlBaseName", mysqlBaseName);
				account.getParams().put("dataWarehouseName", dataWarehouseName);
				account.getParams().put(RsConstant.paramsStartDate, startDate);
				account.getParams().put(RsConstant.paramsEndDate, endDate);
				index++;
			}
		}else {//实际
			DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery = new DwsReinsuSettleBillQuery();
			dwsReinsuSettleBillQuery.getParams().put("mysqlBaseName", mysqlBaseName);
			dwsReinsuSettleBillQuery.setConfirmStatus(ConfirmStatus.已确认.getCode());
			dwsReinsuSettleBillQuery.getParams().put(RsConstant.paramsStartDate, firstDayOfMonth);
			dwsReinsuSettleBillQuery.getParams().put(RsConstant.paramsEndDate, lastDayOfMonth);
			accountList = dwsReinsuSettleBillService.selectSettleBillAsPrpAccount(dwsReinsuSettleBillQuery);
			if(CollUtil.isEmpty(accountList)) {
				return Result.success("生成报表成功，" + reportYear + "年" + reportMonth + "月无实际（已结算）账单数据。", 0);
			}
			List<String> serialNos = redisService.getUniqueCodes(RedisKeyModule.PRP, companyCode, accountList.size());
			if(CollUtil.isEmpty(serialNos) || accountList.size() != serialNos.size()) {
				return Result.error("生成流水号失败，请联系管理员");
			}
			int index = 0;
			List<String> billNos = accountList.stream().map(DwsPrpAccountDTO::getSettleBillNo).collect(Collectors.toList());
			Map<String, DwsPrpAccountDTO> accountMoneyMap = dwsReinsuTradeService.selectDwsReinsuTradeSummaryMoneyAsPrpAccount(billNos);
			for(DwsPrpAccountDTO account : accountList) {
				DwsPrpAccountDTO accountMoney = accountMoneyMap.get(account.getSettleBillNo());
				account.setPairingStatus(billType);
				account.setCompanyCode(companyCode);
				account.setCurrency(RsConstant.prp_currency);
				account.setTransactionNo(serialNos.get(index));
				account.setAccountingPeriodfrom(firstDayOfMonth);
				account.setAccountingPeriodto(lastDayOfMonth);
				account.setAccountStatus(PrpContStatus.有效.getCode());
				account.setContImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setEdorImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setClaimImportStatus(CedeoutEnums.导入状态_未导入.getValue());
				account.setBenefitImportStatus(CedeoutEnums.导入状态_不需要.getValue());
				account.setAccountID(account.getReInsuranceContNo() + reportYear + "Q" + DateUtil.quarter(firstDayOfMonth));//再保合同号+年度+季度

				account.setReturnAnnuity(BigDecimal.ZERO);//摊回年金
				account.setReturnLivBene(BigDecimal.ZERO);//摊回生存金
				account.setReturnSurrenderPay(BigDecimal.ZERO);//摊回退保金
				account.setReinsurancePremium(accountMoney != null ? accountMoney.getReinsurancePremium() : BigDecimal.ZERO);//分保费
				account.setReinsuranceCommssion(accountMoney != null ? accountMoney.getReinsuranceCommssion() : BigDecimal.ZERO);//分保佣金
				account.setReturnReinsurancePremium(accountMoney != null ? accountMoney.getReturnReinsurancePremium() : BigDecimal.ZERO);//退回分保费
				account.setReturnReinsuranceCommssion(accountMoney != null ? accountMoney.getReturnReinsuranceCommssion() : BigDecimal.ZERO);//退回分保佣金
				account.setReturnClaimPay(accountMoney != null ? accountMoney.getReturnClaimPay() : BigDecimal.ZERO);//摊回理赔款
				account.setReturnMaturity(accountMoney != null ? accountMoney.getReturnMaturity() : BigDecimal.ZERO);//摊回满期金

				account.setReportYear(reportYear);
				account.setReportMonth(reportMonth);
				account.setCreateTime(DateUtils.getNowDate());
				account.setUpdateTime(DateUtils.getNowDate());
				account.setCreateBy(SecurityUtils.getUsername());
				account.setUpdateBy(SecurityUtils.getUsername());
				account.setDataSource(ReportDataSource.系统.getCode());
				account.setPushStatus(ReportPushStatus.未推送.getCode());
				account.setAccountPeriod(reportYear + String.format("%02d", reportMonth));

				account.setTotalRows(accountMoney.getTotalRows());
				account.getParams().put("mysqlBaseName", mysqlBaseName);
				account.getParams().put("dataWarehouseName", dataWarehouseName);
				index++;
			}
		}
		int insertRows = dwsPrpAccountService.insertBatchDwsPrpAccount(accountList);
		final List<DwsPrpAccountDTO> insertAccountList = ReinsuObjectUtil.convertList(accountList, DwsPrpAccountDTO.class);
		CompletableFuture.runAsync(() -> insertPrpAccountSubData(insertAccountList, batchLimit));//插入四类明细数据，更新流水号
		return Result.success("生成报表数据成功", insertRows);
	}

	/**
	 * 生成保单登记首续期险种明细、保全变更信息、理赔信息数据
	 * @param insertAccountList
	 * @param batchLimit
	 */
	private void insertPrpAccountSubData(List<DwsPrpAccountDTO> insertAccountList, int batchLimit){
		for (DwsPrpAccountDTO account : insertAccountList){
			int insertRows = 0;
			try{
				List<String> busiTypes = Arrays.asList(StringUtils.trimToEmpty(account.getBusiTypes()).split(","));
				//生成续期险种明细、保全变更信息、理赔信息数据
				insertRows += this.insertPrpContData(busiTypes, account);
				insertRows += this.insertPrpEdorData(busiTypes, account);
				insertRows += this.insertPrpClaimData(busiTypes, account);
				//更新续期险种明细、保全变更信息、理赔信息表的交易编码
				this.updatePrpContTransactionNo(batchLimit, account);
				this.updatePrpEdorTransactionNo(batchLimit, account);
				this.updatePrpClaimTransactionNo(batchLimit, account);
				//更新账单导入明细的状态
				account.setUpdateTime(DateUtils.getNowDate());
				account.setRemark("totalRows:" + account.getTotalRows() + "; insertRows:" + insertRows);
				int updateRows = dwsPrpAccountService.updateAccountImportStatusByTransactionNo(account);
				log.info("再保生成保单登记更新明细交易编码完成, TransactionNo:{}, updateRows:{}", account.getTransactionNo(), updateRows);
			}catch(Exception e){
				log.error("再保生成保单登记更新明细交易编码出错, TransactionNo:{}, 错误原因：", account.getTransactionNo(), e);
			}
		}
	}

	/**
	 * 生成再保登记首续期险种明细表数据
	 * @param busiTypes
	 * @param account
	 * @return
	 */
	private int insertPrpContData(List<String> busiTypes, DwsPrpAccountDTO account){
		int insertContRows = 0;
		if(!busiTypes.contains(CedeoutEnums.业务类型_新单.getValue().toString()) &&
				!busiTypes.contains(CedeoutEnums.业务类型_续期.getValue().toString()) &&
				!busiTypes.contains(CedeoutEnums.业务类型_保全.getValue().toString())) {//不存在分出场景
			account.setContImportStatus(CedeoutEnums.导入状态_不需要.getValue());
			return insertContRows;
		}
		try {
			if(String.valueOf(PrpBillType.已结算.getCode()).equals(account.getPairingStatus())) {
				insertContRows = dwsPrpContService.insertDwsPrpContFormTrade(account);
			}else {
				insertContRows = dwsPrpContService.insertPreDwsPrpContFormTrade(account);
			}
			account.setContImportRemark("insertRows:" + insertContRows);
			account.setContImportStatus(CedeoutEnums.导入状态_已导入.getValue());
			log.info("再保生成保单登记首续期险种明细完成, TransactionNo:{}, insertRows:{}", account.getTransactionNo(), insertContRows);
		}catch(Exception e) {
			account.setContImportRemark("插入首续期险种明细表异常");
			log.error("再保生成保单登记首续期险种明细插入出错, TransactionNo:{}, 错误原因:", account.getTransactionNo(), e);
		}
		return insertContRows;
	}

	/**
	 * 生成再保登记保全变更信息表数据
	 * @param busiTypes
	 * @param account
	 * @return
	 */
	private int insertPrpEdorData(List<String> busiTypes, DwsPrpAccountDTO account){
		int insertEdorRows = 0;
		if(!busiTypes.contains(CedeoutEnums.业务类型_保全.getValue().toString()) &&
				!busiTypes.contains(CedeoutEnums.业务类型_失效.getValue().toString())) {//保全、失效摊回场景
			account.setEdorImportStatus(CedeoutEnums.导入状态_不需要.getValue());
			return insertEdorRows;
		}
		try {
			if(String.valueOf(PrpBillType.已结算.getCode()).equals(account.getPairingStatus())) {
				insertEdorRows = dwsPrpEdorService.insertDwsPrpEdorFormTrade(account);
			}else {
				insertEdorRows = dwsPrpEdorService.insertPreDwsPrpEdorFormTrade(account);
			}
			account.setEdorImportRemark("insertRows:" + insertEdorRows);
			account.setEdorImportStatus(CedeoutEnums.导入状态_已导入.getValue());
			log.info("再保生成保单登记保全变更信息完成, TransactionNo:{}, insertRows:{}", account.getTransactionNo(), insertEdorRows);
		}catch(Exception e) {
			account.setEdorImportRemark("插入保全变更信息表异常");
			log.error("再保生成保单登记保全变更信息插入出错, TransactionNo:{}, 错误原因:", account.getTransactionNo(), e);
		}
		return insertEdorRows;
	}

	/**
	 * 生成再保登记理赔信息表数据
	 * @param busiTypes
	 * @param account
	 * @return
	 */
	private int insertPrpClaimData(List<String> busiTypes, DwsPrpAccountDTO account){
		int insertClaimRows = 0;
		if(!busiTypes.contains(CedeoutEnums.业务类型_理赔.getValue().toString())) {//理赔摊回场景
			account.setClaimImportStatus(CedeoutEnums.导入状态_不需要.getValue());
			return insertClaimRows;
		}
		try {
			if(String.valueOf(PrpBillType.已结算.getCode()).equals(account.getPairingStatus())) {
				insertClaimRows = dwsPrpClaimService.insertDwsPrpClaimFormTrade(account);
			}else {
				insertClaimRows = dwsPrpClaimService.insertPreDwsPrpClaimFormTrade(account);
			}
			account.setClaimImportRemark("insertRows:" + insertClaimRows);
			account.setClaimImportStatus(CedeoutEnums.导入状态_已导入.getValue());
			log.info("再保生成保单登记理赔信息表完成, TransactionNo:{}, insertRows:{}", account.getTransactionNo(), insertClaimRows);
		}catch(Exception e) {
			account.setClaimImportRemark("插入理赔信息表异常");
			log.error("再保生成保单登记理赔信息表插入出错, TransactionNo:{}, 错误原因:", account.getTransactionNo(), e);
		}
		return insertClaimRows;
	}

	/**
	 * 更新保单登记首续期险种明细交易编码
	 * @param batchLimit
	 * @param account
	 */
	private void updatePrpContTransactionNo(int batchLimit, DwsPrpAccountDTO account) {
		try {
			log.info("再保保单登记首续期险种明细更新交易编码开始, TransactionNo:{}", account.getTransactionNo());
			while (true) {
				List<Long> ids = dwsPrpContService.selectWaitSetTransNoDwsPrpContListByAccTransNo(account.getTransactionNo(), batchLimit);
				if (CollUtil.isEmpty(ids)) {
					account.setContImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "未匹配到明细数据"));
					log.info("再保保单登记首续期险种明细更新交易编码结束, 未匹配到明细数据, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				List<String> transNos = redisService.getUniqueCodes(RedisKeyModule.PRP, account.getReinsurerCode(), ids.size());
				if (CollUtil.isEmpty(transNos) || ids.size() != transNos.size()) {
					account.setContImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "生成流水号失败"));
					log.info("再保保单登记首续期险种明细更新交易编码结束, 生成流水号失败, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				int index = 0;
				Date updateTime = DateUtils.getNowDate();
				List<Map<String, Object>> prpContList = new ArrayList<Map<String, Object>>();
				for (Long id : ids) {
					Map<String, Object> contMap = MapUtil.newHashMap(4);
					contMap.put("Id", id);
					contMap.put("TransactionNo", transNos.get(index));
					contMap.put("UpdateTime", updateTime);
					contMap.put("UpdateBy", account.getCreateBy());
					prpContList.add(contMap);
					index++;
				}
				dwsPrpContService.updateDwsPrpContTransactionNo(prpContList);
				if (ids.size() < batchLimit) {//已处理完成
					log.info("再保保单登记首续期险种明细更新交易编码完成, TransactionNo:{}", account.getTransactionNo());
					break;
				}
			}
		}catch(Exception e){
			account.setContImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "系统异常"));
			log.error("再保保单登记首续期险种明细更新交易编码出错, TransactionNo:{}", account.getTransactionNo(), e);
		}
	}

	/**
	 * 更新保单登记保全变更信息交易编码
	 * @param batchLimit
	 * @param account
	 */
	private void updatePrpEdorTransactionNo(int batchLimit, DwsPrpAccountDTO account) {
		try {
			log.info("再保保单登记保全变更信息更新交易编码开始, TransactionNo:{}", account.getTransactionNo());
			while (true) {
				List<Long> ids = dwsPrpContService.selectWaitSetTransNoDwsPrpContListByAccTransNo(account.getTransactionNo(), batchLimit);
				if (CollUtil.isEmpty(ids)) {
					account.setEdorImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "未匹配到明细数据"));
					log.info("再保保单登记保全变更信息更新交易编码结束, 未匹配到明细数据, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				List<String> transNos = redisService.getUniqueCodes(RedisKeyModule.PRP, account.getReinsurerCode(), ids.size());
				if (CollUtil.isEmpty(transNos) || ids.size() != transNos.size()) {
					account.setEdorImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "生成流水号失败"));
					log.info("再保保单登记保全变更信息更新交易编码结束, 生成流水号失败, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				int index = 0;
				Date updateTime = DateUtils.getNowDate();
				List<Map<String, Object>> prpEdorList = new ArrayList<Map<String, Object>>();
				for (Long id : ids) {
					Map<String, Object> contMap = MapUtil.newHashMap(4);
					contMap.put("Id", id);
					contMap.put("TransactionNo", transNos.get(index));
					contMap.put("UpdateTime", updateTime);
					contMap.put("UpdateBy", account.getCreateBy());
					prpEdorList.add(contMap);
					index++;
				}
				dwsPrpEdorService.updateDwsPrpEdorTransactionNo(prpEdorList);
				if (ids.size() < batchLimit) {//已处理完成
					log.info("再保保单登记保全变更信息更新交易编码完成, TransactionNo:{}", account.getTransactionNo());
					break;
				}
			}
		}catch(Exception e){
			account.setEdorImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "系统异常"));
			log.error("再保保单登记保全变更信息更新交易编码出错, TransactionNo:{}", account.getTransactionNo(), e);
		}
	}

	/**
	 * 更新保单登记理赔信息交易编码
	 * @param batchLimit
	 * @param account
	 */
	private void updatePrpClaimTransactionNo(int batchLimit, DwsPrpAccountDTO account) {
		try {
			log.info("再保保单登记理赔信息更新交易编码开始, TransactionNo:{}", account.getTransactionNo());
			while (true) {
				List<Long> ids = dwsPrpClaimService.selectWaitSetTransNoDwsPrpClaimListByAccTransNo(account.getTransactionNo(), batchLimit);
				if (CollUtil.isEmpty(ids)) {
					account.setClaimImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "未匹配到明细数据"));
					log.info("再保保单登记理赔信息更新交易编码结束, 未匹配到明细数据, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				List<String> transNos = redisService.getUniqueCodes(RedisKeyModule.PRP, account.getReinsurerCode(), ids.size());
				if (CollUtil.isEmpty(transNos) || ids.size() != transNos.size()) {
					account.setClaimImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "生成流水号失败"));
					log.info("再保保单登记理赔信息更新交易编码结束, 生成流水号失败, TransactionNo:{}", account.getTransactionNo());
					break;
				}
				int index = 0;
				Date updateTime = DateUtils.getNowDate();
				List<Map<String, Object>> prpClaimList = new ArrayList<Map<String, Object>>();
				for (Long id : ids) {
					Map<String, Object> contMap = MapUtil.newHashMap(4);
					contMap.put("Id", id);
					contMap.put("TransactionNo", transNos.get(index));
					contMap.put("UpdateTime", updateTime);
					contMap.put("UpdateBy", account.getCreateBy());
					prpClaimList.add(contMap);
					index++;
				}
				dwsPrpClaimService.updateDwsPrpClaimTransactionNo(prpClaimList);
				if (ids.size() < batchLimit) {//已处理完成
					log.info("再保保单登记理赔信息更新交易编码完成, TransactionNo:{}", account.getTransactionNo());
					break;
				}
			}
		}catch(Exception e){
			account.setClaimImportRemark(StringUtils.joinWith(";", account.getContImportRemark(), "系统异常"));
			log.error("再保保单登记理赔信息更新交易编码出错, TransactionNo:{}", account.getTransactionNo(), e);
		}
	}

}
