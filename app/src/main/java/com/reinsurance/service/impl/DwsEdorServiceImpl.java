package com.reinsurance.service.impl;

import com.reinsurance.domain.DwsEdorEntity;
import com.reinsurance.domain.DwsEdorLabelEntity;
import com.reinsurance.mapper.DwsEdorLabelMapper;
import com.reinsurance.mapper.DwsEdorMapper;
import com.reinsurance.service.IDwsEdorService;
import com.reinsurance.utils.ReinsuJsonUtil;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.JsonUtil;
import com.jd.finance.common.util.StarRocksConnector;
import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.enums.DataSourceType;

@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsEdorServiceImpl implements IDwsEdorService {
	
	@Autowired
	private DwsEdorMapper dwsEdorMapper;
	
	@Autowired
	private DwsEdorLabelMapper dwsEdorLabelMapper;
	
	@Autowired
    private StarRocksConnector starRocksConnector;
	
	/**
	 * 保全列表
	 *
	 * @param dwsEdorEntity 保全
	 * @return 保全列表集合
	 */
	public List<DwsEdorEntity> selectDwsEdorList(DwsEdorEntity dwsEdorEntity) {
		return dwsEdorMapper.selectDwsEdorList(dwsEdorEntity);
	}

	@Override
	public int insertBatchDwsEdorLabel(DwsEdorLabelEntity dwsEdorLabelEntity) {
		return dwsEdorLabelMapper.insertBatchDwsEdorLabel(dwsEdorLabelEntity);
	}

	@Override
	public List<DwsEdorLabelEntity> selectDwsEdorLabelList(DwsEdorLabelEntity dwsEdorLabelEntity) {
		return dwsEdorLabelMapper.selectDwsEdorLabelList(dwsEdorLabelEntity);
	}

	@Override
	public boolean updateBatchDwsEdorLabel(List<Map<String, Object>> updateDwsEdorLabelList) {
		StarRocksOperationResult operationResult = null;
    	try {
    		String jsonData = ReinsuJsonUtil.toJsonString(updateDwsEdorLabelList);
    		String columnNames = "id,handle_status,handle_date,handle_error_num,handle_error_msg";
			StarRocksOperationResult starRocksResult = starRocksConnector.update("t_dws_edor_label", jsonData, columnNames);
			log.info("批量更新保全记录处理状态完成, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
			return ("success").equalsIgnoreCase(starRocksResult.getStatus()) ? true : false;
    	}catch(Exception e) {
    		log.error("starRocks更新保全记录处理状态失败, operationResult:{}", JsonUtil.toJSONString(operationResult));
    		return false;
    	}
	}
	
}
