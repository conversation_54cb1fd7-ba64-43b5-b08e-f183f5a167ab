package com.reinsurance.service.impl;

import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.HttpStatus;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.jd.finance.common.dto.StarRocksOperationResult;
import com.jd.finance.common.util.StarRocksConnector;
import com.jd.lightning.common.annotation.Excel;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.DictUtils;
import com.jd.lightning.common.utils.StringUtils;
import com.reinsurance.domain.DwsPrpAccountEntity;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.dto.excel.DwsPrpClaimImportXlsDTO;
import com.reinsurance.enums.BasicDataEnums;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.listener.DwsPrpClaimReadListener;
import com.reinsurance.mapper.DwsPrpAccountMapper;
import com.reinsurance.service.IFileService;
import com.reinsurance.service.IRedisService;
import com.reinsurance.utils.FileUploadUtils;
import com.reinsurance.utils.ReinsuJsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.jd.lightning.common.annotation.DataSource;
import com.jd.lightning.common.enums.DataSourceType;
import com.jd.lightning.common.utils.DateUtils;

import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.mapper.DwsPrpClaimMapper;
import com.reinsurance.query.DwsPrpClaimQuery;
import com.reinsurance.service.IDwsPrpClaimService;
import com.reinsurance.utils.ReinsuObjectUtil;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 再保理赔信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Slf4j
@Service
@DataSource(DataSourceType.SLAVE)
public class DwsPrpClaimServiceImpl implements IDwsPrpClaimService {

    @Autowired
    private IFileService fileService;

    @Autowired
    private IRedisService redisService;

    @Autowired
    private StarRocksConnector starRocksConnector;

    @Autowired
    private DwsPrpClaimMapper dwsPrpClaimMapper;

    @Autowired
    private DwsPrpAccountMapper dwsPrpAccountMapper;

    /**
     * 查询再保理赔信息
     *
     * @param Id 再保理赔信息主键
     * @return 再保理赔信息
     */
    @Override
    public DwsPrpClaimEntity selectDwsPrpClaimById(Long Id) {
        return dwsPrpClaimMapper.selectDwsPrpClaimById(Id);
    }

    /**
     * 查询再保理赔信息列表
     *
     * @param dwsPrpClaimQuery 再保理赔信息
     * @return 再保理赔信息
     */
    @Override
    public List<DwsPrpClaimEntity> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery) {
        return dwsPrpClaimMapper.selectDwsPrpClaimList(dwsPrpClaimQuery);
    }

    /**
     * 根据主键数组查询再保理赔信息列表
     *
     * @param Ids 主键数组
     * @return 再保理赔信息集合
     */
    @Override
    public List<DwsPrpClaimEntity> selectDwsPrpClaimByIds(Long[] Ids) {
        return dwsPrpClaimMapper.selectDwsPrpClaimByIds(Ids);
    }

    /**
     * 新增再保理赔信息
     *
     * @param dwsPrpClaimDTO 再保理赔信息
     * @return 结果
     */
    @Override
    public int insertDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO) {
        DwsPrpClaimEntity dwsPrpClaimEntity = ReinsuObjectUtil.convertModel(dwsPrpClaimDTO, DwsPrpClaimEntity.class);
        dwsPrpClaimEntity.setCreateTime(DateUtils.getNowDate());
        return dwsPrpClaimMapper.insertDwsPrpClaim(dwsPrpClaimEntity);
    }

    /**
     * 批量新增再保理赔信息
     *
     * @param dwsPrpClaimList 再保理赔信息列表
     * @return 结果
     */
    @Override
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimEntity> dwsPrpClaimList) {
        return dwsPrpClaimMapper.insertBatchDwsPrpClaim(dwsPrpClaimList);
    }

    /**
     * 修改再保理赔信息
     *
     * @param dwsPrpClaimDTO 再保理赔信息
     * @return 结果
     */
    @Override
    public int updateDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO) {
        DwsPrpClaimEntity dwsPrpClaimEntity = ReinsuObjectUtil.convertModel(dwsPrpClaimDTO, DwsPrpClaimEntity.class);
        dwsPrpClaimEntity.setUpdateTime(DateUtils.getNowDate());
        return dwsPrpClaimMapper.updateDwsPrpClaim(dwsPrpClaimEntity);
    }

    /**
     * 批量删除再保理赔信息
     *
     * @param Ids 需要删除的再保理赔信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpClaimByIds(Long[] Ids) {
        return dwsPrpClaimMapper.deleteDwsPrpClaimByIds(Ids);
    }

    /**
     * 删除再保理赔信息信息
     *
     * @param Id 再保理赔信息主键
     * @return 结果
     */
    @Override
    public int deleteDwsPrpClaimById(Long Id) {
        return dwsPrpClaimMapper.deleteDwsPrpClaimById(Id);
    }

    /**
     * 查询再保理赔信息总数
     * @param accTransNo 账单交易编码
     * @return
     */
    @Override
    public int selectDwsPrpClaimCountByAccTransNo(String accTransNo) {
        return dwsPrpClaimMapper.selectDwsPrpClaimCountByAccTransNo(accTransNo);
    }

    /**
     * 查询再保理赔信息列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保理赔信息列表
     */
    @Override
    public List<DwsPrpClaimEntity> selectDwsPrpClaimListByAccTransNo(String accTransNo, int pageSize, int startRows) {
        return dwsPrpClaimMapper.selectDwsPrpClaimListByAccTransNo(accTransNo, pageSize, startRows);
    }

    /**
     * 删除再保理赔信息
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    @Override
    public int deleteDwsPrpClaimByAccTransNo(String accTransNo) {
        return dwsPrpClaimMapper.deleteDwsPrpClaimByAccTransNo(accTransNo);
    }

    /**
     * 批量插入再保理赔信息数据（已结算）
     * @param account
     * @return
     */
    @Override
    public int insertDwsPrpClaimFormTrade(DwsPrpAccountDTO account){
        return dwsPrpClaimMapper.insertDwsPrpClaimFormTrade(account);
    }

    /**
     * 批量插入再保理赔信息数据（未结算）
     * @param account
     * @return
     */
    @Override
    public int insertPreDwsPrpClaimFormTrade(DwsPrpAccountDTO account){
        return dwsPrpClaimMapper.insertPreDwsPrpClaimFormTrade(account);
    }

    /**
     * 查询再保理赔信息交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return 再保理赔信息ID集合
     */
    @Override
    public List<Long> selectWaitSetTransNoDwsPrpClaimListByAccTransNo(String accTransNo, int limit) {
        return dwsPrpClaimMapper.selectWaitSetTransNoDwsPrpClaimListByAccTransNo(accTransNo, limit);
    }

    /**
     * 批量更新再保理赔信息交易编码（streamload方式）
     * @param contList
     * @return
     */
    @Override
    public int updateDwsPrpClaimTransactionNo(List<Map<String, Object>> contList){
        int result = 0;
        try {
            String columnNames = "Id,TransactionNo,UpdateBy,UpdateTime";
            StarRocksOperationResult starRocksResult = starRocksConnector.update("t_dws_prp_claim", ReinsuJsonUtil.toJsonString(contList), columnNames);
            String status = StringUtils.trimToEmpty(starRocksResult.getStatus());
            if(!("success").equalsIgnoreCase(status)) {//操作失败
                log.error("再保批量更新保单登记理赔信息交易编码失败, starRocksResult:{}", ReinsuJsonUtil.toJsonString(starRocksResult));
            }else {
                result = contList.size();
            }
            return result;
        } catch (Exception e) {
            log.error("再保批量更新保单登记理赔信息交易编码出错, 错误原因", e);
            return 0;
        }
    }

    /**
     * 导入再保理赔信息
     *
     * @param accountId 所属账单Id
     * @param file 导入文件
     * @return 结果
     */
    @Override
    public Result importDwsPrpClaim(Long accountId, MultipartFile file){
        try {
            DwsPrpAccountEntity account = dwsPrpAccountMapper.selectDwsPrpAccountById(accountId);
            if(account == null) {
                return Result.error("账单信息不存在");
            }
            if(CedeoutEnums.导入状态_已导入.getValue() == account.getClaimImportStatus()) {
                return Result.error("账单已导入理赔信息明细，不允许重复导入。");
            }
            if(BasicDataEnums.ReportPushStatus.已推送.getCode() == account.getPushStatus()) {
                return Result.error("账单已推送至报送平台，不允许导入理赔信息。");
            }
            String fileName = FileUploadUtils.upload(fileService.getStoragePath() + "prp/claim/", file);
            CompletableFuture.runAsync(() -> {
                DwsPrpClaimReadListener readListener = new DwsPrpClaimReadListener(account, redisService, starRocksConnector);
                EasyExcel.read(FileUtil.getInputStream(fileName), DwsPrpClaimImportXlsDTO.class, readListener).sheet().doRead();
            });
            return Result.success("文件上传成功, 数据后台导入中, 请稍后查询导入结果。", HttpStatus.HTTP_OK);
        }catch(Exception e) {
            log.error("再保导入保单登记首续期险种明细出错, accountId:{}, 错误原因:", accountId, e);
            return Result.error("导入首续期险种明细失败（系统异常），请联系管理员");
        }
    }

    /**
     * 导出再保理赔信息
     *
     * @param response 响应对象
     * @param accountIds 所属账单Id集合
     */
    @Override
    public void exportDwsPrpClaim(HttpServletResponse response, Long[] accountIds){
        OutputStream os = null;
        final int pageSize = 10000;
        ExcelWriter excelWriter = null;
        try {
            if(accountIds == null || accountIds.length <= 0) {
                return;
            }
            List<DwsPrpAccountEntity> accountList = dwsPrpAccountMapper.selectDwsPrpAccountByIds(accountIds);
            if(CollUtil.isEmpty(accountList)) {
                log.info("再保导出保单登记理赔信息结束, 要导出的账单不存在, ids:{}", accountIds.toString());
                return;
            }
            Map<String, Object> headers = this.getHeaders();
            List<String> fieldList = (List<String>)headers.get("fieldList");
            List<List<String>> headerList = (List<List<String>>)headers.get("titleList");
            Map<String, Map<String, String>> fieldMapping = (Map<String, Map<String, String>>)headers.get("fieldMapping");
            String fileName = URLEncoder.encode("保单登记理赔信息" + BasicDataEnums.FileSuffix.EXCEL.getSuffix(), "utf-8");

            os = response.getOutputStream();
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            excelWriter = EasyExcel.write(os).registerWriteHandler(this.getCellStyle()).head(headerList).build();
            for(DwsPrpAccountEntity account : accountList) {
                int totalRows = dwsPrpClaimMapper.selectDwsPrpClaimCountByAccTransNo(account.getTransactionNo());
                if(totalRows <= 0) {
                    continue;
                }
                WriteSheet writeSheet = EasyExcel.writerSheet(account.getTransactionNo()).build();

                int totalPage = (totalRows+pageSize-1)/pageSize;
                for(int pageNo=1; pageNo<=totalPage; pageNo++) {
                    int startRows = (pageNo - 1) * pageSize;
                    List<DwsPrpClaimEntity> claimList = dwsPrpClaimMapper.selectDwsPrpClaimListByAccTransNo(account.getTransactionNo(), pageSize, startRows);
                    if(CollUtil.isNotEmpty(claimList)) {
                        excelWriter.write(this.getDatas(fieldList, fieldMapping, claimList), writeSheet);
                    }
                }
            }
        }catch(Exception e) {
            log.error("再保导出保单登记理赔信息出错, accountIds:{}, 错误原因:", accountIds.toString(), e);
        }finally {
            if(excelWriter != null) {
                excelWriter.finish();
            }
            if(os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {}
            }
        }
    }


    private HorizontalCellStyleStrategy getCellStyle(){
        HorizontalCellStyleStrategy handlerFontStyleStrategy = new HorizontalCellStyleStrategy();
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont writeFont = new WriteFont();
        writeFont.setFontHeightInPoints((short)12);
        headWriteCellStyle.setWriteFont(writeFont);
        handlerFontStyleStrategy.setHeadWriteCellStyle(headWriteCellStyle);
        return handlerFontStyleStrategy;
    }

    private Map<String, Object> getHeaders(){
        List<String> fieldList = new ArrayList<String>();
        List<List<String>> titleList = new ArrayList<List<String>>();
        Map<String, Map<String, String>> fieldMapping = MapUtil.newHashMap();
        Field[] fields = ReflectUtil.getFields(DwsPrpClaimDTO.class);
        for(Field field : fields) {
            Excel excel = field.getAnnotation(Excel.class);
            if(excel != null && (Excel.Type.ALL == excel.type() || Excel.Type.EXPORT == excel.type())) {
                fieldList.add(field.getName());
                titleList.add(Arrays.asList(excel.name()));
                if(StringUtils.isNotBlank(excel.readConverterExp())) {
                    fieldMapping.put(field.getName(),
                            Stream.of(excel.readConverterExp().split(",")).map(pair -> pair.split("="))
                                    .collect(Collectors.toMap(data -> StringUtils.trim(data[0]),
                                            data -> StringUtils.trim(data[1]))));
                }
                if(StringUtils.isNotBlank(excel.dictType())) {
                    List<SysDictData> dictList = DictUtils.getDictCache(excel.dictType());
                    if(CollUtil.isNotEmpty(dictList)) {
                        fieldMapping.put(field.getName(), dictList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel)));
                    }
                }
            }
        }
        return ImmutableMap.of("titleList", titleList, "fieldList", fieldList, "fieldMapping", fieldMapping);
    }

    private List<List<Object>> getDatas(List<String> fieldList, Map<String, Map<String, String>> fieldMapping, List<DwsPrpClaimEntity> claimList) {
        List<List<Object>> datas = new ArrayList<List<Object>>();
        for(DwsPrpClaimEntity claim : claimList) {
            List<Object> rowDatas = Lists.newArrayList();
            for(String field : fieldList) {
                Object dataValue = BeanUtil.getFieldValue(claim, field);
                if(dataValue != null && dataValue instanceof Date) {
                    rowDatas.add(DateUtil.formatDate((Date)dataValue));
                }else {
                    if(fieldMapping.containsKey(field)) {
                        String dataLabel = fieldMapping.get(field).get(String.valueOf(dataValue));
                        rowDatas.add(StringUtils.isNotEmpty(dataLabel) ? dataLabel : dataValue);
                    }else {
                        rowDatas.add(dataValue);
                    }
                }
            }
            datas.add(rowDatas);
        }
        return datas;
    }

}
