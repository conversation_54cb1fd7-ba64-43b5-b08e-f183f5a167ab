package com.reinsurance.service;

import java.util.List;
import java.util.Map;

import com.jd.lightning.common.core.domain.Result;
import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.dto.DwsPrpAccountDTO;
import com.reinsurance.dto.DwsPrpClaimDTO;
import com.reinsurance.query.DwsPrpClaimQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

/**
 * 再保理赔信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface IDwsPrpClaimService {
    
    /**
     * 查询再保理赔信息
     *
     * @param Id 再保理赔信息主键
     * @return 再保理赔信息
     */
    public DwsPrpClaimEntity selectDwsPrpClaimById(Long Id);

    /**
     * 查询再保理赔信息列表
     *
     * @param dwsPrpClaimQuery 再保理赔信息
     * @return 再保理赔信息集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 根据主键数组查询再保理赔信息列表
     *
     * @param Ids 主键数组
     * @return 再保理赔信息集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimByIds(Long[] Ids);

    /**
     * 新增再保理赔信息
     *
     * @param dwsPrpClaimDTO 再保理赔信息
     * @return 结果
     */
    public int insertDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO);

    /**
     * 批量新增再保理赔信息
     *
     * @param dwsPrpClaimList 再保理赔信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimEntity> dwsPrpClaimList);

    /**
     * 修改再保理赔信息
     *
     * @param dwsPrpClaimDTO 再保理赔信息
     * @return 结果
     */
    public int updateDwsPrpClaim(DwsPrpClaimDTO dwsPrpClaimDTO);

    /**
     * 批量删除再保理赔信息
     *
     * @param Ids 需要删除的再保理赔信息主键集合
     * @return 结果
     */
    public int deleteDwsPrpClaimByIds(Long[] Ids);

    /**
     * 删除再保理赔信息信息
     *
     * @param Id 再保理赔信息主键
     * @return 结果
     */
    public int deleteDwsPrpClaimById(Long Id);

    /**
     * 查询再保理赔信息总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpClaimCountByAccTransNo(String accTransNo);

    /**
     * 查询再保理赔信息列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保理赔信息列表
     */
    List<DwsPrpClaimEntity> selectDwsPrpClaimListByAccTransNo(String accTransNo, int pageSize, int startRows);

    /**
     * 删除再保理赔信息
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpClaimByAccTransNo(String accTransNo);

    /**
     * 批量插入再保理赔信息数据（已结算）
     * @param account
     * @return
     */
    public int insertDwsPrpClaimFormTrade(DwsPrpAccountDTO account);

    /**
     * 批量插入再保理赔信息数据（未结算）
     * @param account
     * @return
     */
    public int insertPreDwsPrpClaimFormTrade(DwsPrpAccountDTO account);

    /**
     * 查询再保理赔信息交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return 再保理赔信息ID集合
     */
    public List<Long> selectWaitSetTransNoDwsPrpClaimListByAccTransNo(String accTransNo, int limit);

    /**
     * 批量更新再保理赔信息交易编码（streamload方式）
     * @param contList
     * @return
     */
    public int updateDwsPrpClaimTransactionNo(List<Map<String, Object>> contList);

    /**
     * 导入再保理赔信息
     *
     * @param accountId 所属账单Id
     * @param file 导入文件
     * @return 结果
     */
    public Result importDwsPrpClaim(Long accountId, MultipartFile file);

    /**
     * 导出再保理赔信息
     *
     * @param response 响应对象
     * @param accountIds 所属账单Id集合
     */
    public void exportDwsPrpClaim(HttpServletResponse response, Long[] accountIds);
}
