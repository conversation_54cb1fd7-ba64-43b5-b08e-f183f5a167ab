package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import com.jd.lightning.common.core.domain.BaseEntity;
import lombok.EqualsAndHashCode;

/**
 * 临分数据对象 t_cedeout_facultative
 *
 * <AUTHOR>
 * @date 2024-05-31
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutFacultativeEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 被保险人客户号 */
    private String insuredNo;

    /** 被保人姓名 */
    private String insuredName;

    /** 保单号 */
    private String contNo;

    /** 险种编码 */
    private String riskCode;

    /** 给付责任编码 */
    private String getDutyCode;

    /** 再保责任编码 */
    private String liabilityCode;

    /** 核心临分结论 */
    private String coreConclusion;

    /** 再保方案编码 */
    private String programmeCode;

    /** 再保临分结论 */
    private String reinsuranceConclusion;

    /** 计算标记 */
    private Integer calcStatus;

    /** 数据标记 1=待处理 2=处理完毕 */
    private Integer dataStatus;

    /** EM加点 */
    private BigDecimal em;

    /** 临分确认书 */
    private String confirmingOrder;

    /** 保单创建日期 */
    private Date contMakeDate;

    /** 分保类型 */
    private Integer cedeoutType;

    /** 状态（0=有效,1=无效） */
    private Integer status;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
