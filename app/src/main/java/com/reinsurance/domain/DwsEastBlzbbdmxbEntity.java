package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.lightning.common.core.domain.BaseEntity;

/**
 * East比例再保保单明细对象 t_dws_east_blzbbdmxb
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsEastBlzbbdmxbEntity extends BaseEntity
{
	private static final long serialVersionUID = -7844938065285930296L;

	/** 自增主键 */
    private Long id;

    /** 账单流水号 */
    private String billLsh;

    /** 流水号 */
    private String lsh;

    /** 保险机构代码（唯一固定值：000166） */
    private String bxjgdm;

    /** 保险机构名称（唯一固定值：弘康人寿保险股份有限公司） */
    private String bxjgmc;

    /** 临分保单标志（0=否,1=是） */
    private String lfbdbz;

    /** 虚拟合同标志（0=否,1=是） */
    private String xnhtbz;

    /** 再保险合同号码 */
    private String zbxhthm;

    /** 再保险公司代码 */
    private String zbxgsdm;

    /** 再保险公司名称 */
    private String zbxgsmc;

    /** 政保合作业务标志（0=否,1=是） */
    private String zbhzywbz;

    /** 保单团个性质（个人,团体） */
    private String bdtgxz;

    /** 团体保单号 */
    private String ttbdh;

    /** 团单保险险种号码 */
    private String tdbxxzhm;

    /** 个人保单号 */
    private String grbdh;

    /** 个单保险险种号码 */
    private String gdbxxzhm;

    /** 险种编码 */
    private String xzbm;

    /** 责任代码 */
    private String zrdm;

    /** 责任名称 */
    private String zrmc;

        /** 责任分类 */
    private String zrfl;

    /** 业务类型(新契约；续期；保全；理赔；其他) */
    private String ywlx;

    /** 保单年度 */
    private Integer bdnd;

    /** 分保方式（枚举值：溢额（YRT）；成数（YRT）；成数溢额混合（YRT）；共保；超赔；超额赔付率） */
    private String fbfs;

    /** 风险保额 */
    private String fxbe;

    /** 分保风险保额 */
    private String fbfxbe;

    /** 自留额 */
    private String zle;

    /** 分保比例 */
    private String fbbl;

    /** 再保人参与份额比例(接受份额*100) */
    private String zbrcyfebl;

    /** 分保或摊回计算编号 */
    private String fbhthjsbh;

    /** 分保费 */
    private BigDecimal fbf;

    /** 分保佣金 */
    private BigDecimal fbyj;

    /** 退回分保费 */
    private BigDecimal thfbf;

    /** 退回分保佣金 */
    private BigDecimal thfbyj;

    /** 摊回退保金 */
    private BigDecimal thtbj;

    /** 摊回理赔款 */
    private BigDecimal thlpk;

    /** 赔案号 */
    private String pah;

    /** 摊回满期金 */
    private BigDecimal thmqj;

    /** 摊回生存金 */
    private BigDecimal thscj;

    /** 计算日期 */
    private String jsrq;

    /** 货币代码 */
    private String hbdm;

    /** 保单状态 */
    private String bdzt;

    /** 备注 */
    private String bz;

    /** 采集日期 */
    private String cjrq;
    
    /**数据报送批次号*/
    private String sjbspch;
    
    /**管理机构*/
    private String managecom;
    
    /** 数据来源（0=系统,1=人工） */
    private Integer dataSource;
    
    /** 推送状态（0=未推送,1=已推送） */
    private Integer pushStatus;

    /** 推送日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date pushDate;

    /** 推送人 */
    private String pushBy;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;
}
