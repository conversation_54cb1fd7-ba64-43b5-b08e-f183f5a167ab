package com.reinsurance.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 保单登记再保保全变更信息表 t_dws_prp_edor
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpEdorEntity extends PrpBaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 自增主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    private String TransactionNo;

    /** 保险机构代码（唯一固定值：000166） */
    @JsonProperty("CompanyCode")
    private String CompanyCode;

    /** 团体保单号 */
    @JsonProperty("GrpPolicyNo")
    private String GrpPolicyNo;

    /** 团单保险险种号码 */
    @JsonProperty("GrpProductNo")
    private String GrpProductNo;

    /** 个人保单号 */
    @JsonProperty("PolicyNo")
    private String PolicyNo;

    /** 个单保险险种号码 */
    @JsonProperty("ProductNo")
    private String ProductNo;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    private String GPFlag;

    /** 主险保险险种号码 */
    @JsonProperty("MainProductNo")
    private String MainProductNo;

    /** 主附险性质代码（1=主险,2=附加险,3=不区分） */
    @JsonProperty("MainProductFlag")
    private String MainProductFlag;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    private String ProductCode;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    private String LiabilityName;

    /** 责任分类代码 */
    @JsonProperty("Classification")
    private String Classification;

    /** 保险期限类型 */
    @JsonProperty("TermType")
    private String TermType;

    /** 管理机构代码 */
    @JsonProperty("ManageCom")
    private String ManageCom;

    /** 保单年度 */
    @JsonProperty("PolYear")
    private Integer PolYear;

    /** 签单日期 */
    @JsonProperty("SignDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date SignDate;

    /** 保险责任生效日期 */
    @JsonProperty("EffDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date EffDate;

    /** 保险责任终止日期 */
    @JsonProperty("InvalidDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date InvalidDate;

    /** 核保结论代码 */
    @JsonProperty("UWConclusion")
    private String UWConclusion;

    /** 保单状态代码 */
    @JsonProperty("PolStatus")
    private String PolStatus;

    /** 保单险种状态代码 */
    @JsonProperty("Status")
    private String Status;

    /** 基本保额 */
    @JsonProperty("BasicSumInsured")
    private BigDecimal BasicSumInsured;

    /** 风险保额 */
    @JsonProperty("RiskAmnt")
    private BigDecimal RiskAmnt;

    /** 保费 */
    @JsonProperty("Premium")
    private BigDecimal Premium;

    /** 保险账户价值 */
    @JsonProperty("AccountValue")
    private BigDecimal AccountValue;

    /** 临分标记（0=否,1=是） */
    @JsonProperty("FacultativeFlag")
    private String FacultativeFlag;

    /** 无名单标志 */
    @JsonProperty("AnonymousFlag")
    private String AnonymousFlag;

    /** 豁免险标志（0=否,1=是） */
    @JsonProperty("WaiverFlag")
    private String WaiverFlag;

    /** 所需豁免剩余保费 */
    @JsonProperty("WaiverPrem")
    private BigDecimal WaiverPrem;

    /** 期末现金价值 */
    @JsonProperty("FinalCashValue")
    private BigDecimal FinalCashValue;

    /** 期末责任准备金 */
    @JsonProperty("FinalLiabilityReserve")
    private BigDecimal FinalLiabilityReserve;

    /** 被保人数 */
    @JsonProperty("InsurePeoples")
    private Integer InsurePeoples;

    /** 被保人客户号 */
    @JsonProperty("InsuredNo")
    private String InsuredNo;

    /** 被保人姓名 */
    @JsonProperty("InsuredName")
    private String InsuredName;

    /** 被保人性别 */
    @JsonProperty("InsuredSex")
    private String InsuredSex;

    /** 被保人证件类型 */
    @JsonProperty("InsuredCertType")
    private String InsuredCertType;

    /** 被保人证件编码 */
    @JsonProperty("InsuredCertNo")
    private String InsuredCertNo;

    /** 职业代码 */
    @JsonProperty("OccupationType")
    private String OccupationType;

    /** 投保年龄 */
    @JsonProperty("AppntAge")
    private Integer AppntAge;

    /** 当前年龄 */
    @JsonProperty("PreAge")
    private Integer PreAge;

    /** 职业加费金额 */
    @JsonProperty("ProfessionalFee")
    private BigDecimal ProfessionalFee;

    /** 次标准体加费金额 */
    @JsonProperty("SubStandardFee")
    private BigDecimal SubStandardFee;

    /** EM加点 */
    @JsonProperty("EMRate")
    private BigDecimal EMRate;

    /** 建工险标志 */
    @JsonProperty("ProjectFlag")
    private String ProjectFlag;

    /** 批改受理号 */
    @JsonProperty("EndorAcceptNo")
    private String EndorAcceptNo;

    /** 批改通知书号 */
    @JsonProperty("EndorsementNo")
    private String EndorsementNo;

    /** 批改类型 */
    @JsonProperty("EdorType")
    private String EdorType;

    /** 批改生效日期 */
    @JsonProperty("EdorValiDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date EdorValiDate;

    /** 批改确认日期 */
    @JsonProperty("EdorConfDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date EdorConfDate;

    /** 批改金额 */
    @JsonProperty("EdorMoney")
    private BigDecimal EdorMoney;

    /** 批改前被保险人年龄 */
    @JsonProperty("PreInsuredAge")
    private Integer PreInsuredAge;

    /** 批改前基本保额 */
    @JsonProperty("PreBasicSumInsured")
    private BigDecimal PreBasicSumInsured;

    /** 批改前风险保额 */
    @JsonProperty("PreRiskAmnt")
    private BigDecimal PreRiskAmnt;

    /** 批改前分保保额 */
    @JsonProperty("PreReinsuranceAmnt")
    private BigDecimal PreReinsuranceAmnt;

    /** 批改前自留额 */
    @JsonProperty("PreRetentionAmount")
    private BigDecimal PreRetentionAmount;

    /** 批改前保费 */
    @JsonProperty("PrePremium")
    private BigDecimal PrePremium;

    /** 批改前账户价值 */
    @JsonProperty("PreAccountValue")
    private BigDecimal PreAccountValue;

    /** 批改前豁免保费 */
    @JsonProperty("PreWaiverPrem")
    private BigDecimal PreWaiverPrem;

    /** 工程面积变化 */
    @JsonProperty("ProjectAcreageChange")
    private BigDecimal ProjectAcreageChange;

    /** 工程造价变化 */
    @JsonProperty("ProjectCostChange")
    private BigDecimal ProjectCostChange;

    /** 分出标记（0=未达到溢额线保单,1=分出保单） */
    @JsonProperty("SaparateFlag")
    private String SaparateFlag;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    private String ReInsuranceContNo;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    private String ReinsurerName;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    private String ReinsurMode;

    /** 分保比例 */
    @JsonProperty("QuotaSharePercentage")
    private String QuotaSharePercentage;

    /** 分保保额变化 */
    @JsonProperty("ReinsuranceAmntChange")
    private BigDecimal ReinsuranceAmntChange;

    /** 自留额 */
    @JsonProperty("RetentionAmount")
    private BigDecimal RetentionAmount;

    /** 变更分保费 */
    @JsonProperty("ReinsurancePremiumChange")
    private BigDecimal ReinsurancePremiumChange;

    /** 变更分保佣金 */
    @JsonProperty("ReinsuranceCommssionChange")
    private BigDecimal ReinsuranceCommssionChange;

    /** 货币代码 */
    @JsonProperty("Currency")
    private String Currency;

    /** 分保计算日期 */
    @JsonProperty("ReComputationsDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ReComputationsDate;

    /** 账单归属日期 */
    @JsonProperty("AccountGetDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date AccountGetDate;

    /** 所属账单流水号 */
    @JsonProperty("AccTransNo")
    private String AccTransNo;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;

}
