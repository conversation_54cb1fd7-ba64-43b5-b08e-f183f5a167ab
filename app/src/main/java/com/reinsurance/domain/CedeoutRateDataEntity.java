package com.reinsurance.domain;

import java.math.BigDecimal;

import com.jd.lightning.common.core.domain.BaseEntity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 费率数据对象 t_cedeout_rate_data
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class CedeoutRateDataEntity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    /** 费率表编码 */
    private String rateCode;

    /** 费率表类型（0=分保费率表,1=分保佣金率表,2=折扣率,3=生命表） */
    private Integer rateType;

    /** 被保险人年龄 */
    private Long insuredAge;

    /** 被保险人性别（1=男,2=女） */
    private Integer insuredSex;

    /** 保单年度 */
    private Long policyYear;

    /** 费率值 */
    private BigDecimal rateValue;

    /** 费率单位 */
    private BigDecimal rateUnit;

    private String sellType;

    /** 承保类型 0:一般体,1:标准体,2:优选体,3:优选+体" */
    private Integer riskType;

    /** 投保年龄 */
    private Integer insuredAppAge;

    /** 被保险人职业类别 */
    private String insuredOccType;

    /** 被保险人数量 */
    private Integer insuredPeoples;

    /** 预留字段1 */
    private Long extends1;

    /** 预留字段2 */
    private String extends2;

    /** 预留字段3 */
    private BigDecimal extends3;

    /** 预留字段4 */
    private String extends4;

    /** 预留字段5 */
    private String extends5;

    /** 是否删除（0=未删除,1=已删除） */
    private Integer isDel;

}
