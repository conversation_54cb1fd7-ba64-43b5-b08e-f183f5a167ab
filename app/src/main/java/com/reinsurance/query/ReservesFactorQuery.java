package com.reinsurance.query;

import java.math.BigDecimal;
import com.jd.lightning.common.core.domain.BaseQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 准备金因子对象 t_reserves_factor
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
@ApiModel("准备金因子--查询参数")
@Data
@EqualsAndHashCode(callSuper=true)
public class ReservesFactorQuery extends BaseQuery
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 批次号 */
    private String batchNo;

    /** 版本号 */
    private Long version;

    @ApiModelProperty("优选体标识，字典：reserves_factor_inusred_mrtclst")
    private Integer inusredMrtclst;

    @ApiModelProperty("交费频率，字典：cont_pay_intv")
    private Integer payIntv;

    @ApiModelProperty("险种编码，字典：core_insurance_type")
    private String riskCode;

    @ApiModelProperty("因子类型，字典：reserves_factor_type")
    private String factorType;

    @ApiModelProperty("保障计划")
    private String planCode;

    @ApiModelProperty("风险类型")
    private String riskType;

    @ApiModelProperty("性别，字典：sys_user_sex")
    private Integer insuredSex;

    @ApiModelProperty("投保年龄")
    private Integer insuredAge;

    @ApiModelProperty("保险期间")
    private Integer insuYear;

    @ApiModelProperty("保险期间单位，字典：insu_payend_year_flag")
    private String insuYearFlag;

    @ApiModelProperty("交费期间")
    private Integer payendYear;

    @ApiModelProperty("交费期间单位，字典：cont_pay_intv")
    private String payendYearFlag;

    @ApiModelProperty("保单年度")
    private Integer policyYear;

    @ApiModelProperty("年交保费")
    private BigDecimal premium;

    @ApiModelProperty("保险金额")
    private BigDecimal amount;

    @ApiModelProperty("期初准备金")
    private BigDecimal initReserve;

    @ApiModelProperty("状态，字典：reserves_factor_status")
    private Integer status;

    /** 是否删除 */
    private Integer isDel;

}
