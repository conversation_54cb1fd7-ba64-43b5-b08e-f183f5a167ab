package com.reinsurance.query;

import java.util.Date;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 再保生存金信息查询对象
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class DwsPrpBenefitQuery extends PrpBaseQuery {
    
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @JsonProperty("Id")
    private Long Id;

    /** 交易编码 */
    @JsonProperty("TransactionNo")
    @Size(max = 64, message = "交易编码长度不能超过64个字符")
    private String TransactionNo;

    /** 保险机构代码,唯一固定值000166 */
    @JsonProperty("CompanyCode")
    @Size(max = 64, message = "保险机构代码长度不能超过64个字符")
    private String CompanyCode;

    /** 团体保单号 */
    @JsonProperty("GrpPolicyNo")
    @Size(max = 64, message = "团体保单号长度不能超过64个字符")
    private String GrpPolicyNo;

    /** 团个性质（01=个险,02=团险,99=其他） */
    @JsonProperty("GPFlag")
    @Size(max = 4, message = "团个性质长度不能超过4个字符")
    private String GPFlag;

    /** 个人保单号 */
    @JsonProperty("PolicyNo")
    @Size(max = 64, message = "个人保单号长度不能超过64个字符")
    private String PolicyNo;

    /** 个单保险险种号码 */
    @JsonProperty("ProductNo")
    @Size(max = 64, message = "个单保险险种号码长度不能超过64个字符")
    private String ProductNo;

    /** 保单年度 */
    @JsonProperty("PolYear")
    @Min(value = 1, message = "保单年度最小值为1")
    private Integer PolYear;

    /** 产品编码 */
    @JsonProperty("ProductCode")
    @Size(max = 64, message = "产品编码长度不能超过64个字符")
    private String ProductCode;

    /** 责任代码 */
    @JsonProperty("LiabilityCode")
    @Size(max = 64, message = "责任代码长度不能超过64个字符")
    private String LiabilityCode;

    /** 责任名称 */
    @JsonProperty("LiabilityName")
    @Size(max = 128, message = "责任名称长度不能超过128个字符")
    private String LiabilityName;

    /** 给付责任代码 */
    @JsonProperty("GetLiabilityCode")
    @Size(max = 64, message = "给付责任代码长度不能超过64个字符")
    private String GetLiabilityCode;

    /** 给付责任名称 */
    @JsonProperty("GetLiabilityName")
    @Size(max = 128, message = "给付责任名称长度不能超过128个字符")
    private String GetLiabilityName;

    /** 保险期限类型 */
    @JsonProperty("TermType")
    @Size(max = 4, message = "保险期限类型长度不能超过4个字符")
    private String TermType;

    /** 领取序号 */
    @JsonProperty("WDNo")
    @Size(max = 4, message = "领取序号长度不能超过4个字符")
    private String WDNo;

    /** 被保人客户号 */
    @JsonProperty("InsuredNo")
    @Size(max = 64, message = "被保人客户号长度不能超过64个字符")
    private String InsuredNo;

    /** 被保人姓名 */
    @JsonProperty("InsuredName")
    @Size(max = 64, message = "被保人姓名长度不能超过64个字符")
    private String InsuredName;

    /** 被保人性别 */
    @JsonProperty("InsuredSex")
    @Size(max = 4, message = "被保人性别长度不能超过4个字符")
    private String InsuredSex;

    /** 被保人证件类型 */
    @JsonProperty("InsuredCertType")
    @Size(max = 4, message = "被保人证件类型长度不能超过4个字符")
    private String InsuredCertType;

    /** 被保人证件编码 */
    @JsonProperty("InsuredCertNo")
    @Size(max = 64, message = "被保人证件编码长度不能超过64个字符")
    private String InsuredCertNo;

    /** 职业代码 */
    @JsonProperty("OccupationType")
    @Size(max = 12, message = "职业代码长度不能超过12个字符")
    private String OccupationType;

    /** 投保年龄 */
    @JsonProperty("AppntAge")
    @Min(value = 0, message = "投保年龄最小值为0")
    @Max(value = 150, message = "投保年龄最大值为150")
    private Integer AppntAge;

    /** 当前年龄 */
    @JsonProperty("PreAge")
    @Min(value = 0, message = "当前年龄最小值为0")
    @Max(value = 150, message = "当前年龄最大值为150")
    private Integer PreAge;

    /** 给付日期 */
    @JsonProperty("BenefitDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date BenefitDate;

    /** 分出标记（0=未达到溢额线保单,1=分出保单） */
    @JsonProperty("SaparateFlag")
    @Size(max = 4, message = "分出标记长度不能超过4个字符")
    private String SaparateFlag;

    /** 生存金类型 */
    @JsonProperty("BenefitClass")
    @Size(max = 12, message = "生存金类型长度不能超过12个字符")
    private String BenefitClass;

    /** 生存金领取金额 */
    @JsonProperty("BenefitAmount")
    @DecimalMin(value = "0", message = "生存金领取金额不能小于0")
    private BigDecimal BenefitAmount;

    /** 到账日期 */
    @JsonProperty("EnterAccDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date EnterAccDate;

    /** 再保险合同号码 */
    @JsonProperty("ReInsuranceContNo")
    @Size(max = 64, message = "再保险合同号码长度不能超过64个字符")
    private String ReInsuranceContNo;

    /** 再保险公司代码 */
    @JsonProperty("ReinsurerCode")
    @Size(max = 64, message = "再保险公司代码长度不能超过64个字符")
    private String ReinsurerCode;

    /** 再保险公司名称 */
    @JsonProperty("ReinsurerName")
    @Size(max = 256, message = "再保险公司名称长度不能超过256个字符")
    private String ReinsurerName;

    /** 分保方式（1=溢额,2=成数,3=成数溢额混合,4=超赔） */
    @JsonProperty("ReinsurMode")
    @Size(max = 4, message = "分保方式长度不能超过4个字符")
    private String ReinsurMode;

    /** 摊回金额 */
    @JsonProperty("BackBenefitAmount")
    @DecimalMin(value = "0", message = "摊回金额不能小于0")
    private BigDecimal BackBenefitAmount;

    /** 摊回日期 */
    @JsonProperty("BackDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date BackDate;

    /** 货币代码 */
    @JsonProperty("Currency")
    @Size(max = 4, message = "货币代码长度不能超过4个字符")
    private String Currency;

    /** 分保计算日期 */
    @JsonProperty("ReComputationsDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date ReComputationsDate;

    /** 账单归属日期 */
    @JsonProperty("AccountGetDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date AccountGetDate;

    /** 所属账单流水号 */
    @JsonProperty("AccTransNo")
    @Size(max = 64, message = "所属账单流水号长度不能超过64个字符")
    private String AccTransNo;

    /** 数据来源（0=系统,1=人工） */
    @JsonProperty("DataSource")
    private Integer DataSource;

    /** 推送状态（0=未推送,1=已推送） */
    @JsonProperty("PushStatus")
    private Integer PushStatus;

    /** 推送日期 */
    @JsonProperty("PushDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date PushDate;

    /** 推送人 */
    @JsonProperty("PushBy")
    @Size(max = 64, message = "推送人长度不能超过64个字符")
    private String PushBy;

    /** 备注 */
    @JsonProperty("Remark")
    @Size(max = 128, message = "备注长度不能超过128个字符")
    private String Remark;

    /** 是否删除(0=未删除,1=已删除) */
    @JsonProperty("IsDel")
    private Integer IsDel;
}
