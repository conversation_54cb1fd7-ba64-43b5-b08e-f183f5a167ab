package com.reinsurance.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.reinsurance.utils.ReinsuObjectUtil;
import com.jd.lightning.common.core.domain.entity.SysDictData;
import com.jd.lightning.common.utils.DateUtils;
import com.jd.lightning.system.service.ISysConfigService;
import com.jd.lightning.system.service.ISysDictTypeService;
import com.reinsurance.constant.RsConstant;
import com.reinsurance.domain.DwsReinsuPolicyLiabilityEntity;
import com.reinsurance.dto.*;
import com.reinsurance.enums.BasicDataEnums.DataTrackType;
import com.reinsurance.enums.BasicDataEnums.HandleStatus;
import com.reinsurance.enums.CedeoutEnums;
import com.reinsurance.query.*;
import com.reinsurance.service.*;
import com.reinsurance.utils.CedeoutUtils;
import com.reinsurance.utils.ReinsuJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 获取核心新单数据转换为再保待分出数据<br/>
 * 批处理执行逻辑<br/>
 * 1、查询所有虚拟合同<br/>
 * 2、查询再保方案<br/>
 * 3、查询方案对应的其他配置<br/>
 * 4、根据方案配置组装查询保单物化视图<br/>
 * 5、根据再保责任合并保单数据<br/>
 * 6、根据虚拟合同的分出公司进行数据复制<br/>
 * 7、批量插入分出记录表、分出日志
 * <AUTHOR>
 * @version 1.0
 * @date 2024-04-22 09:35:12
 */
@Slf4j
@Component("dwsPolicy2ReinsuTradeHandler")
public class DwsPolicy2ReinsuTradeHandler {
	
    @Autowired
	private ISysConfigService sysConfigService;
    
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    
    @Autowired
	private IRiskLiabilityService riskLiabilityService;
    
    @Autowired
    private IDwsReinsuTradeService dwsReinsuTradeService;

    @Autowired
    private ICedeoutProgrammeService cedeoutProgrammeService;

    @Autowired
    private ICedeoutVirtualContractService cedeoutVirtualContractService;
    
    @Autowired
    private IDwsReinsuPolicyLiabilityService dwsReinsuPolicyLiabilityService;

    public void handler(String batchNo, JobParamDTO jobParam){
        try{
            log.info("再保新单分出批处理开始, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
            /**查询虚拟合同*/
            CedeoutVirtualContractQuery virtualContractQuery = new CedeoutVirtualContractQuery();
            virtualContractQuery.setStatus(CedeoutEnums.状态_有效.getValue());
            virtualContractQuery.getParams().put("effectiveDate", DateUtils.getNowDate());
            List<CedeoutVirtualContractDTO> virtualContracts = cedeoutVirtualContractService.selectCedeoutVirtualContractList(virtualContractQuery);
            if(CollUtil.isEmpty(virtualContracts)){
                log.info("再保新单分出批处理结束, 未查询到虚拟合同, batchNo:{}, virtualContractQuery:{}", batchNo, ReinsuJsonUtil.toJsonString(virtualContractQuery));
                return;
            }

			Map<String, RiskLiabilityDTO> riskLiabilityHash = riskLiabilityService.selectRiskLiabilityHash();
			if(MapUtil.isEmpty(riskLiabilityHash)) {
				log.info("再保新单分出批处理结束, 未查询到再保责任配置, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
				return;
			}
			
			Map<String, String> unpayPremiumRiskCodeMap = MapUtil.newHashMap();
			List<SysDictData> unpayPremiumRiskCodeList = sysDictTypeService.selectDictDataByType(RsConstant.unpayPremiumRiskCode);
			if(CollUtil.isNotEmpty(unpayPremiumRiskCodeList)) {
				unpayPremiumRiskCodeMap = unpayPremiumRiskCodeList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
			}
			
			int limit = Integer.valueOf(sysConfigService.selectConfigByKey(RsConstant.jobOnceMaxRows));
			Map<String, CedeoutLiabilityContractRelDTO> liabilityContractHash = cedeoutProgrammeService.getLiabilityContractHash();
            for(CedeoutVirtualContractDTO virtualContract : virtualContracts){
                /**查询虚拟合同的再保公司、分出比例*/
                CedeoutVirtualCompanyQuery virtualCompanyQuery = new CedeoutVirtualCompanyQuery();
                virtualCompanyQuery.setStatus(CedeoutEnums.状态_有效.getValue());
                virtualCompanyQuery.setVirtualCode(virtualContract.getVirtualCode());
                List<CedeoutVirtualCompanyDTO> virtualCompanys = cedeoutVirtualContractService.selectCedeoutVirtualCompanyList(virtualCompanyQuery);
                if(CollUtil.isEmpty(virtualCompanys)){
                    log.info("再保新单分出批处理, 虚拟合同未查询到再保公司, batchNo:{}, virtualCode:{}", batchNo, virtualContract.getVirtualCode());
                    continue;
                }
                /**查询虚拟合同的再保方案*/
                CedeoutProgrammeQuery programmeQuery = new CedeoutProgrammeQuery();
                programmeQuery.setStatus(CedeoutEnums.状态_有效.getValue());
                programmeQuery.setVirtualCode(virtualContract.getVirtualCode());
                programmeQuery.setBackTrackStatus(DataTrackType.常规数据.getCode());
                programmeQuery.setCedeoutType(CedeoutEnums.分保方案类型_正常分保.getValue());
                programmeQuery.setPayIntv(CedeoutEnums.缴费频率_年交.getValue());
                List<CedeoutProgrammeDTO> programmes = cedeoutProgrammeService.selectCedeoutProgrammeList(programmeQuery);
                if(CollUtil.isEmpty(programmes)){
                    log.info("再保新单分出批处理, 虚拟合同未查询到再保方案, batchNo:{}, virtualCode:{}", batchNo, virtualContract.getVirtualCode());
                    continue;
                }
                log.info("再保新单分出批处理执行, 开始循环方案, batchNo:{}, virtualCode:{}", batchNo, virtualContract.getVirtualCode());

                for(CedeoutProgrammeDTO programme : programmes){
                    /**获取方案使用的费率配置*/
                    CedeoutProgrammeRateQuery programmeRateQuery = new CedeoutProgrammeRateQuery();
                    programmeRateQuery.setProgrammeCode(programme.getProgrammeCode());
                    programmeRateQuery.setVirtualCode(virtualContract.getVirtualCode());
                    programmeRateQuery.setStatus(CedeoutEnums.状态_有效.getValue());
                    List<CedeoutProgrammeRateDTO> programmeRates = cedeoutProgrammeService.selectCedeoutProgrammeRateList(programmeRateQuery);
                    if(CollUtil.isEmpty(programmeRates)){
                        log.info("再保新单分出批处理, 再保方案未查询到费率, batchNo:{}, programmeCode:{}", batchNo, programme.getProgrammeCode());
                        continue;
                    }
                    /**获取方案使用的再保责任*/
                    CedeoutProgrammeLiabilityQuery programmeLiabilityQuery = new CedeoutProgrammeLiabilityQuery();
                    programmeLiabilityQuery.setRiskCode(jobParam.getRiskCode());
                    programmeLiabilityQuery.setProgrammeCode(programme.getProgrammeCode());
                    programmeLiabilityQuery.setVirtualCode(virtualContract.getVirtualCode());
                    programmeLiabilityQuery.setStatus(CedeoutEnums.状态_有效.getValue());
                    List<CedeoutProgrammeLiabilityDTO> programmeLiabilitys = cedeoutProgrammeService.selectCedeoutProgrammeLiabilityList(programmeLiabilityQuery);
                    if(CollUtil.isEmpty(programmeLiabilitys)){
                        log.info("再保新单分出批处理, 再保方案未查询到再保责任, batchNo:{}, programmeCode:{}", batchNo, programme.getProgrammeCode());
                        continue;
                    }
                    
                    log.info("再保新单分出批处理执行, 开始匹配保单, batchNo:{}, programmeCode:{}", batchNo, programme.getProgrammeCode());
                    /**匹配保单给付责任数据，合并为再保责任数据（insert into t_dws_reinsure_policy_liability select form t_dws_cont_history where ...）*/
                    DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityEntity = new DwsReinsuPolicyLiabilityEntity();
                    dwsReinsuPolicyLiabilityEntity.setBatchNo(batchNo);
                    dwsReinsuPolicyLiabilityEntity.setContMonth(BigDecimal.ZERO.intValue());
                    dwsReinsuPolicyLiabilityEntity.setCedeoutType(programme.getCedeoutType());
                    dwsReinsuPolicyLiabilityEntity.setProgrammeCode(programme.getProgrammeCode());
                    dwsReinsuPolicyLiabilityEntity.setBusiType(CedeoutEnums.业务类型_新单.getValue());
                    dwsReinsuPolicyLiabilityEntity.getParams().put("liabilitys", programmeLiabilitys);
                    if(jobParam.getCalcType() == CedeoutEnums.执行方式_手动.getValue()) {//手动时按指定起始日期
                    	dwsReinsuPolicyLiabilityEntity.getParams().put(RsConstant.paramsStartDate, jobParam.getStartDate());
                    }
                    dwsReinsuPolicyLiabilityEntity.getParams().put(RsConstant.paramsEndDate, jobParam.getEndDate());
                    int insertReinsuLiabilityRows = dwsReinsuPolicyLiabilityService.insertBatchDwsReinsuPolicyLiabilityNew(dwsReinsuPolicyLiabilityEntity);
                    if(insertReinsuLiabilityRows <= 0) {
                    	log.info("再保新单分出批处理, 再保方案未查询到保单, batchNo:{}, programmeCode:{}, dwsPolicyQuery:{}", batchNo, programme.getProgrammeCode(), ReinsuJsonUtil.toJsonString(dwsReinsuPolicyLiabilityEntity));
                    }

                    log.info("再保新单分出批处理, 再保方案开始查询保单, batchNo:{}, programmeCode:{}", batchNo, programme.getProgrammeCode());
                    /**查询待处理的再保方案责任数据*/
                    DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiabilityQuery = new DwsReinsuPolicyLiabilityEntity();
                    dwsReinsuPolicyLiabilityQuery.getParams().put("limit", limit);
                    dwsReinsuPolicyLiabilityQuery.setCedeoutType(programme.getCedeoutType());
                    dwsReinsuPolicyLiabilityQuery.setProgrammeCode(programme.getProgrammeCode());
                    dwsReinsuPolicyLiabilityQuery.setHandleStatus(HandleStatus.未处理.getCode());
                    dwsReinsuPolicyLiabilityQuery.setBusiType(CedeoutEnums.业务类型_新单.getValue());
                    dwsReinsuPolicyLiabilityQuery.setBackTrackData(DataTrackType.常规数据.getCode());
                    
                    while(true) {//循环处理，查询到的数据小于limit时跳出循环
                    	List<DwsReinsuPolicyLiabilityEntity> dwsReinsuPolicyLiabilityList = dwsReinsuPolicyLiabilityService.selectWaitReinsuPolicyLiabilityList(dwsReinsuPolicyLiabilityQuery);
        				if(CollUtil.isEmpty(dwsReinsuPolicyLiabilityList)) {
        					log.info("再保新单分出批处理, 再保方案未查询到保单, batchNo:{}, programmeCode:{}, dwsReinsuPolicyLiabilityQuery:{}", batchNo, programme.getProgrammeCode(), ReinsuJsonUtil.toJsonString(dwsReinsuPolicyLiabilityQuery));
                            break;
        				}
        				List<DwsReinsuTradeDTO> dwsReinsuTradeList = new ArrayList<DwsReinsuTradeDTO>();
        				for(DwsReinsuPolicyLiabilityEntity dwsReinsuPolicyLiability : dwsReinsuPolicyLiabilityList) {
        					DwsReinsuTradeDTO dwsReinsuTrade = ReinsuObjectUtil.convertModel(dwsReinsuPolicyLiability, DwsReinsuTradeDTO.class);
                            RiskLiabilityDTO riskLiability = riskLiabilityHash.get(dwsReinsuTrade.getRiskCode() + RsConstant.splicing + dwsReinsuTrade.getLiabilityCode());
                            dwsReinsuTrade.setTaxRate(riskLiability == null ? BigDecimal.ZERO : riskLiability.getTaxRate());
                            dwsReinsuTrade.setReservesType(riskLiability == null ? null : riskLiability.getReserveType());
                            
                            dwsReinsuTrade.setBatchNo(batchNo);
                            dwsReinsuTrade.setGetDate(DateUtils.getNowDate());
                            dwsReinsuTrade.setGetTime(DateUtils.getTime().substring(11));
                            dwsReinsuTrade.setAccountDate(dwsReinsuTrade.getGetDate());
                            dwsReinsuTrade.setAccountPeriod(Integer.valueOf(DateUtils.dateTime().substring(0, 6)));
                            dwsReinsuTrade.setDataType(CedeoutEnums.数据类型_分出.getValue());
                            dwsReinsuTrade.setBusiType(CedeoutEnums.业务类型_新单.getValue());
                            dwsReinsuTrade.setDataCopy(CedeoutEnums.数据_原始.getValue());
                            dwsReinsuTrade.setBackTrackData(DataTrackType.常规数据.getCode());
                            dwsReinsuTrade.setCedeoutCount(BigDecimal.ONE.intValue());
                            dwsReinsuTrade.setProgrammeCode(programme.getProgrammeCode());
                            dwsReinsuTrade.setProgrammeName(programme.getProgrammeName());
                            dwsReinsuTrade.setCompanyCode(virtualCompanys.get(0).getCompanyCode());
                            dwsReinsuTrade.setCompanyName(virtualCompanys.get(0).getCompanyName());
                            dwsReinsuTrade.setDataGroupNo(CedeoutUtils.getGroupNo(dwsReinsuTrade.getBusiType(), dwsReinsuTrade.getPolNo(), dwsReinsuTrade.getLiabilityCode()));
                            dwsReinsuTrade.setTotalPremium(CedeoutUtils.getTotalPremium(dwsReinsuTrade));
                            dwsReinsuTrade.setPayPeriods(CedeoutUtils.getPayPeriods(dwsReinsuTrade));
                            dwsReinsuTrade.setUnPayPeriods(CedeoutUtils.getUnPayPeriods(dwsReinsuTrade));
                            dwsReinsuTrade.setInPayPeriods(dwsReinsuTrade.getPayPeriods() - dwsReinsuTrade.getUnPayPeriods());
                            dwsReinsuTrade.setUnPayPremium(CedeoutUtils.getUnPayPremium(dwsReinsuTrade));
                            dwsReinsuTrade.setAvailableAmount(dwsReinsuTrade.getAmount());
                            dwsReinsuTrade.setSumPayMoney(dwsReinsuTrade.getTotalPremium().multiply(BigDecimal.valueOf(dwsReinsuTrade.getInPayPeriods())).setScale(2, RoundingMode.HALF_UP));
                            try {
                        		if(unpayPremiumRiskCodeMap.containsKey(dwsReinsuTrade.getRiskCode())) {//计算未来应交保费特殊处理
                        			DwsReinsuTradeQuery mainPolReinsuTradeQuery = new DwsReinsuTradeQuery();
                        			mainPolReinsuTradeQuery.setPolNo(dwsReinsuTrade.getMainPolNo());
                        			mainPolReinsuTradeQuery.setBusiType(dwsReinsuTrade.getBusiType());
                        			mainPolReinsuTradeQuery.setContYear(dwsReinsuTrade.getContYear());
                        			mainPolReinsuTradeQuery.setContMonth(dwsReinsuTrade.getContMonth());
                        			mainPolReinsuTradeQuery.setBackTrackData(DataTrackType.常规数据.getCode());
                        			DwsReinsuTradeDTO mainPolReinsuTrade = dwsReinsuTradeService.selectOneVirtualReinsuTrade(mainPolReinsuTradeQuery);
    	                    		if(mainPolReinsuTrade == null) {
    	                    			log.info("再保新单分出批处理计算附加险未来应交保费未查询到主险信息, liabilityId:{}, riskCode:{}, polNo:{}, 错误原因:", dwsReinsuPolicyLiability.getId(), dwsReinsuTrade.getRiskCode(), dwsReinsuTrade.getPolNo());
    	                    		}else {
    	                    			mainPolReinsuTrade.setTotalPremium(CedeoutUtils.getTotalPremium(mainPolReinsuTrade));
    	                    			mainPolReinsuTrade.setPayPeriods(CedeoutUtils.getPayPeriods(mainPolReinsuTrade));
    	                    			mainPolReinsuTrade.setUnPayPeriods(CedeoutUtils.getUnPayPeriods(mainPolReinsuTrade));
    	                    			mainPolReinsuTrade.setInPayPeriods(mainPolReinsuTrade.getPayPeriods() - mainPolReinsuTrade.getUnPayPeriods());
    	                    			mainPolReinsuTrade.setUnPayPremium(CedeoutUtils.getUnPayPremium(mainPolReinsuTrade));
    	                    			dwsReinsuTrade.setUnPayPremium(dwsReinsuTrade.getUnPayPremium().add(mainPolReinsuTrade.getUnPayPremium()));
    	                    		}
                        		}
                        	}catch(Exception e) {
                        		log.error("再保新单分出批处理计算附加险未来应交保费出错, liabilityId:{}, riskCode:{}, polNo:{}, 错误原因:", dwsReinsuPolicyLiability.getId(), dwsReinsuTrade.getRiskCode(), dwsReinsuTrade.getPolNo(), e);
                        	}
                            dwsReinsuTrade.setRsPayIntv(programme.getPayIntv());
                            dwsReinsuTrade.setCalcType(jobParam.getCalcType());
                            dwsReinsuTrade.setCedeoutWay(programme.getCedeoutWay());
                            dwsReinsuTrade.setCedeoutMode(programme.getCedeoutMode());
                            dwsReinsuTrade.setCedeoutType(programme.getCedeoutType());
                        	dwsReinsuTrade.setCalcStatus(CedeoutEnums.计算状态_未计算.getValue());
                        	dwsReinsuTrade.setReturnStatus(CedeoutEnums.摊回状态_未摊回.getValue());
                        	dwsReinsuTrade.setAcceptCopies(virtualCompanys.get(0).getCedeoutScale());
                        	if(CedeoutEnums.溢额类型_自留额.getValue() == programme.getFirstExcessType()) {
                        		dwsReinsuTrade.setProgrammeSelfAmount(programme.getFirstExcessValue());
                        	}else if(CedeoutEnums.溢额类型_自留额.getValue() == programme.getSecondExcessType()){
                        		dwsReinsuTrade.setProgrammeSelfAmount(programme.getSecondExcessValue());
                        	}else {
                        		dwsReinsuTrade.setProgrammeSelfAmount(dwsReinsuTrade.getAmount());
                        	}
                        	dwsReinsuTrade.setProgrammeSelfScale(programme.getSelfScale());
                            dwsReinsuTrade.setAddupRiskCode(programme.getAddupRiskCode());
                            dwsReinsuTrade.setAddupRiskName(programme.getAddupRiskName());
                            dwsReinsuTrade.setAddupAmountType(programme.getAddupAmountType());
                            
                            CedeoutProgrammeRateDTO programmeRate = programmeRates.stream().filter(rate -> rate.getCompanyCode().equals(dwsReinsuTrade.getCompanyCode())).collect(Collectors.toList()).get(0);
                            dwsReinsuTrade.setRateCode(programmeRate.getCedeoutRateCode());
                            dwsReinsuTrade.setComRateCode(programmeRate.getCommissionRateCode());
                            dwsReinsuTrade.setDisRateCode(programmeRate.getDisRateCode());
                            dwsReinsuTrade.setCreateTime(DateUtils.getNowDate());
                            dwsReinsuTrade.setCreateBy(jobParam.getExecutor());

                            String liabilityContractKey = CedeoutUtils.getLiabilityContractKey(dwsReinsuTrade);
                            if(liabilityContractHash.containsKey(liabilityContractKey)){
                            	CedeoutLiabilityContractRelDTO liabilityContract = liabilityContractHash.get(liabilityContractKey);
                            	dwsReinsuTrade.setContractCode(liabilityContract.getContractCode());
                            	dwsReinsuTrade.setContractName(liabilityContract.getContractName());
                            	dwsReinsuTrade.setMainContractCode(liabilityContract.getMainContractCode());
                            	dwsReinsuTrade.setMainContractName(liabilityContract.getMainContractName());
                            }
                            dwsReinsuTradeList.add(dwsReinsuTrade);
        				}
        				log.info("再保新单分出批处理执行, 合并分出保单完毕, batchNo:{}, programmeCode:{}, srcDwsReinsuTradeList:{}", batchNo, programme.getProgrammeCode(), dwsReinsuTradeList.size());
        				
        				/**根据虚拟合同的再保公司数进行分出保单复制*/
        				List<DwsReinsuTradeDTO> copyDwsReinsuTradeList = new ArrayList<DwsReinsuTradeDTO>();
                        for(int compnayIndex=1; compnayIndex<virtualCompanys.size(); compnayIndex++){
                            List<DwsReinsuTradeDTO> compnayDwsReinsuTradeList = BeanUtil.copyToList(dwsReinsuTradeList, DwsReinsuTradeDTO.class);
                            CedeoutVirtualCompanyDTO virtualCompany = virtualCompanys.get(compnayIndex);
                            compnayDwsReinsuTradeList.forEach(copyDwsReinsuTrade -> {
                            	copyDwsReinsuTrade.setDataCopy(CedeoutEnums.数据_复制.getValue());
                            	copyDwsReinsuTrade.setCompanyCode(virtualCompany.getCompanyCode());
                            	copyDwsReinsuTrade.setCompanyName(virtualCompany.getCompanyName());
                            	copyDwsReinsuTrade.setAcceptCopies(virtualCompany.getCedeoutScale());
                            	CedeoutProgrammeRateDTO copyProgrammeRate = programmeRates.stream().filter(rate -> rate.getCompanyCode().equals(copyDwsReinsuTrade.getCompanyCode())).collect(Collectors.toList()).get(0);
                            	copyDwsReinsuTrade.setRateCode(copyProgrammeRate.getCedeoutRateCode());
                            	copyDwsReinsuTrade.setComRateCode(copyProgrammeRate.getCommissionRateCode());
                            	copyDwsReinsuTrade.setDisRateCode(copyProgrammeRate.getDisRateCode());

                            	String liabilityContractKey = CedeoutUtils.getLiabilityContractKey(copyDwsReinsuTrade);
                                if(liabilityContractHash.containsKey(liabilityContractKey)){
                                	CedeoutLiabilityContractRelDTO liabilityContract = liabilityContractHash.get(liabilityContractKey);
                                    copyDwsReinsuTrade.setContractCode(liabilityContract.getContractCode());
                                    copyDwsReinsuTrade.setContractName(liabilityContract.getContractName());
                                    copyDwsReinsuTrade.setMainContractCode(liabilityContract.getMainContractCode());
                                    copyDwsReinsuTrade.setMainContractName(liabilityContract.getMainContractName());
                                }
                            });
                            copyDwsReinsuTradeList.addAll(compnayDwsReinsuTradeList);
                        }
                        
                        if(CollUtil.isNotEmpty(copyDwsReinsuTradeList)) {
                        	dwsReinsuTradeList.addAll(copyDwsReinsuTradeList);
                        }
                        log.info("再保新单分出批处理执行, 复制分出保单完毕, batchNo:{}, programmeCode:{}, copyDwsReinsuTradeList:{}", batchNo, programme.getProgrammeCode(), copyDwsReinsuTradeList.size());
                        
                        boolean isSuccess = dwsReinsuPolicyLiabilityService.updateBatchhDwsReinsuPolicyLiability(HandleStatus.处理成功.getCode(), null, dwsReinsuPolicyLiabilityList);
                		if(isSuccess) {//保存分出记录
                			isSuccess = dwsReinsuTradeService.insertBatchDwsReinsuTrade(dwsReinsuTradeList) == dwsReinsuTradeList.size() ? true : false;
                		}
                		if(!isSuccess) {
                			dwsReinsuPolicyLiabilityService.updateBatchhDwsReinsuPolicyLiability(HandleStatus.处理失败.getCode(), "系统异常", dwsReinsuPolicyLiabilityList);
                		}
                        log.info("再保新单分出批处理执行, 生成分出保单完毕, batchNo:{}, programmeCode:{}, dwsReinsuTradeList:{}", batchNo, programme.getProgrammeCode(), dwsReinsuTradeList.size());
                        
                        if(dwsReinsuTradeList.size() < limit) {//已处理完成
        					break;
        				}
                    }
                }
            }
            log.error("再保新单分出批处理完成, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam));
        }catch(Exception e){
            log.error("再保新单分出批处理出错, batchNo:{}, jobParam:{}", batchNo, ReinsuJsonUtil.toJsonString(jobParam), e);
        }
    }
    
}
