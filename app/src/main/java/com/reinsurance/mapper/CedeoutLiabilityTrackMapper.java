package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutLiabilityTrackEntity;
import com.reinsurance.query.CedeoutLiabilityTrackQuery;

/**
 * 分保责任轨迹Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-20
 */
public interface CedeoutLiabilityTrackMapper 
{
    /**
     * 查询分保责任轨迹
     * 
     * @param trackId 分保责任轨迹主键
     * @return 分保责任轨迹
     */
    public CedeoutLiabilityTrackEntity selectCedeoutLiabilityTrackByTrackId(Long trackId);

    /**
     * 查询分保责任轨迹列表
     * 
     * @param cedeoutLiabilityTrackQuery 分保责任轨迹
     * @return 分保责任轨迹集合
     */
    public List<CedeoutLiabilityTrackEntity> selectCedeoutLiabilityTrackList(CedeoutLiabilityTrackQuery cedeoutLiabilityTrackQuery);

    /**
     * 新增分保责任轨迹
     * 
     * @param cedeoutLiabilityTrack 分保责任轨迹
     * @return 结果
     */
    public int insertCedeoutLiabilityTrack(CedeoutLiabilityTrackEntity cedeoutLiabilityTrack);

    /**
     * 修改分保责任轨迹
     * 
     * @param cedeoutLiabilityTrack 分保责任轨迹
     * @return 结果
     */
    public int updateCedeoutLiabilityTrack(CedeoutLiabilityTrackEntity cedeoutLiabilityTrack);

    /**
     * 删除分保责任轨迹
     * 
     * @param trackId 分保责任轨迹主键
     * @return 结果
     */
    public int deleteCedeoutLiabilityTrackByTrackId(Long trackId);

    /**
     * 批量删除分保责任轨迹
     * 
     * @param trackIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCedeoutLiabilityTrackByTrackIds(Long[] trackIds);
}
