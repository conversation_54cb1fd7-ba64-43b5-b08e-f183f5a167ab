package com.reinsurance.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpBenefitEntity;
import com.reinsurance.query.DwsPrpBenefitQuery;

/**
 * 再保生存金信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface DwsPrpBenefitMapper {
    
    /**
     * 查询再保生存金信息
     *
     * @param Id 再保生存金信息主键
     * @return 再保生存金信息
     */
    public DwsPrpBenefitEntity selectDwsPrpBenefitById(Long Id);

    /**
     * 查询再保生存金信息列表
     *
     * @param dwsPrpBenefitQuery 再保生存金信息
     * @return 再保生存金信息集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitList(DwsPrpBenefitQuery dwsPrpBenefitQuery);

    /**
     * 根据主键数组查询再保生存金信息列表
     *
     * @param Ids 主键数组
     * @return 再保生存金信息集合
     */
    public List<DwsPrpBenefitEntity> selectDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 新增再保生存金信息
     *
     * @param dwsPrpBenefitEntity 再保生存金信息
     * @return 结果
     */
    public int insertDwsPrpBenefit(DwsPrpBenefitEntity dwsPrpBenefitEntity);

    /**
     * 批量新增再保生存金信息
     *
     * @param dwsPrpBenefitList 再保生存金信息列表
     * @return 结果
     */
    public int insertBatchDwsPrpBenefit(List<DwsPrpBenefitEntity> dwsPrpBenefitList);

    /**
     * 修改再保生存金信息
     *
     * @param dwsPrpBenefitEntity 再保生存金信息
     * @return 结果
     */
    public int updateDwsPrpBenefit(DwsPrpBenefitEntity dwsPrpBenefitEntity);

    /**
     * 删除再保生存金信息
     *
     * @param Id 再保生存金信息主键
     * @return 结果
     */
    public int deleteDwsPrpBenefitById(Long Id);

    /**
     * 批量删除再保生存金信息
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpBenefitByIds(Long[] Ids);

    /**
     * 查询再保生存金信息总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpBenefitCountByAccTransNo(String accTransNo);

    /**
     * 查询再保生存金信息列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保生存金信息列表
     */
    List<DwsPrpBenefitEntity> selectDwsPrpBenefitListByAccTransNo(@Param("accTransNo")String accTransNo, @Param("pageSize")int pageSize, @Param("startRows")int startRows);

    /**
     * 删除再保生存金信息
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpBenefitByAccTransNo(String accTransNo);

    /**
     * 更新再保生存金信息推送状态
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param accTransNos 账单交易编码集合
     * @return 结果
     */
    public int updateDwsPrpBenefitPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("accTransNos") List<String> accTransNos);
}
