package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.dto.DwsPrpAccountDTO;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsReinsuSettleBillEntity;
import com.reinsurance.dto.DwsEastZbzdxxbDTO;
import com.reinsurance.query.DwsReinsuSettleBillQuery;

/**
 * 再保结算账单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface DwsReinsuSettleBillMapper {
	
    /**
     * 查询再保结算账单
     *
     * @param id 再保结算账单主键
     * @return 再保结算账单
     */
    DwsReinsuSettleBillEntity selectDwsReinsuSettleBillById(Long id);

    /**
     * 查询再保结算账单
     *
     * @param id 再保结算账单主键
     * @return 再保结算账单
     */
    List<DwsReinsuSettleBillEntity> selectDwsReinsuSettleBillByIds(Long [] ids);
    
    /**
     * 查询再保结算账单
     *
     * @param billNo 账单号
     * @return 再保结算账单
     */
    DwsReinsuSettleBillEntity selectDwsReinsuSettleBillByBillNo(String billNo);
    
    /**
     * 查询再保结算账单列表
     *
     * @param dwsReinsuSettleBillQuery 再保结算账单
     * @return 再保结算账单集合
     */
    List<DwsReinsuSettleBillEntity> selectDwsReinsuSettleBillList(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery);

    /**
     * 检查是否存在再保公司、再保合同、业务起始截止日期交叉的账单
     * @param dwsReinsuSettleBillQuery
     * @return
     */
    int selectDwsReinsuSettleExists(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery);
    
    /**
     * 查询再保结算账单
     *
     * @param tradeId 分出摊回明细Id
     * @return 再保结算账单
     */
    DwsReinsuSettleBillEntity selectDwsReinsuSettleBillByTradeId(@Param("tradeId") Long tradeId);
    
    /**
     * 查询再保结算账单
     *
     * @param tradeIds 分出摊回明细Id集合
     * @return 再保结算账单
     */
    List<DwsReinsuSettleBillEntity> selectDwsReinsuSettleBillByTradeIds(@Param("tradeIds") List<Long> tradeIds);
    
    /**
     * 批量新增再保结算账单
     *
     * @param dwsReinsuSettleBillList 再保结算账单
     * @return 结果
     */
    int insertBatchDwsReinsuSettleBill(@Param("dwsReinsuSettleBillList") List<DwsReinsuSettleBillEntity> dwsReinsuSettleBillList);
    
    /**
     * 新增再保结算账单
     *
     * @param dwsReinsuSettleBillEntity 再保结算账单
     * @return 结果
     */
    int insertDwsReinsuSettleBill(DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity);

    /**
     * 修改再保结算账单
     *
     * @param dwsReinsuSettleBillEntity 再保结算账单
     * @return 结果
     */
    int updateDwsReinsuSettleBill(DwsReinsuSettleBillEntity dwsReinsuSettleBillEntity);

    /**
     * 删除再保结算账单
     *
     * @param id 再保结算账单主键
     * @return 结果
     */
    int deleteDwsReinsuSettleBillById(Long id);

    /**
     * 批量删除再保结算账单
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDwsReinsuSettleBillByIds(Long[] ids);
    
    /**
     * 查询再保结算账单转换为East账单
     *
     * @param dwsReinsuSettleBillQuery 再保结算账单Query
     * @return East账单DTO集合
     */
    List<DwsEastZbzdxxbDTO> selectSettleBillAsZbzdxxb(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery);

    /**
     * 查询再保结算账单转换为保单登记账单
     *
     * @param dwsReinsuSettleBillQuery 再保结算账单Query
     * @return 保单登记账单DTO集合
     */
    List<DwsPrpAccountDTO> selectSettleBillAsPrpAccount(DwsReinsuSettleBillQuery dwsReinsuSettleBillQuery);
}
