package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.dto.DwsPrpAccountDTO;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpEdorEntity;
import com.reinsurance.query.DwsPrpEdorQuery;

/**
 * 再保保全险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface DwsPrpEdorMapper {
    
    /**
     * 查询再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 再保保全险种明细
     */
    public DwsPrpEdorEntity selectDwsPrpEdorById(Long Id);

    /**
     * 查询再保保全险种明细列表
     *
     * @param dwsPrpEdorQuery 再保保全险种明细
     * @return 再保保全险种明细集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorList(DwsPrpEdorQuery dwsPrpEdorQuery);

    /**
     * 根据主键数组查询再保保全险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保保全险种明细集合
     */
    public List<DwsPrpEdorEntity> selectDwsPrpEdorByIds(Long[] Ids);

    /**
     * 新增再保保全险种明细
     *
     * @param dwsPrpEdorEntity 再保保全险种明细
     * @return 结果
     */
    public int insertDwsPrpEdor(DwsPrpEdorEntity dwsPrpEdorEntity);

    /**
     * 批量新增再保保全险种明细
     *
     * @param dwsPrpEdorList 再保保全险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpEdor(List<DwsPrpEdorEntity> dwsPrpEdorList);

    /**
     * 修改再保保全险种明细
     *
     * @param dwsPrpEdorEntity 再保保全险种明细
     * @return 结果
     */
    public int updateDwsPrpEdor(DwsPrpEdorEntity dwsPrpEdorEntity);

    /**
     * 删除再保保全险种明细
     *
     * @param Id 再保保全险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpEdorById(Long Id);

    /**
     * 批量删除再保保全险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpEdorByIds(Long[] Ids);

    /**
     * 查询再保保全险种明细总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpEdorCountByAccTransNo(String accTransNo);

    /**
     * 查询再保保全险种明细列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保保全险种明细列表
     */
    List<DwsPrpEdorEntity> selectDwsPrpEdorListByAccTransNo(@Param("accTransNo")String accTransNo, @Param("pageSize")int pageSize, @Param("startRows")int startRows);

    /**
     * 删除再保保全险种明细
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpEdorByAccTransNo(String accTransNo);

    /**
     * 批量插入再保保全险种明细数据（已结算）
     * @param account
     * @return
     */
    public int insertDwsPrpEdorFormTrade(DwsPrpAccountDTO account);

    /**
     * 批量插入再保保全险种明细数据（未结算）
     * @param account
     * @return
     */
    public int insertPreDwsPrpEdorFormTrade(DwsPrpAccountDTO account);

    /**
     * 查询再保保全险种明细交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return 再保保全险种明细ID集合
     */
    public List<Long> selectWaitSetTransNoDwsPrpEdorListByAccTransNo(String accTransNo, int limit);

    /**
     * 更新再保保全险种明细推送状态
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param accTransNos 账单交易编码集合
     * @return 结果
     */
    public int updateDwsPrpEdorPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("accTransNos") List<String> accTransNos);
}
