package com.reinsurance.mapper;

import java.util.List;

import com.reinsurance.dto.DwsPrpAccountDTO;
import org.apache.ibatis.annotations.Param;

import com.reinsurance.domain.DwsPrpClaimEntity;
import com.reinsurance.query.DwsPrpClaimQuery;

/**
 * 再保理赔险种明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
public interface DwsPrpClaimMapper {
    
    /**
     * 查询再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 再保理赔险种明细
     */
    public DwsPrpClaimEntity selectDwsPrpClaimById(Long Id);

    /**
     * 查询再保理赔险种明细列表
     *
     * @param dwsPrpClaimQuery 再保理赔险种明细
     * @return 再保理赔险种明细集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimList(DwsPrpClaimQuery dwsPrpClaimQuery);

    /**
     * 根据主键数组查询再保理赔险种明细列表
     *
     * @param Ids 主键数组
     * @return 再保理赔险种明细集合
     */
    public List<DwsPrpClaimEntity> selectDwsPrpClaimByIds(Long[] Ids);

    /**
     * 新增再保理赔险种明细
     *
     * @param dwsPrpClaimEntity 再保理赔险种明细
     * @return 结果
     */
    public int insertDwsPrpClaim(DwsPrpClaimEntity dwsPrpClaimEntity);

    /**
     * 批量新增再保理赔险种明细
     *
     * @param dwsPrpClaimList 再保理赔险种明细列表
     * @return 结果
     */
    public int insertBatchDwsPrpClaim(List<DwsPrpClaimEntity> dwsPrpClaimList);

    /**
     * 修改再保理赔险种明细
     *
     * @param dwsPrpClaimEntity 再保理赔险种明细
     * @return 结果
     */
    public int updateDwsPrpClaim(DwsPrpClaimEntity dwsPrpClaimEntity);

    /**
     * 删除再保理赔险种明细
     *
     * @param Id 再保理赔险种明细主键
     * @return 结果
     */
    public int deleteDwsPrpClaimById(Long Id);

    /**
     * 批量删除再保理赔险种明细
     *
     * @param Ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDwsPrpClaimByIds(Long[] Ids);

    /**
     * 查询再保理赔险种明细总数
     * @param accTransNo 账单交易编码
     * @return
     */
    int selectDwsPrpClaimCountByAccTransNo(String accTransNo);

    /**
     * 查询再保理赔险种明细列表
     * @param accTransNo 账单交易编码
     * @param pageSize
     * @param startRows
     * @return 再保理赔险种明细列表
     */
    List<DwsPrpClaimEntity> selectDwsPrpClaimListByAccTransNo(@Param("accTransNo")String accTransNo, @Param("pageSize")int pageSize, @Param("startRows")int startRows);

    /**
     * 删除再保理赔险种明细
     * @param accTransNo 账单交易编码
     * @return 结果
     */
    public int deleteDwsPrpClaimByAccTransNo(String accTransNo);

    /**
     * 批量插入再保理赔险种明细数据（已结算）
     * @param account
     * @return
     */
    public int insertDwsPrpClaimFormTrade(DwsPrpAccountDTO account);

    /**
     * 批量插入再保理赔险种明细数据（未结算）
     * @param account
     * @return
     */
    public int insertPreDwsPrpClaimFormTrade(DwsPrpAccountDTO account);

    /**
     * 查询再保理赔险种明细交易编码为空的数据
     * @param accTransNo
     * @param limit
     * @return 再保理赔险种明细ID集合
     */
    public List<Long> selectWaitSetTransNoDwsPrpClaimListByAccTransNo(String accTransNo, int limit);

    /**
     * 更新再保理赔险种明细推送状态
     * @param pushStatus 推送状态
     * @param pushBy 推送人
     * @param accTransNos 账单交易编码集合
     * @return 结果
     */
    public int updateDwsPrpClaimPushStatus(@Param("pushStatus") Integer pushStatus, @Param("pushBy") String pushBy, @Param("accTransNos") List<String> accTransNos);
}
