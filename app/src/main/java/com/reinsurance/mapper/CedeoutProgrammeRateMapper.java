package com.reinsurance.mapper;

import java.util.List;
import com.reinsurance.domain.CedeoutProgrammeRateEntity;
import com.reinsurance.query.CedeoutProgrammeRateQuery;

/**
 * 再保分出方案费率Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-19
 */
public interface CedeoutProgrammeRateMapper 
{
    /**
     * 查询再保分出方案费率
     * 
     * @param id 再保分出方案费率主键
     * @return 再保分出方案费率
     */
    CedeoutProgrammeRateEntity selectCedeoutProgrammeRateById(Long id);

    List<CedeoutProgrammeRateEntity> selectCedeoutProgrammeRateListByRateCode(String rateCode);
    /**
     * 查询再保分出方案费率列表
     * 
     * @param cedeoutProgrammeRateQuery 再保分出方案费率
     * @return 再保分出方案费率集合
     */
    List<CedeoutProgrammeRateEntity> selectCedeoutProgrammeRateList(CedeoutProgrammeRateQuery cedeoutProgrammeRateQuery);

    /**
     * 新增再保分出方案费率
     * 
     * @param cedeoutProgrammeRate 再保分出方案费率
     * @return 结果
     */
    int insertCedeoutProgrammeRate(CedeoutProgrammeRateEntity cedeoutProgrammeRate);

    /**
     * 修改再保分出方案费率
     * 
     * @param cedeoutProgrammeRate 再保分出方案费率
     * @return 结果
     */
    int updateCedeoutProgrammeRate(CedeoutProgrammeRateEntity cedeoutProgrammeRate);

    /**
     * 删除再保分出方案费率
     * 
     * @param id 再保分出方案费率主键
     * @return 结果
     */
    int deleteCedeoutProgrammeRateById(Long id);

    int deleteCedeoutProgrammeRateByProgrammeCode(String programmeCode);
    /**
     * 批量删除再保分出方案费率
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteCedeoutProgrammeRateByIds(Long[] ids);
}
